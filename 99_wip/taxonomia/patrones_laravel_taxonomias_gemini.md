# Patrones Idiomáticos de Laravel para Taxonomías y Atributos Dinámicos

Este documento describe un enfoque idiomático, utilizando herramientas nativas de <PERSON>vel, para resolver el desafío de gestionar taxonomías de productos y sus atributos de especificación dinámicos, como se define en los requisitos del proyecto PromoSmart.

---

### 1. Para la Taxonomía Jerárquica (Categoría → Subcategoría)

El problema es representar la relación padre-hijo entre `Category` y `Subcategory`.

*   **Solución Idiomática:** **Relaciones de Eloquent estándar**. Para una jerarquía de dos niveles como la descrita, no se necesita nada más complejo.

*   **Cómo se Aplica a PromoSmart:**
    1.  **Modelo `Category`:**
        ```php
        class Category extends Model
        {
            public function subcategories(): HasMany
            {
                return $this->hasMany(Subcategory::class);
            }
        }
        ```
    2.  **Modelo `Subcategory`:**
        ```php
        class Subcategory extends Model
        {
            public function category(): BelongsTo
            {
                return $this->belongsTo(Category::class);
            }
        }
        ```

*   **Beneficios:**
    *   **Simple y Expresivo:** Es la forma nativa de Laravel. El código es legible y universalmente entendido por cualquier desarrollador del framework.
    *   **Eficiente:** Permite cargar relaciones fácilmente con `Category::with('subcategories')->get()`.

*   **Nota para el Futuro:** Si la jerarquía alguna vez necesitara más de dos niveles (ej. Categoría -> Subcategoría -> Tipo), la práctica idiomática sería adoptar un paquete como **`kalnoy/nested-set`**. Este paquete optimiza las consultas en estructuras de árbol, pero para la necesidad actual, las relaciones estándar son perfectas.

---

### 2. Para las Especificaciones Dinámicas por Subcategoría

Este es el desafío más interesante: ¿cómo manejar que "Textiles" requiere "GSM" y "Merchandising" requiere "Capacidad"?

La solución idiomática en Laravel es una combinación de **columnas JSON** y **validación dinámica en Form Requests**.

#### A) Almacenar las Reglas (La "Plantilla de Especificaciones")

*   **Solución:** Añadir una columna de tipo `JSON` llamada `specification_structure` a la tabla `subcategories`.

*   **Cómo se Aplica a PromoSmart:**
    *   Para la subcategoría "Garments" (Prendas), el JSON contendría:
        ```json
        {
            "gsm": {"type": "number", "label": "Gramaje (GSM)", "required": true},
            "tallas": {"type": "tags", "label": "Tallas Disponibles", "required": true},
            "instrucciones_cuidado": {"type": "textarea", "label": "Instrucciones de Cuidado", "required": false}
        }
        ```
    *   Para la subcategoría "Drinking Items" (Artículos para Beber), contendría:
        ```json
        {
            "capacidad_ml": {"type": "number", "label": "Capacidad (ml)", "required": true},
            "material": {"type": "text", "label": "Material Principal", "required": true},
            "certificacion_bpa_free": {"type": "boolean", "label": "Libre de BPA", "required": false}
        }
        ```

#### B) Almacenar los Valores (Los Datos del `ProductItem`)

*   **Solución:** Añadir una columna de tipo `JSON` llamada `specifications` a la tabla `product_items`.

*   **Cómo se Aplica a PromoSmart:**
    *   Para una polera específica, el JSON contendría:
        ```json
        {
            "gsm": 180,
            "tallas": ["S", "M", "L"],
            "instrucciones_cuidado": "Lavar en frío, no usar secadora."
        }
        ```

#### C) Unirlo Todo con Validación Dinámica (La "Magia" Idiomática)

*   **Solución:** Usar una **`FormRequest`** que construye las reglas de validación dinámicamente.

*   **Cómo se Aplica a PromoSmart:**
    1.  El frontend envía el `subcategory_id` junto con los datos del formulario.
    2.  Se crea una `StoreProductItemRequest` que se inyecta en el controlador.
    3.  Dentro de la `FormRequest`, ocurre la magia:

    ```php
    class StoreProductItemRequest extends FormRequest
    {
        public function rules(): array
        {
            $rules = [
                // Reglas base para el ProductItem
                'subcategory_id' => 'required|exists:subcategories,id',
                'name' => 'required|string|max:255',
            ];

            // Cargar la estructura de la subcategoría seleccionada
            $subcategory = Subcategory::find($this->input('subcategory_id'));
            if (!$subcategory) {
                return $rules;
            }

            // Construir las reglas dinámicamente
            foreach ($subcategory->specification_structure as $key => $structure) {
                $validationPath = 'specifications.' . $key;
                $fieldRules = [];

                if ($structure['required']) {
                    $fieldRules[] = 'required';
                } else {
                    $fieldRules[] = 'nullable';
                }

                if ($structure['type'] === 'number') {
                    $fieldRules[] = 'numeric';
                } else if ($structure['type'] === 'text' || $structure['type'] === 'textarea') {
                    $fieldRules[] = 'string';
                } else if ($structure['type'] === 'tags') {
                    $fieldRules[] = 'array';
                }

                $rules[$validationPath] = $fieldRules;
            }

            return $rules;
        }
    }
    ```

*   **Beneficios de este enfoque combinado:**
    *   **Extremadamente Flexible:** Añadir o modificar las especificaciones de una subcategoría es tan simple como editar un JSON en la base de datos. No se requiere ningún cambio de código ni migración de base de datos.
    *   **Validación Robusta:** La validación sigue siendo manejada por Laravel, de forma centralizada y segura, antes de que la lógica del controlador se ejecute.
    *   **Fuente Única de Verdad:** La `Subcategory` es la fuente de la verdad sobre qué especificaciones son necesarias, manteniendo el sistema coherente.
    *   **Limpio y Organizado:** Cumple perfectamente con el requisito `RF-19` de una manera muy limpia y mantenible.
