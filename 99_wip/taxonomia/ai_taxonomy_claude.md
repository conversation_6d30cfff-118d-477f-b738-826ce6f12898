# Especificación: AI-Assisted Product Specification Extraction

> **Propósito:** Definir la funcionalidad que permite a los analistas describir productos en lenguaje natural y que el sistema genere automáticamente las especificaciones técnicas estructuradas, reduciendo la fricción operativa y mejorando la consistencia de datos.

---

## 1. Visión General

### Problema a Resolver

Los analistas de cotización deben llenar formularios complejos con múltiples campos técnicos específicos por subcategoría de producto. Este proceso es:

- **Lento:** Formularios largos con muchos campos obligatorios
- **Propenso a errores:** Tipeo manual en campos especializados
- **Disruptivo:** Interrumpe el flujo natural de conversación con proveedores
- **Rígido:** Campos predefinidos pueden no cubrir variaciones de productos

### Solución Propuesta

Implementar un **asistente AI** que:

1. **Recibe** descripción en lenguaje natural del producto
2. **Extrae** especificaciones técnicas usando el contexto de la subcategoría
3. **Pre-llena** el formulario estructurado automáticamente
4. **Permite** corrección y refinamiento manual por el analista
5. **Aprende** de las correcciones para mejorar futuras extracciones

---

## 2. Flujo de Usuario

### Flujo Principal: Creación de ProductItem con AI

```mermaid
sequenceDiagram
    participant A as Analista
    participant UI as Interfaz
    participant AI as AI Service
    participant DB as Database
    
    A->>UI: Selecciona subcategoría "Prendas"
    UI->>A: Muestra textarea "Describe el producto"
    A->>UI: Escribe: "Polera algodón 180 GSM, tallas S a XL, cuello V"
    UI->>AI: extract_specifications(text, subcategory_schema)
    AI->>UI: Retorna JSON + confidence
    UI->>A: Muestra formulario pre-llenado
    A->>UI: Corrige/confirma especificaciones
    UI->>DB: Guarda ProductItem con specs finales
    DB->>AI: Retroalimentación para aprendizaje (opcional)
```

### Estados de la Feature

1. **Input Natural:** Analista describe producto libremente
2. **Processing:** AI extrae especificaciones estructuradas
3. **Review:** Formulario pre-llenado mostrado para revisión
4. **Correction:** Analista ajusta campos si es necesario
5. **Confirmation:** Especificaciones finales guardadas

---

## 3. Interfaz de Usuario

### 3.1 Pantalla de Creación de ProductItem (Mejorada)

**Ubicación:** Vista de Proyecto → Pestaña "Ítems" → Botón "Agregar Ítem"

#### Componente: AI Product Description Input

```html
<!-- Paso 1: Selección de Subcategoría (existente) -->
<x-filament::form.field>
    <x-filament::form.label>Subcategoría</x-filament::form.label>
    <x-filament::form.select name="subcategory_id" wire:model.live="subcategoryId">
        <option value="">Seleccionar subcategoría...</option>
        @foreach($subcategories as $subcategory)
            <option value="{{ $subcategory->id }}">{{ $subcategory->name }}</option>
        @endforeach
    </x-filament::form.select>
</x-filament::form.field>

<!-- Paso 2: Descripción Natural (NUEVO) -->
@if($subcategoryId)
<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
    <div class="flex items-start space-x-3">
        <x-heroicon-s-sparkles class="w-5 h-5 text-blue-600 mt-1"/>
        <div class="flex-1">
            <h3 class="text-sm font-medium text-blue-900 mb-2">
                ✨ Describe el producto en tus propias palabras
            </h3>
            <textarea 
                name="product_description" 
                wire:model.defer="productDescription"
                class="w-full border-gray-300 rounded-md"
                rows="3"
                placeholder="Ej: Polera algodón 100%, gramaje 180 GSM, tallas desde S hasta XL, cuello redondo, manga corta, impresión en serigrafía"
            ></textarea>
            <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-gray-500">
                    Incluye material, dimensiones, características técnicas, etc.
                </span>
                <x-filament::button 
                    wire:click="extractSpecifications"
                    size="sm"
                    :disabled="!$productDescription"
                >
                    🪄 Generar Especificaciones
                </x-filament::button>
            </div>
        </div>
    </div>
</div>
@endif
```

#### Componente: AI-Generated Specifications Review

```html
<!-- Paso 3: Especificaciones Extraídas (NUEVO) -->
@if($aiExtracted)
<div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
    <div class="flex items-center justify-between mb-3">
        <h3 class="text-sm font-medium text-green-900 flex items-center">
            <x-heroicon-s-check-circle class="w-4 h-4 mr-2"/>
            Especificaciones Generadas (Confianza: {{ $aiExtracted['confidence'] }}%)
        </h3>
        <div class="flex space-x-2">
            <x-filament::button size="xs" color="success" wire:click="acceptAllSpecs">
                ✅ Aceptar Todo
            </x-filament::button>
            <x-filament::button size="xs" color="gray" wire:click="editManually">
                ✏️ Editar Manualmente
            </x-filament::button>
        </div>
    </div>
    
    <!-- Vista previa de especificaciones extraídas -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
        @foreach($aiExtracted['specifications'] as $field => $value)
        <div class="bg-white rounded p-3 border">
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">
                    {{ $subcategory->getFieldLabel($field) }}
                </span>
                @if($value['confidence'] >= 0.8)
                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Alta</span>
                @elseif($value['confidence'] >= 0.6)
                    <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">Media</span>
                @else
                    <span class="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Baja</span>
                @endif
            </div>
            <div class="mt-1 text-sm text-gray-900">{{ $value['extracted'] }}</div>
            @if($value['confidence'] < 0.8)
                <div class="mt-1 text-xs text-yellow-600">⚠️ Verificar valor</div>
            @endif
        </div>
        @endforeach
    </div>
    
    <!-- Campos sugeridos que no estaban en el esquema -->
    @if(!empty($aiExtracted['suggested_fields']))
    <div class="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
        <h4 class="text-sm font-medium text-blue-900 mb-2">💡 Campos adicionales detectados:</h4>
        <div class="space-y-2">
            @foreach($aiExtracted['suggested_fields'] as $field => $value)
            <div class="flex items-center justify-between text-sm">
                <span class="text-blue-800">{{ $field }}: {{ $value }}</span>
                <x-filament::button size="xs" color="blue" wire:click="addField('{{ $field }}', '{{ $value }}')">
                    + Agregar
                </x-filament::button>
            </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endif
```

#### Componente: Formulario de Especificaciones (Mejorado)

```html
<!-- Paso 4: Formulario Final de Especificaciones -->
<div class="space-y-4">
    <h3 class="text-lg font-medium text-gray-900">Especificaciones Técnicas</h3>
    
    @foreach($subcategory->specification_structure as $fieldKey => $fieldConfig)
    <x-filament::form.field>
        <x-filament::form.label 
            :for="'specifications.' . $fieldKey"
            :required="$fieldConfig['required']"
        >
            {{ $fieldConfig['label'] }}
            @if(isset($aiExtracted['specifications'][$fieldKey]))
                <span class="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    🤖 Auto-completado
                </span>
            @endif
        </x-filament::form.label>
        
        @if($fieldConfig['type'] === 'number')
            <x-filament::form.input 
                type="number"
                :name="'specifications.' . $fieldKey"
                wire:model.defer="specifications.{{ $fieldKey }}"
                :value="$aiExtracted['specifications'][$fieldKey]['extracted'] ?? ''"
            />
        @elseif($fieldConfig['type'] === 'text')
            <x-filament::form.input 
                type="text"
                :name="'specifications.' . $fieldKey"
                wire:model.defer="specifications.{{ $fieldKey }}"
                :value="$aiExtracted['specifications'][$fieldKey]['extracted'] ?? ''"
            />
        @elseif($fieldConfig['type'] === 'textarea')
            <x-filament::form.textarea 
                :name="'specifications.' . $fieldKey"
                wire:model.defer="specifications.{{ $fieldKey }}"
                rows="3"
            >{{ $aiExtracted['specifications'][$fieldKey]['extracted'] ?? '' }}</x-filament::form.textarea>
        @elseif($fieldConfig['type'] === 'array')
            <x-filament::form.tags-input 
                :name="'specifications.' . $fieldKey"
                wire:model.defer="specifications.{{ $fieldKey }}"
                :value="$aiExtracted['specifications'][$fieldKey]['extracted'] ?? []"
            />
        @elseif($fieldConfig['type'] === 'boolean')
            <x-filament::form.checkbox 
                :name="'specifications.' . $fieldKey"
                wire:model.defer="specifications.{{ $fieldKey }}"
                :checked="$aiExtracted['specifications'][$fieldKey]['extracted'] ?? false"
            />
        @endif
        
        @if(isset($fieldConfig['help']))
            <x-filament::form.help>{{ $fieldConfig['help'] }}</x-filament::form.help>
        @endif
    </x-filament::form.field>
    @endforeach
</div>
```

---

## 4. Arquitectura Técnica

### 4.1 Componentes del Sistema

```php
// Service Layer
class SpecificationExtractorService
{
    public function __construct(
        private AIProviderInterface $aiProvider,
        private SpecificationValidator $validator,
        private LearningService $learningService
    ) {}

    public function extractFromText(
        string $description, 
        Subcategory $subcategory,
        ?User $user = null
    ): ExtractionResult {
        // 1. Preparar contexto para AI
        $context = $this->buildContext($subcategory, $user);
        
        // 2. Ejecutar extracción
        $aiResponse = $this->aiProvider->extract($description, $context);
        
        // 3. Validar contra esquema
        $validated = $this->validator->validate($aiResponse, $subcategory->specification_structure);
        
        // 4. Detectar campos adicionales
        $suggestions = $this->findAdditionalFields($description, $subcategory);
        
        return new ExtractionResult(
            specifications: $validated,
            confidence: $aiResponse['confidence'],
            suggestedFields: $suggestions,
            rawResponse: $aiResponse
        );
    }
}

// Value Objects
class ExtractionResult
{
    public function __construct(
        public array $specifications,
        public float $confidence,
        public array $suggestedFields,
        public array $rawResponse
    ) {}
    
    public function hasHighConfidence(): bool 
    {
        return $this->confidence >= 0.8;
    }
    
    public function getFieldConfidence(string $field): float
    {
        return $this->specifications[$field]['confidence'] ?? 0.0;
    }
}

// AI Provider Interface
interface AIProviderInterface
{
    public function extract(string $text, array $context): array;
}

class OpenAIProvider implements AIProviderInterface
{
    public function extract(string $text, array $context): array
    {
        $prompt = $this->buildPrompt($text, $context);
        
        $response = $this->client->chat()->create([
            'model' => 'gpt-4',
            'messages' => [
                ['role' => 'system', 'content' => $this->getSystemPrompt()],
                ['role' => 'user', 'content' => $prompt]
            ],
            'temperature' => 0.1,
            'response_format' => ['type' => 'json_object']
        ]);
        
        return json_decode($response->choices[0]->message->content, true);
    }
    
    private function getSystemPrompt(): string
    {
        return "Eres un experto en productos promocionales. Tu tarea es extraer especificaciones técnicas de descripciones en lenguaje natural y convertirlas a JSON estructurado.

REGLAS:
1. Solo extrae información explícitamente mencionada
2. Usa las unidades estándar del esquema proporcionado
3. Asigna confidence score (0.0-1.0) por campo
4. Si hay ambigüedad, marca confidence < 0.8
5. Sugiere campos importantes detectados que no están en el esquema

FORMATO DE RESPUESTA:
{
    \"specifications\": {
        \"campo1\": {\"extracted\": \"valor\", \"confidence\": 0.95},
        \"campo2\": {\"extracted\": \"valor\", \"confidence\": 0.75}
    },
    \"confidence\": 0.85,
    \"suggested_fields\": {
        \"campo_extra\": \"valor_detectado\"
    }
}";
    }
}
```

### 4.2 Livewire Component

```php
class CreateProductItemWithAI extends Component
{
    public $subcategoryId;
    public $productDescription;
    public $aiExtracted;
    public $specifications = [];
    public $isProcessing = false;
    
    protected $rules = [
        'subcategoryId' => 'required|exists:subcategories,id',
        'productDescription' => 'required|string|min:10',
    ];
    
    public function extractSpecifications()
    {
        $this->validate(['productDescription' => 'required|string|min:10']);
        
        $this->isProcessing = true;
        
        try {
            $subcategory = Subcategory::find($this->subcategoryId);
            
            $result = app(SpecificationExtractorService::class)
                ->extractFromText(
                    $this->productDescription, 
                    $subcategory,
                    auth()->user()
                );
            
            $this->aiExtracted = [
                'specifications' => $result->specifications,
                'confidence' => $result->confidence * 100, // Para mostrar como %
                'suggested_fields' => $result->suggestedFields
            ];
            
            // Pre-llenar especificaciones para el formulario
            foreach ($result->specifications as $field => $data) {
                $this->specifications[$field] = $data['extracted'];
            }
            
        } catch (Exception $e) {
            $this->addError('ai_extraction', 'Error al procesar descripción: ' . $e->getMessage());
        } finally {
            $this->isProcessing = false;
        }
    }
    
    public function acceptAllSpecs()
    {
        // Las especificaciones ya están en $this->specifications
        $this->addSuccess('specs_accepted', 'Especificaciones aceptadas. Puedes realizar ajustes si es necesario.');
    }
    
    public function editManually()
    {
        // Limpiar datos AI para edición manual
        $this->aiExtracted = null;
    }
    
    public function addField(string $field, string $value)
    {
        $this->specifications[$field] = $value;
        
        // Remover de sugerencias
        unset($this->aiExtracted['suggested_fields'][$field]);
    }
    
    public function save()
    {
        // Validación dinámica usando el FormRequest existente
        $validatedData = app(StoreProductItemRequest::class)->validate([
            'subcategory_id' => $this->subcategoryId,
            'specifications' => $this->specifications,
            // ... otros campos del ProductItem
        ]);
        
        // Crear ProductItem
        $productItem = ProductItem::create($validatedData);
        
        // Registrar feedback para aprendizaje
        if ($this->aiExtracted) {
            app(LearningService::class)->recordFeedback(
                originalDescription: $this->productDescription,
                aiExtracted: $this->aiExtracted['specifications'],
                finalSpecifications: $this->specifications,
                subcategory: $subcategory
            );
        }
        
        return redirect()->route('projects.show', $productItem->project_id)
            ->with('success', 'Producto creado exitosamente');
    }
}
```

---

## 5. Configuración y Prompts

### 5.1 Estructura de Prompts por Subcategoría

```json
{
    "prendas": {
        "system_context": "Especialista en textiles y prendas de vestir promocionales",
        "common_terms": {
            "gramaje": ["gsm", "peso", "gr/m2", "gramaje"],
            "material": ["algodón", "poliéster", "mezcla", "viscosa"],
            "tallas": ["xs", "s", "m", "l", "xl", "xxl"]
        },
        "validation_rules": {
            "gsm": {"min": 80, "max": 400, "unit": "gr/m²"},
            "tallas": {"type": "array", "valid_values": ["XS", "S", "M", "L", "XL", "XXL"]}
        }
    },
    "drinking_items": {
        "system_context": "Especialista en artículos para bebidas promocionales",
        "common_terms": {
            "capacidad": ["ml", "cc", "litros", "onzas", "oz"],
            "material": ["plástico", "vidrio", "acero", "cerámica"]
        },
        "validation_rules": {
            "capacidad_ml": {"min": 50, "max": 2000, "unit": "ml"},
            "material": {"type": "enum", "values": ["plástico", "vidrio", "acero", "cerámica"]}
        }
    }
}
```

### 5.2 Prompt Template Dinámico

```php
class PromptBuilder
{
    public function buildExtractionPrompt(string $description, Subcategory $subcategory): string
    {
        $schema = $subcategory->specification_structure;
        $context = $this->getSubcategoryContext($subcategory);
        
        return "DESCRIPCIÓN DEL PRODUCTO:
{$description}

ESQUEMA ESPERADO PARA '{$subcategory->name}':
" . json_encode($schema, JSON_PRETTY_PRINT) . "

CONTEXTO ESPECIALIZADO:
{$context['system_context']}

TÉRMINOS COMUNES EN ESTE DOMINIO:
" . json_encode($context['common_terms'], JSON_PRETTY_PRINT) . "

REGLAS DE VALIDACIÓN:
" . json_encode($context['validation_rules'], JSON_PRETTY_PRINT) . "

INSTRUCCIONES:
1. Extrae SOLO información explícitamente mencionada
2. Normaliza términos usando el diccionario de términos comunes
3. Aplica las reglas de validación para verificar rangos
4. Asigna confidence score basado en claridad de la información
5. Identifica campos adicionales importantes que no están en el esquema

Responde en el formato JSON especificado anteriormente.";
    }
}
```

---

## 6. Métricas y Monitoreo

### 6.1 KPIs de la Feature

| Métrica | Objetivo | Método de Medición |
|:--------|:---------|:-------------------|
| **Adoption Rate** | >70% de ProductItems creados usan AI | Tracking en database |
| **Accuracy Rate** | >85% de campos extraídos sin corrección | Comparación AI vs. final |
| **Time Savings** | 50% reducción en tiempo de creación | Time tracking per user |
| **User Satisfaction** | >4.0/5.0 rating | Survey post-creación |
| **Confidence Correlation** | >90% accuracy cuando confidence >0.8 | Statistical analysis |

### 6.2 Logging y Analytics

```php
class AIExtractionLogger
{
    public function logExtraction(array $data): void
    {
        Log::channel('ai_extraction')->info('AI Extraction Performed', [
            'user_id' => $data['user_id'],
            'subcategory_id' => $data['subcategory_id'],
            'description_length' => strlen($data['description']),
            'fields_extracted' => count($data['extracted_fields']),
            'average_confidence' => $data['average_confidence'],
            'processing_time_ms' => $data['processing_time'],
            'api_cost_usd' => $data['api_cost'],
            'timestamp' => now()
        ]);
    }
    
    public function logFeedback(array $data): void
    {
        Log::channel('ai_learning')->info('User Feedback Recorded', [
            'user_id' => $data['user_id'],
            'extraction_id' => $data['extraction_id'],
            'fields_corrected' => $data['corrected_fields'],
            'fields_added' => $data['added_fields'],
            'overall_satisfaction' => $data['satisfaction_score'],
            'timestamp' => now()
        ]);
    }
}
```

---

## 7. Plan de Implementación

### Fase 1: MVP (3-4 semanas)

**Semana 1-2: Core Infrastructure**
- Implementar `SpecificationExtractorService`
- Configurar OpenAI API integration
- Crear `ExtractionResult` value object
- Tests unitarios para componentes core

**Semana 3-4: UI Implementation**
- Desarrollar Livewire component `CreateProductItemWithAI`
- Implementar UI de descripción natural + revisión
- Integrar con formulario existente de ProductItem
- Tests de integración

**Deliverables:**
- Feature funcional para 2 subcategorías piloto (Prendas, Drinking Items)
- Métricas básicas de uso y accuracy
- Documentación de usuario

### Fase 2: Optimización (4-6 semanas)

**Semana 5-6: Learning System**
- Implementar `LearningService` para feedback
- Sistema de prompts dinámicos por subcategoría
- Análisis de patrones de corrección

**Semana 7-8: Advanced Features**
- Campos sugeridos automáticos
- Confidence-based UI indicators
- Bulk processing para múltiples productos

**Semana 9-10: Polishing**
- Performance optimization
- Error handling robusto
- UX improvements basados en feedback

**Deliverables:**
- Feature completa para todas las subcategorías
- Sistema de aprendizaje funcionando
- Dashboards de métricas

### Fase 3: Escalamiento (6-8 semanas)

**Semana 11-12: Alternative Providers**
- Implementar soporte para modelos locales
- Fallback strategies para disponibilidad
- Cost optimization

**Semana 13-14: Advanced AI Features**
- Image-to-specification extraction
- Multi-language support
- Industry-specific fine-tuning

**Deliverables:**
- Sistema robusto y escalable
- Documentación técnica completa
- Training materials para usuarios

---

## 8. Criterios de Éxito

### Criterios Técnicos

✅ **Funcionalidad Core**
- [ ] AI extrae especificaciones de texto natural
- [ ] Pre-llena formulario con >85% accuracy
- [ ] Permite corrección manual fluida
- [ ] Se integra con validaciones existentes

✅ **Performance**
- [ ] Respuesta AI <3 segundos promedio
- [ ] UI responsive durante processing
- [ ] Manejo graceful de errores de API
- [ ] Fallback a input manual siempre disponible

✅ **Usabilidad**
- [ ] Flujo intuitivo para usuarios no técnicos
- [ ] Feedback visual claro sobre confidence
- [ ] Posibilidad de override completo
- [ ] Help contextual por subcategoría

### Criterios de Negocio

✅ **Adopción**
- [ ] >70% de analistas usan la feature regularmente
- [ ] >50% de ProductItems creados usan AI assistance
- [ ] Reducción en tickets de soporte sobre especificaciones

✅ **Eficiencia**
- [ ] 40-60% reducción en tiempo de creación
- [ ] Menos errores en especificaciones técnicas
- [ ] Mayor consistencia en nomenclatura

✅ **ROI**
- [ ] Tiempo ahorrado > costo de API calls
- [ ] Reducción en re-trabajo por especificaciones incorrectas
- [ ] Mejora en satisfacción de analistas

---

## 9. Riesgos y Contingencias

| Riesgo | Impacto | Probabilidad | Mitigación |
|:-------|:--------|:-------------|:-----------|
| **API downtime/limits** | Alto | Media | Fallback a input manual + rate limiting |
| **Poor extraction accuracy** | Alto | Media | Extensive testing + user feedback loop |
| **High API costs** | Medio | Media | Cost monitoring + efficient prompting |
| **User resistance** | Medio | Baja | Training + optional usage + clear benefits |
| **Data privacy concerns** | Alto | Baja | Local models option + data sanitization |

### Plan de Contingencia

**Si accuracy <70%:**
1. Revisar y mejorar prompts
2. Implementar fine-tuning con datos reales
3. Considerar modelos alternativos

**Si costos exceden presupuesto:**
1. Implementar aggressive caching
2. Migrar a modelo local (Llama/Mistral)
3. Limitar usage por usuario/tiempo

**Si resistencia de usuarios:**
1. Hacer feature completamente opcional
2. Mejorar UX basado en feedback
3. Training sessions focused en beneficios

---

## 10. Consideraciones Futuras

### Roadmap Extendido

**6-12 meses:**
- **Image Recognition:** Extraer specs de fotos de productos
- **Voice Input:** Descripción por voz en móvil
- **Batch Processing:** Múltiples productos desde Excel/CSV
- **Provider Integration:** Auto-sync con catálogos de proveedores

**12+ meses:**
- **Custom Models:** Entrenamiento específico para dominio PromoSmart
- **Predictive Specs:** Sugerir especificaciones basadas en tendencias
- **Quality Scoring:** AI que evalúa calidad de especificaciones
- **Market Intelligence:** Análisis de productos competitivos

### Integración con Otras Features

- **Sourcing:** AI sugiere proveedores basados en especificaciones
- **Pricing:** Estimación automática de costos por especificaciones
- **Quotations:** Generación de descripciones comerciales desde specs técnicas
- **Reporting:** Analytics sobre patrones de especificaciones por cliente/industria