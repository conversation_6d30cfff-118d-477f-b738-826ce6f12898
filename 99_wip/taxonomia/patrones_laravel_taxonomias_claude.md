# Patrones Laravel para Taxonomías y Especificaciones de Productos

> **Propósito:** Documentar patrones idiomáticos específicos de Laravel para manejar taxonomías de productos y especificaciones dinámicas en PromoSmart, aprovechando completamente las capacidades del framework para sistemas de clasificación complejos.

---

## Contexto: Necesidad de Taxonomías Flexibles

PromoSmart maneja múltiples categorías de productos (textiles, drinkware, merchandising) donde cada categoría tiene especificaciones completamente diferentes:

- **Textiles**: Material, GSM, tallas, colores
- **Drinkware**: Material, capacidad, tipo de tapa
- **Merchandising**: Dimensiones, material, funcionalidades

### Desafío Arquitectónico

Necesitamos un sistema que permita:
1. **Agregar nuevas categorías** sin cambios de código
2. **Especificaciones dinámicas** por categoría
3. **Validación automática** según tipo de producto
4. **UI adaptativa** que se ajuste a cada categoría
5. **Performance optimizada** para jerarquías de productos

---

## 1. Nested Set Model con `kalnoy/nestedset`

### Problema
Las jerarquías de categorías (Categoria > Subcategoría) requieren queries complejas y no performantes con parent_id tradicional.

### Solución: Nested Set Pattern

```bash
composer require kalnoy/nestedset
```

```php
use Kalnoy\Nestedset\NodeTrait;

class ProductCategory extends Model 
{
    use NodeTrait;
    
    protected $fillable = ['name', 'slug', 'description'];
    
    // Automáticamente maneja parent_id, _lft, _rgt para nested set
    public function subcategories() 
    {
        return $this->children();
    }
    
    public function getFullPathAttribute(): string 
    {
        return $this->ancestors->pluck('name')->push($this->name)->implode(' > ');
    }
    
    // Queries optimizadas para jerarquías
    public function getAllProductsInBranch() 
    {
        return ProductItem::whereHas('subcategory', function($q) {
            $q->whereDescendantOf($this);
        });
    }
    
    // Scope para obtener solo categorías raíz
    public function scopeRoots($query) 
    {
        return $query->whereIsRoot();
    }
    
    // Método para mover categoría en la jerarquía
    public function moveToCategory(ProductCategory $newParent): void 
    {
        $this->appendToNode($newParent)->save();
    }
}

class ProductSubcategory extends Model 
{
    use NodeTrait;
    
    protected $fillable = ['name', 'slug', 'specification_schema'];
    
    protected $casts = [
        'specification_schema' => 'array'
    ];
    
    public function category() 
    {
        return $this->parent();
    }
    
    public function products() 
    {
        return $this->hasMany(ProductItem::class, 'subcategory_id');
    }
    
    public function getRequiredSpecifications(): array 
    {
        return collect($this->specification_schema)
            ->filter(fn($config) => $config['required'] ?? false)
            ->keys()
            ->toArray();
    }
}
```

### Migration

```php
Schema::create('product_categories', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('slug')->unique();
    $table->text('description')->nullable();
    $table->nestedSet(); // Agrega _lft, _rgt, parent_id automáticamente
    $table->timestamps();
});
```

### Beneficios

- **Queries O(1)**: Obtener todos los descendientes en una query
- **Performance**: Sin recursión para jerarquías profundas
- **Flexibilidad**: Mover nodos fácilmente en la jerarquía

---

## 2. JSON Schema con Laravel Validation

### Problema
Cada subcategoría tiene especificaciones completamente diferentes que deben validarse dinámicamente.

### Solución: Schema Dinámico en JSON

```php
class ProductSubcategory extends Model 
{
    protected $casts = [
        'specification_schema' => 'array'
    ];
    
    // Schema ejemplo para textiles
    public function getDefaultSchema(): array 
    {
        return [
            'material' => [
                'type' => 'enum',
                'required' => true,
                'options' => ['Cotton', 'Polyester', 'Blend'],
                'label' => 'Material',
                'help_text' => 'Tipo de material principal del producto'
            ],
            'gsm' => [
                'type' => 'integer',
                'required' => true,
                'min' => 100,
                'max' => 400,
                'label' => 'GSM (Gramaje)',
                'help_text' => 'Peso del material en gramos por metro cuadrado'
            ],
            'colors' => [
                'type' => 'array',
                'required' => true,
                'min_items' => 1,
                'max_items' => 5,
                'label' => 'Colores disponibles',
                'help_text' => 'Lista de colores disponibles para el producto'
            ],
            'sizes' => [
                'type' => 'enum',
                'required' => false,
                'multiple' => true,
                'options' => ['XS', 'S', 'M', 'L', 'XL', 'XXL'],
                'label' => 'Tallas',
                'help_text' => 'Tallas disponibles (opcional)'
            ],
            'print_area' => [
                'type' => 'object',
                'required' => true,
                'properties' => [
                    'max_width' => ['type' => 'integer', 'min' => 1],
                    'max_height' => ['type' => 'integer', 'min' => 1],
                    'location' => ['type' => 'enum', 'options' => ['Front', 'Back', 'Left Chest']]
                ],
                'label' => 'Área de Impresión'
            ]
        ];
    }
    
    public function generateValidationRules(): array 
    {
        $rules = [];
        
        foreach ($this->specification_schema as $field => $config) {
            $fieldRules = [];
            
            if ($config['required'] ?? false) {
                $fieldRules[] = 'required';
            } else {
                $fieldRules[] = 'nullable';
            }
            
            match($config['type']) {
                'enum' => $this->addEnumRules($fieldRules, $config),
                'integer' => $this->addIntegerRules($fieldRules, $config),
                'array' => $this->addArrayRules($fieldRules, $config),
                'object' => $this->addObjectRules($rules, $field, $config),
                'string' => $fieldRules[] = 'string|max:255',
                'boolean' => $fieldRules[] = 'boolean'
            };
            
            if ($config['type'] !== 'object') {
                $rules["specifications.{$field}"] = $fieldRules;
            }
        }
        
        return $rules;
    }
    
    private function addEnumRules(array &$fieldRules, array $config): void 
    {
        if ($config['multiple'] ?? false) {
            $fieldRules[] = 'array';
            $fieldRules[] = Rule::in($config['options']);
        } else {
            $fieldRules[] = Rule::in($config['options']);
        }
    }
    
    private function addIntegerRules(array &$fieldRules, array $config): void 
    {
        $fieldRules[] = 'integer';
        
        if (isset($config['min'])) {
            $fieldRules[] = "min:{$config['min']}";
        }
        
        if (isset($config['max'])) {
            $fieldRules[] = "max:{$config['max']}";
        }
    }
    
    private function addArrayRules(array &$fieldRules, array $config): void 
    {
        $fieldRules[] = 'array';
        
        if (isset($config['min_items'])) {
            $fieldRules[] = "min:{$config['min_items']}";
        }
        
        if (isset($config['max_items'])) {
            $fieldRules[] = "max:{$config['max_items']}";
        }
    }
    
    private function addObjectRules(array &$rules, string $field, array $config): void 
    {
        foreach ($config['properties'] as $prop => $propConfig) {
            $propRules = ['required'];
            
            match($propConfig['type']) {
                'integer' => $propRules[] = 'integer',
                'enum' => $propRules[] = Rule::in($propConfig['options']),
                'string' => $propRules[] = 'string',
                default => null
            };
            
            $rules["specifications.{$field}.{$prop}"] = $propRules;
        }
    }
}
```

### Uso en FormRequest

```php
class CreateProductItemRequest extends FormRequest 
{
    public function rules(): array 
    {
        $subcategory = ProductSubcategory::find($this->subcategory_id);
        
        $baseRules = [
            'name' => 'required|string|max:255',
            'quantity' => 'required|integer|min:1|max:100000',
            'subcategory_id' => 'required|exists:product_subcategories,id'
        ];
        
        if ($subcategory) {
            $specificationRules = $subcategory->generateValidationRules();
            $baseRules = array_merge($baseRules, $specificationRules);
        }
        
        return $baseRules;
    }
    
    public function messages(): array 
    {
        $subcategory = ProductSubcategory::find($this->subcategory_id);
        $messages = [];
        
        if ($subcategory) {
            foreach ($subcategory->specification_schema as $field => $config) {
                $label = $config['label'] ?? $field;
                $messages["specifications.{$field}.required"] = "El campo {$label} es obligatorio.";
                
                if ($config['type'] === 'enum') {
                    $options = implode(', ', $config['options']);
                    $messages["specifications.{$field}.in"] = "El campo {$label} debe ser uno de: {$options}.";
                }
            }
        }
        
        return $messages;
    }
}
```

---

## 3. Polymorphic Specification Values

### Problema
Almacenar especificaciones complejas con diferentes tipos de datos (strings, integers, arrays, objetos).

### Solución: Tabla Polimórfica Tipada

```php
// Para especificaciones complejas con diferentes tipos de datos
class SpecificationValue extends Model 
{
    protected $fillable = [
        'product_item_id',
        'specification_key',
        'value_type', // string, integer, array, enum, object
        'string_value',
        'integer_value', 
        'array_value',
        'metadata' // Para información adicional del campo
    ];
    
    protected $casts = [
        'array_value' => 'array',
        'metadata' => 'array'
    ];
    
    public function productItem() 
    {
        return $this->belongsTo(ProductItem::class);
    }
    
    public function getValueAttribute() 
    {
        return match($this->value_type) {
            'string', 'enum' => $this->string_value,
            'integer' => $this->integer_value,
            'array' => $this->array_value,
            'object' => $this->array_value, // JSON objects stored as arrays
            'boolean' => (bool) $this->integer_value,
            default => null
        };
    }
    
    public function setValueAttribute($value) 
    {
        if (is_string($value)) {
            $this->value_type = 'string';
            $this->string_value = $value;
        } elseif (is_int($value)) {
            $this->value_type = 'integer';
            $this->integer_value = $value;
        } elseif (is_bool($value)) {
            $this->value_type = 'boolean';
            $this->integer_value = $value ? 1 : 0;
        } elseif (is_array($value)) {
            $this->value_type = 'array';
            $this->array_value = $value;
        }
    }
    
    public function getFormattedValueAttribute(): string 
    {
        $config = $this->metadata['field_config'] ?? [];
        
        return match($this->value_type) {
            'array' => implode(', ', $this->value),
            'object' => $this->formatObjectValue($this->value),
            'integer' => $this->value . ($config['unit'] ?? ''),
            'boolean' => $this->value ? 'Sí' : 'No',
            default => (string) $this->value
        };
    }
    
    private function formatObjectValue(array $object): string 
    {
        $formatted = [];
        foreach ($object as $key => $value) {
            $formatted[] = ucfirst($key) . ': ' . $value;
        }
        return implode(', ', $formatted);
    }
}

class ProductItem extends Model 
{
    public function specificationValues() 
    {
        return $this->hasMany(SpecificationValue::class);
    }
    
    public function getSpecificationsAttribute(): array 
    {
        return $this->specificationValues
            ->pluck('value', 'specification_key')
            ->toArray();
    }
    
    public function setSpecificationsAttribute(array $specs): void 
    {
        // Eliminar especificaciones existentes
        $this->specificationValues()->delete();
        
        // Obtener configuración de la subcategoría
        $schema = $this->subcategory->specification_schema ?? [];
        
        // Crear nuevas especificaciones
        foreach ($specs as $key => $value) {
            $fieldConfig = $schema[$key] ?? [];
            
            $this->specificationValues()->create([
                'specification_key' => $key,
                'value' => $value,
                'metadata' => [
                    'field_config' => $fieldConfig,
                    'created_at' => now()
                ]
            ]);
        }
    }
    
    public function getFormattedSpecificationsAttribute(): array 
    {
        return $this->specificationValues->mapWithKeys(function($spec) {
            return [$spec->specification_key => [
                'value' => $spec->value,
                'formatted_value' => $spec->formatted_value,
                'label' => $spec->metadata['field_config']['label'] ?? $spec->specification_key,
                'type' => $spec->value_type
            ]];
        })->toArray();
    }
}
```

### Migration

```php
Schema::create('specification_values', function (Blueprint $table) {
    $table->id();
    $table->foreignId('product_item_id')->constrained()->onDelete('cascade');
    $table->string('specification_key');
    $table->enum('value_type', ['string', 'integer', 'array', 'enum', 'object', 'boolean']);
    $table->text('string_value')->nullable();
    $table->integer('integer_value')->nullable();
    $table->json('array_value')->nullable();
    $table->json('metadata')->nullable();
    $table->timestamps();
    
    $table->index(['product_item_id', 'specification_key']);
});
```

---

## 4. Attribute Casts para Specifications

### Problema
Necesitamos que las especificaciones se comporten como objetos tipados automáticamente.

### Solución: Custom Cast

```php
class SpecificationsCast implements CastsAttributes 
{
    public function get($model, string $key, $value, array $attributes): ProductSpecifications 
    {
        $data = json_decode($value, true) ?? [];
        $subcategory = $model->subcategory;
        
        return new ProductSpecifications($data, $subcategory);
    }
    
    public function set($model, string $key, $value, array $attributes): string 
    {
        if ($value instanceof ProductSpecifications) {
            return json_encode($value->toArray());
        }
        
        if (is_array($value)) {
            $subcategory = $model->subcategory ?? ProductSubcategory::find($attributes['subcategory_id']);
            $specs = new ProductSpecifications($value, $subcategory);
            return json_encode($specs->toArray());
        }
        
        return json_encode([]);
    }
}

class ProductSpecifications 
{
    public function __construct(
        private array $data,
        private ?ProductSubcategory $subcategory = null
    ) {
        $this->validate();
    }
    
    public function validate(): void 
    {
        if (!$this->subcategory) return;
        
        $schema = $this->subcategory->specification_schema;
        
        foreach ($schema as $field => $config) {
            if (($config['required'] ?? false) && !isset($this->data[$field])) {
                throw new InvalidSpecificationException("Required field '{$field}' is missing");
            }
            
            if (isset($this->data[$field])) {
                $this->validateField($field, $this->data[$field], $config);
            }
        }
    }
    
    private function validateField(string $field, $value, array $config): void 
    {
        match($config['type']) {
            'enum' => $this->validateEnum($field, $value, $config),
            'integer' => $this->validateInteger($field, $value, $config),
            'array' => $this->validateArray($field, $value, $config),
            'object' => $this->validateObject($field, $value, $config),
            default => null
        };
    }
    
    private function validateEnum(string $field, $value, array $config): void 
    {
        $options = $config['options'] ?? [];
        
        if ($config['multiple'] ?? false) {
            if (!is_array($value)) {
                throw new InvalidSpecificationException("Field '{$field}' must be an array");
            }
            
            $invalid = array_diff($value, $options);
            if (!empty($invalid)) {
                throw new InvalidSpecificationException("Invalid options for '{$field}': " . implode(', ', $invalid));
            }
        } else {
            if (!in_array($value, $options)) {
                throw new InvalidSpecificationException("Invalid value for '{$field}': {$value}");
            }
        }
    }
    
    private function validateInteger(string $field, $value, array $config): void 
    {
        if (!is_int($value)) {
            throw new InvalidSpecificationException("Field '{$field}' must be an integer");
        }
        
        if (isset($config['min']) && $value < $config['min']) {
            throw new InvalidSpecificationException("Field '{$field}' must be at least {$config['min']}");
        }
        
        if (isset($config['max']) && $value > $config['max']) {
            throw new InvalidSpecificationException("Field '{$field}' must be at most {$config['max']}");
        }
    }
    
    private function validateArray(string $field, $value, array $config): void 
    {
        if (!is_array($value)) {
            throw new InvalidSpecificationException("Field '{$field}' must be an array");
        }
        
        if (isset($config['min_items']) && count($value) < $config['min_items']) {
            throw new InvalidSpecificationException("Field '{$field}' must have at least {$config['min_items']} items");
        }
        
        if (isset($config['max_items']) && count($value) > $config['max_items']) {
            throw new InvalidSpecificationException("Field '{$field}' must have at most {$config['max_items']} items");
        }
    }
    
    private function validateObject(string $field, $value, array $config): void 
    {
        if (!is_array($value)) {
            throw new InvalidSpecificationException("Field '{$field}' must be an object");
        }
        
        $properties = $config['properties'] ?? [];
        
        foreach ($properties as $prop => $propConfig) {
            if (!isset($value[$prop])) {
                throw new InvalidSpecificationException("Property '{$prop}' is required in '{$field}'");
            }
            
            $this->validateField("{$field}.{$prop}", $value[$prop], $propConfig);
        }
    }
    
    public function get(string $key) 
    {
        return $this->data[$key] ?? null;
    }
    
    public function set(string $key, $value): self 
    {
        $this->data[$key] = $value;
        $this->validate();
        return $this;
    }
    
    public function toArray(): array 
    {
        return $this->data;
    }
    
    public function toFormattedArray(): array 
    {
        if (!$this->subcategory) return $this->data;
        
        $formatted = [];
        $schema = $this->subcategory->specification_schema;
        
        foreach ($this->data as $key => $value) {
            $config = $schema[$key] ?? null;
            $formatted[$key] = [
                'value' => $value,
                'label' => $config['label'] ?? $key,
                'type' => $config['type'] ?? 'string',
                'formatted_value' => $this->formatValue($value, $config),
                'help_text' => $config['help_text'] ?? null
            ];
        }
        
        return $formatted;
    }
    
    private function formatValue($value, ?array $config): string 
    {
        if (!$config) return (string) $value;
        
        return match($config['type']) {
            'array' => is_array($value) ? implode(', ', $value) : (string) $value,
            'object' => $this->formatObjectValue($value),
            'integer' => $value . ($config['unit'] ?? ''),
            'boolean' => $value ? 'Sí' : 'No',
            'enum' => (string) $value,
            default => (string) $value
        };
    }
    
    private function formatObjectValue($value): string 
    {
        if (!is_array($value)) return (string) $value;
        
        $formatted = [];
        foreach ($value as $key => $val) {
            $formatted[] = ucfirst($key) . ': ' . $val;
        }
        return implode(', ', $formatted);
    }
}

// Uso en el modelo
class ProductItem extends Model 
{
    protected $casts = [
        'specifications' => SpecificationsCast::class,
    ];
    
    // Ahora specifications siempre es un objeto ProductSpecifications
    public function getFormattedSpecifications(): array 
    {
        return $this->specifications->toFormattedArray();
    }
}
```

---

## 5. Filament Integration para Taxonomías

### Problema
Crear interfaces administrativas dinámicas que se adapten a cada taxonomía.

### Solución: Filament Forms Dinámicos

```php
class ProductCategoryResource extends Resource 
{
    protected static ?string $model = ProductCategory::class;
    
    public static function form(Form $form): Form 
    {
        return $form->schema([
            TextInput::make('name')
                ->required()
                ->live(onBlur: true)
                ->afterStateUpdated(fn($state, callable $set) => $set('slug', Str::slug($state))),
            
            TextInput::make('slug')
                ->required()
                ->unique(ignoreRecord: true),
            
            Textarea::make('description')
                ->rows(3),
            
            Select::make('parent_id')
                ->relationship('parent', 'name')
                ->searchable()
                ->placeholder('Select parent category (leave empty for root category)'),
            
            Section::make('Specification Schema')
                ->description('Define the specifications that products in this category must have')
                ->schema([
                    Repeater::make('specification_schema')
                        ->schema([
                            Grid::make(2)->schema([
                                TextInput::make('field_name')
                                    ->required()
                                    ->placeholder('e.g., material')
                                    ->helperText('Internal field name (no spaces, lowercase)'),
                                
                                TextInput::make('label')
                                    ->required()
                                    ->placeholder('e.g., Material Type')
                                    ->helperText('Display label for users'),
                            ]),
                            
                            Grid::make(3)->schema([
                                Select::make('type')
                                    ->options([
                                        'string' => 'Text',
                                        'integer' => 'Number',
                                        'enum' => 'Selection',
                                        'array' => 'Multiple Values',
                                        'object' => 'Complex Object',
                                        'boolean' => 'Yes/No'
                                    ])
                                    ->required()
                                    ->live(),
                                
                                Toggle::make('required')
                                    ->default(false),
                                
                                Toggle::make('multiple')
                                    ->visible(fn($get) => $get('type') === 'enum')
                                    ->helperText('Allow multiple selections'),
                            ]),
                            
                            Textarea::make('help_text')
                                ->placeholder('Optional help text for users')
                                ->rows(2),
                            
                            // Configuración específica por tipo
                            TagsInput::make('options')
                                ->visible(fn($get) => $get('type') === 'enum')
                                ->required(fn($get) => $get('type') === 'enum')
                                ->placeholder('Add options...')
                                ->helperText('Available options for selection'),
                            
                            Grid::make(3)->schema([
                                TextInput::make('min')
                                    ->numeric()
                                    ->visible(fn($get) => in_array($get('type'), ['integer', 'array']))
                                    ->helperText(fn($get) => $get('type') === 'integer' ? 'Minimum value' : 'Minimum items'),
                                
                                TextInput::make('max')
                                    ->numeric()
                                    ->visible(fn($get) => in_array($get('type'), ['integer', 'array']))
                                    ->helperText(fn($get) => $get('type') === 'integer' ? 'Maximum value' : 'Maximum items'),
                                
                                TextInput::make('unit')
                                    ->visible(fn($get) => $get('type') === 'integer')
                                    ->placeholder('e.g., cm, g, ml')
                                    ->helperText('Unit of measurement'),
                            ]),
                            
                            // Para objetos complejos
                            Repeater::make('properties')
                                ->visible(fn($get) => $get('type') === 'object')
                                ->schema([
                                    TextInput::make('name')->required(),
                                    Select::make('type')
                                        ->options(['string' => 'Text', 'integer' => 'Number', 'enum' => 'Selection'])
                                        ->required(),
                                    TagsInput::make('options')
                                        ->visible(fn($get) => $get('type') === 'enum'),
                                ])
                                ->collapsible()
                                ->itemLabel(fn($state) => $state['name'] ?? 'Property'),
                        ])
                        ->collapsible()
                        ->itemLabel(fn($state) => $state['label'] ?? $state['field_name'] ?? 'Specification')
                        ->addActionLabel('Add Specification')
                        ->reorderableWithButtons()
                        ->cloneable(),
                ])
                ->collapsible()
        ]);
    }
    
    public static function table(Table $table): Table 
    {
        return $table->columns([
            TextColumn::make('name')
                ->searchable()
                ->sortable(),
            
            TextColumn::make('full_path')
                ->label('Hierarchy')
                ->getStateUsing(fn($record) => $record->ancestors->pluck('name')->push($record->name)->implode(' > '))
                ->wrap(),
            
            TextColumn::make('products_count')
                ->label('Products')
                ->counts('products')
                ->sortable(),
            
            TextColumn::make('specification_fields_count')
                ->label('Spec Fields')
                ->getStateUsing(fn($record) => count($record->specification_schema ?? []))
                ->alignCenter(),
            
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ])
        ->defaultSort('name')
        ->actions([
            Tables\Actions\ViewAction::make(),
            Tables\Actions\EditAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
            ]),
        ]);
    }
}

class ProductItemResource extends Resource 
{
    protected static ?string $model = ProductItem::class;
    
    public static function form(Form $form): Form 
    {
        return $form->schema([
            Section::make('Basic Information')
                ->schema([
                    TextInput::make('name')
                        ->required()
                        ->maxLength(255),
                    
                    Select::make('subcategory_id')
                        ->relationship('subcategory', 'name')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function($state, callable $set) {
                            // Limpiar especificaciones cuando cambia la subcategoría
                            $set('specifications', []);
                        }),
                    
                    TextInput::make('quantity')
                        ->numeric()
                        ->required()
                        ->minValue(1)
                        ->maxValue(100000),
                ]),
            
            Section::make('Specifications')
                ->description(fn($get) => $get('subcategory_id') 
                    ? 'Configure the specifications for this product based on its category'
                    : 'Select a subcategory first to configure specifications')
                ->schema(fn($get) => static::getSpecificationFields($get('subcategory_id')))
                ->visible(fn($get) => $get('subcategory_id'))
                ->collapsible(),
        ]);
    }
    
    protected static function getSpecificationFields(?int $subcategoryId): array 
    {
        if (!$subcategoryId) return [];
        
        $subcategory = ProductSubcategory::find($subcategoryId);
        if (!$subcategory || !$subcategory->specification_schema) {
            return [
                Placeholder::make('no_specs')
                    ->content('This subcategory has no specification schema defined.')
            ];
        }
        
        $fields = [];
        
        foreach ($subcategory->specification_schema as $fieldName => $config) {
            $field = match($config['type']) {
                'string' => TextInput::make("specifications.{$fieldName}")
                    ->label($config['label'] ?? $fieldName)
                    ->helperText($config['help_text'] ?? null)
                    ->required($config['required'] ?? false)
                    ->maxLength(255),
                
                'integer' => TextInput::make("specifications.{$fieldName}")
                    ->label($config['label'] ?? $fieldName)
                    ->helperText($config['help_text'] ?? null)
                    ->numeric()
                    ->minValue($config['min'] ?? null)
                    ->maxValue($config['max'] ?? null)
                    ->suffix($config['unit'] ?? null)
                    ->required($config['required'] ?? false),
                
                'enum' => Select::make("specifications.{$fieldName}")
                    ->label($config['label'] ?? $fieldName)
                    ->helperText($config['help_text'] ?? null)
                    ->options(array_combine($config['options'] ?? [], $config['options'] ?? []))
                    ->multiple($config['multiple'] ?? false)
                    ->required($config['required'] ?? false),
                
                'array' => TagsInput::make("specifications.{$fieldName}")
                    ->label($config['label'] ?? $fieldName)
                    ->helperText($config['help_text'] ?? null)
                    ->required($config['required'] ?? false)
                    ->suggestions($config['suggestions'] ?? []),
                
                'boolean' => Toggle::make("specifications.{$fieldName}")
                    ->label($config['label'] ?? $fieldName)
                    ->helperText($config['help_text'] ?? null),
                
                'object' => static::createObjectField($fieldName, $config),
                
                default => TextInput::make("specifications.{$fieldName}")
                    ->label($config['label'] ?? $fieldName)
                    ->helperText($config['help_text'] ?? null)
                    ->required($config['required'] ?? false)
            };
            
            $fields[] = $field;
        }
        
        return $fields;
    }
    
    protected static function createObjectField(string $fieldName, array $config): Group 
    {
        $properties = $config['properties'] ?? [];
        $propertyFields = [];
        
        foreach ($properties as $propName => $propConfig) {
            $propertyFields[] = match($propConfig['type']) {
                'integer' => TextInput::make("specifications.{$fieldName}.{$propName}")
                    ->label($propConfig['label'] ?? ucfirst($propName))
                    ->numeric()
                    ->required(),
                
                'enum' => Select::make("specifications.{$fieldName}.{$propName}")
                    ->label($propConfig['label'] ?? ucfirst($propName))
                    ->options(array_combine($propConfig['options'] ?? [], $propConfig['options'] ?? []))
                    ->required(),
                
                default => TextInput::make("specifications.{$fieldName}.{$propName}")
                    ->label($propConfig['label'] ?? ucfirst($propName))
                    ->required()
            };
        }
        
        return Group::make($propertyFields)
            ->label($config['label'] ?? $fieldName)
            ->columns(2);
    }
    
    public static function table(Table $table): Table 
    {
        return $table->columns([
            TextColumn::make('name')
                ->searchable()
                ->sortable(),
            
            TextColumn::make('subcategory.name')
                ->label('Subcategory')
                ->searchable()
                ->sortable(),
            
            TextColumn::make('subcategory.category.name')
                ->label('Category')
                ->searchable()
                ->sortable(),
            
            TextColumn::make('quantity')
                ->numeric()
                ->sortable(),
            
            TextColumn::make('status')
                ->badge(),
            
            TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ])
        ->filters([
            SelectFilter::make('subcategory')
                ->relationship('subcategory', 'name')
                ->searchable()
                ->preload(),
            
            SelectFilter::make('category')
                ->options(fn() => ProductCategory::pluck('name', 'id'))
                ->query(function($query, array $data) {
                    if ($data['value']) {
                        $query->whereHas('subcategory.category', fn($q) => $q->where('id', $data['value']));
                    }
                }),
        ])
        ->actions([
            Tables\Actions\ViewAction::make(),
            Tables\Actions\EditAction::make(),
        ]);
    }
    
    public static function infolist(Infolist $infolist): Infolist 
    {
        return $infolist->schema([
            Section::make('Product Information')
                ->schema([
                    TextEntry::make('name'),
                    TextEntry::make('subcategory.full_path')->label('Category'),
                    TextEntry::make('quantity'),
                    TextEntry::make('status')->badge(),
                ]),
            
            Section::make('Specifications')
                ->schema(function($record) {
                    $specs = $record->formatted_specifications ?? [];
                    $entries = [];
                    
                    foreach ($specs as $key => $spec) {
                        $entries[] = TextEntry::make("specifications.{$key}")
                            ->label($spec['label'] ?? $key)
                            ->getStateUsing(fn() => $spec['formatted_value'])
                            ->helperText($spec['help_text'] ?? null);
                    }
                    
                    return $entries;
                })
                ->columns(2),
        ]);
    }
}
```

---

## 6. Validation Rule para Taxonomías

### Problema
Validar que los schemas de especificaciones sean correctos y consistentes.

### Solución: Custom Validation Rules

```php
class ValidSpecificationSchema implements Rule 
{
    private array $errors = [];
    
    public function passes($attribute, $value): bool 
    {
        if (!is_array($value)) {
            $this->errors[] = 'Schema must be an array';
            return false;
        }
        
        foreach ($value as $fieldName => $fieldConfig) {
            if (!$this->isValidField($fieldName, $fieldConfig)) {
                return false;
            }
        }
        
        return empty($this->errors);
    }
    
    private function isValidField(string $fieldName, array $field): bool 
    {
        // Validar campos requeridos
        $required = ['field_name', 'label', 'type'];
        $missing = array_diff($required, array_keys($field));
        
        if (!empty($missing)) {
            $this->errors[] = "Field '{$fieldName}' is missing required properties: " . implode(', ', $missing);
            return false;
        }
        
        // Validar tipo
        $validTypes = ['string', 'integer', 'enum', 'array', 'object', 'boolean'];
        if (!in_array($field['type'], $validTypes)) {
            $this->errors[] = "Field '{$fieldName}' has invalid type '{$field['type']}'";
            return false;
        }
        
        // Validaciones específicas por tipo
        return match($field['type']) {
            'enum' => $this->validateEnumField($fieldName, $field),
            'integer' => $this->validateIntegerField($fieldName, $field),
            'array' => $this->validateArrayField($fieldName, $field),
            'object' => $this->validateObjectField($fieldName, $field),
            default => true
        };
    }
    
    private function validateEnumField(string $fieldName, array $field): bool 
    {
        if (empty($field['options']) || !is_array($field['options'])) {
            $this->errors[] = "Enum field '{$fieldName}' must have options array";
            return false;
        }
        
        if (count($field['options']) < 1) {
            $this->errors[] = "Enum field '{$fieldName}' must have at least one option";
            return false;
        }
        
        return true;
    }
    
    private function validateIntegerField(string $fieldName, array $field): bool 
    {
        if (isset($field['min'], $field['max']) && $field['min'] > $field['max']) {
            $this->errors[] = "Integer field '{$fieldName}' min value cannot be greater than max value";
            return false;
        }
        
        return true;
    }
    
    private function validateArrayField(string $fieldName, array $field): bool 
    {
        if (isset($field['min_items'], $field['max_items']) && $field['min_items'] > $field['max_items']) {
            $this->errors[] = "Array field '{$fieldName}' min_items cannot be greater than max_items";
            return false;
        }
        
        if (isset($field['min_items']) && $field['min_items'] < 0) {
            $this->errors[] = "Array field '{$fieldName}' min_items cannot be negative";
            return false;
        }
        
        return true;
    }
    
    private function validateObjectField(string $fieldName, array $field): bool 
    {
        if (empty($field['properties']) || !is_array($field['properties'])) {
            $this->errors[] = "Object field '{$fieldName}' must have properties array";
            return false;
        }
        
        foreach ($field['properties'] as $propName => $propConfig) {
            if (!isset($propConfig['type'])) {
                $this->errors[] = "Property '{$propName}' in object field '{$fieldName}' must have type";
                return false;
            }
        }
        
        return true;
    }
    
    public function message(): string 
    {
        return 'The specification schema is invalid: ' . implode('; ', $this->errors);
    }
}

class ValidProductSpecificationsForCategory implements Rule 
{
    public function __construct(
        private ProductSubcategory $subcategory
    ) {}
    
    public function passes($attribute, $value): bool 
    {
        if (!is_array($value)) return false;
        
        try {
            $specs = new ProductSpecifications($value, $this->subcategory);
            return true;
        } catch (InvalidSpecificationException $e) {
            return false;
        }
    }
    
    public function message(): string 
    {
        return 'The specifications do not match the requirements for this product category.';
    }
}
```

---

## 7. Seeder para Taxonomías

### Problema
Necesitamos datos de prueba realistas para el desarrollo y testing.

### Solución: Seeder Estructurado

```php
class ProductTaxonomySeeder extends Seeder 
{
    public function run() 
    {
        $taxonomies = $this->getTaxonomyDefinitions();
        
        foreach ($taxonomies as $categoryName => $categoryData) {
            $category = ProductCategory::create([
                'name' => $categoryName,
                'slug' => Str::slug($categoryName),
                'description' => $categoryData['description'] ?? null
            ]);
            
            foreach ($categoryData['subcategories'] as $subName => $subData) {
                $subcategory = $category->children()->create([
                    'name' => $subName,
                    'slug' => Str::slug($subName),
                    'description' => $subData['description'] ?? null,
                    'specification_schema' => $subData['schema']
                ]);
                
                // Crear productos de ejemplo
                if ($subData['sample_products'] ?? false) {
                    $this->createSampleProducts($subcategory, $subData['sample_products']);
                }
            }
        }
    }
    
    private function getTaxonomyDefinitions(): array 
    {
        return [
            'Textiles' => [
                'description' => 'Clothing and fabric-based promotional products',
                'subcategories' => [
                    'T-Shirts' => [
                        'description' => 'Custom printed and embroidered t-shirts',
                        'schema' => [
                            'material' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['100% Cotton', 'Polyester', '50/50 Cotton/Poly Blend', 'Tri-Blend'],
                                'label' => 'Material',
                                'help_text' => 'Base material composition of the t-shirt'
                            ],
                            'gsm' => [
                                'type' => 'integer',
                                'required' => true,
                                'min' => 120,
                                'max' => 200,
                                'unit' => 'g/m²',
                                'label' => 'GSM (Fabric Weight)',
                                'help_text' => 'Grams per square meter - indicates fabric thickness'
                            ],
                            'sizes' => [
                                'type' => 'enum',
                                'required' => true,
                                'multiple' => true,
                                'options' => ['XS', 'S', 'M', 'L', 'XL', 'XXL', '3XL'],
                                'label' => 'Available Sizes',
                                'help_text' => 'Sizes that will be available for this product'
                            ],
                            'colors' => [
                                'type' => 'array',
                                'required' => true,
                                'min_items' => 1,
                                'max_items' => 8,
                                'label' => 'Available Colors',
                                'help_text' => 'List of available colors'
                            ],
                            'print_area' => [
                                'type' => 'object',
                                'required' => true,
                                'properties' => [
                                    'max_width' => ['type' => 'integer', 'label' => 'Max Width (cm)'],
                                    'max_height' => ['type' => 'integer', 'label' => 'Max Height (cm)'],
                                    'location' => [
                                        'type' => 'enum', 
                                        'options' => ['Front Center', 'Back Center', 'Left Chest', 'Right Chest', 'Sleeve'],
                                        'label' => 'Print Location'
                                    ]
                                ],
                                'label' => 'Print Area Specifications'
                            ]
                        ],
                        'sample_products' => [
                            [
                                'name' => 'Classic Cotton Tee',
                                'specifications' => [
                                    'material' => '100% Cotton',
                                    'gsm' => 180,
                                    'sizes' => ['S', 'M', 'L', 'XL'],
                                    'colors' => ['White', 'Black', 'Navy', 'Red'],
                                    'print_area' => [
                                        'max_width' => 25,
                                        'max_height' => 30,
                                        'location' => 'Front Center'
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'Hoodies' => [
                        'description' => 'Custom hooded sweatshirts',
                        'schema' => [
                            'material' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['Cotton Fleece', 'Polyester Fleece', 'Cotton/Poly Blend'],
                                'label' => 'Material'
                            ],
                            'hood_type' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['Standard', 'Lined', 'Double Layer'],
                                'label' => 'Hood Type'
                            ],
                            'zipper' => [
                                'type' => 'boolean',
                                'required' => false,
                                'label' => 'Has Zipper'
                            ],
                            'pocket' => [
                                'type' => 'enum',
                                'required' => false,
                                'options' => ['None', 'Kangaroo Pocket', 'Side Pockets'],
                                'label' => 'Pocket Type'
                            ]
                        ]
                    ]
                ]
            ],
            'Drinkware' => [
                'description' => 'Mugs, bottles, and drinking accessories',
                'subcategories' => [
                    'Mugs' => [
                        'description' => 'Ceramic and stainless steel mugs',
                        'schema' => [
                            'material' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['Ceramic', 'Stainless Steel', 'Glass', 'Enamel'],
                                'label' => 'Material'
                            ],
                            'capacity_ml' => [
                                'type' => 'integer',
                                'required' => true,
                                'min' => 200,
                                'max' => 500,
                                'unit' => 'ml',
                                'label' => 'Capacity'
                            ],
                            'handle_type' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['Standard', 'Large Handle', 'No Handle'],
                                'label' => 'Handle Type'
                            ],
                            'dishwasher_safe' => [
                                'type' => 'boolean',
                                'required' => false,
                                'label' => 'Dishwasher Safe'
                            ]
                        ]
                    ],
                    'Water Bottles' => [
                        'description' => 'Reusable water bottles',
                        'schema' => [
                            'material' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['Stainless Steel', 'BPA-Free Plastic', 'Glass', 'Aluminum'],
                                'label' => 'Material'
                            ],
                            'capacity_ml' => [
                                'type' => 'integer',
                                'required' => true,
                                'min' => 300,
                                'max' => 1000,
                                'unit' => 'ml',
                                'label' => 'Capacity'
                            ],
                            'lid_type' => [
                                'type' => 'enum',
                                'required' => true,
                                'options' => ['Screw-on', 'Flip-top', 'Straw Lid', 'Sports Cap'],
                                'label' => 'Lid Type'
                            ],
                            'insulated' => [
                                'type' => 'boolean',
                                'required' => false,
                                'label' => 'Double-wall Insulated'
                            ]
                        ]
                    ]
                ]
            ],
            'Tech Accessories' => [
                'description' => 'Electronic accessories and gadgets',
                'subcategories' => [
                    'Power Banks' => [
                        'schema' => [
                            'capacity_mah' => [
                                'type' => 'integer',
                                'required' => true,
                                'min' => 2000,
                                'max' => 20000,
                                'unit' => 'mAh',
                                'label' => 'Battery Capacity'
                            ],
                            'output_ports' => [
                                'type' => 'array',
                                'required' => true,
                                'label' => 'Output Port Types'
                            ],
                            'wireless_charging' => [
                                'type' => 'boolean',
                                'required' => false,
                                'label' => 'Wireless Charging Support'
                            ]
                        ]
                    ]
                ]
            ]
        ];
    }
    
    private function createSampleProducts(ProductSubcategory $subcategory, array $sampleProducts): void 
    {
        foreach ($sampleProducts as $productData) {
            ProductItem::factory()->create([
                'name' => $productData['name'],
                'subcategory_id' => $subcategory->id,
                'specifications' => $productData['specifications'],
                'status' => 'DRAFT'
            ]);
        }
    }
}
```

---

## Beneficios Específicos para PromoSmart

### 1. **Flexibilidad Total**
- **Agregar categorías**: Sin cambios de código, solo configuración
- **Nuevas especificaciones**: Dinámicas por categoría
- **Evolución**: Sistema se adapta al crecimiento del negocio

### 2. **Performance Optimizada**  
- **Nested Sets**: Queries jerárquicas O(1)
- **JSON Schema**: Validación rápida en memoria
- **Lazy Loading**: Especificaciones solo cuando necesarias

### 3. **UI Automática**
- **Filament dinámico**: Formularios se generan automáticamente
- **Validación frontend**: Inmediata según schema
- **Consistencia**: Misma interfaz para todas las categorías

### 4. **Mantenibilidad**
- **Schema centralizado**: Una fuente de verdad
- **Type safety**: Value Objects garantizan consistencia
- **Testing**: Factories generan datos realistas

### 5. **Escalabilidad**
- **Multi-tenant**: Diferentes schemas por cliente
- **Versionado**: Schemas pueden evolucionar
- **Performance**: Optimizado para miles de productos

---

## Implementación Recomendada

### Fase 1: Base (2-3 semanas)
1. **Nested Set Model** - Jerarquías eficientes
2. **JSON Schema** - Especificaciones dinámicas  
3. **Custom Cast** - Value Objects automáticos

### Fase 2: Validación (1-2 semanas)
4. **Validation Rules** - Reglas dinámicas
5. **Exception Handling** - Errores claros

### Fase 3: UI (2-3 semanas)
6. **Filament Integration** - Interfaces dinámicas
7. **Schema Builder** - Editor de taxonomías

### Fase 4: Data (1 semana)
8. **Seeders** - Datos de ejemplo
9. **Factories** - Testing automatizado

### ROI Estimado
- **6-8 semanas** inversión inicial
- **-80% tiempo** para agregar nuevas categorías  
- **-60% bugs** de validación
- **+100% flexibilidad** del sistema

Esta aproximación es **significativamente más potente** que schemas estáticos y aprovecha completamente las capacidades avanzadas de Laravel para sistemas complejos de clasificación. 🎯