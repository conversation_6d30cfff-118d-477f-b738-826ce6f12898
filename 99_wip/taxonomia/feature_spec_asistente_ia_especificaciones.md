# Especificación de Funcionalidad: Asistente IA para Generación de Especificaciones

**ID de Feature:** `F-AI-01`

**Autor:** Gemini

**Versión:** 1.0

**Fecha:** 21 de agosto de 2025

---

### 1. Resumen Ejecutivo

Esta funcionalidad introduce un asistente basado en un Modelo Lingüístico Grande (LLM) que interpreta una descripción de producto en lenguaje natural, proporcionada por un analista, y la utiliza para pre-rellenar automáticamente el formulario de especificaciones técnicas estructuradas. El objetivo es acelerar la entrada de datos, reducir errores y mejorar la experiencia de usuario.

### 2. Justificación de Negocio

El proceso de cotización es central para el negocio de PromoSmart. La velocidad y precisión en esta etapa impactan directamente en la satisfacción del cliente y la rentabilidad.

*   **Problema:** La entrada manual de especificaciones detalladas para cada producto es un proceso lento, repetitivo y propenso a errores de tipeo o selección.
*   **Solución:** Un asistente de IA reduce drásticamente el tiempo de entrada de datos, permitiendo a los analistas generar cotizaciones más rápido.
*   **Métricas de Éxito Impactadas:**
    *   `Tiempo de Generación de Cotizaciones`: Se espera una reducción significativa.
    *   `Precisión de Datos`: El LLM puede normalizar la entrada y reducir errores.
    *   `Tasa de Adopción de Usuarios`: Una herramienta potente y moderna mejora la satisfacción y adopción del sistema por parte del equipo.

### 3. Historia de Usuario

**Como** un Analista de Cotizaciones,
**Quiero** describir las características de un producto usando texto libre y conversacional,
**Para que** el sistema rellene automáticamente el formulario de especificaciones técnicas, permitiéndome trabajar más rápido y enfocarme en los detalles comerciales en lugar de la entrada de datos.

### 4. Alcance y Requisitos Funcionales

**RF-AI-01: Interfaz de Entrada de Texto Natural**
*   Al seleccionar una subcategoría de producto, el sistema debe mostrar un campo de texto (ej. `textarea`) para que el usuario ingrese la descripción del producto.
*   Un botón (ej. "Generar Especificaciones") debe estar presente para iniciar el proceso.

**RF-AI-02: Orquestación de la Petición al LLM**
*   Al presionar el botón, el backend debe construir un "prompt" para el servicio de LLM.
*   Este prompt debe incluir:
    1.  El texto ingresado por el usuario.
    2.  La estructura de especificaciones (`specification_structure`) de la subcategoría seleccionada, para que el LLM conozca los campos que debe generar y sus posibles valores.
    3.  Instrucciones claras para que la salida sea un objeto JSON válido que se ajuste a la estructura requerida.

**RF-AI-03: Integración con el Servicio de IA**
*   El sistema debe realizar una llamada API segura y autenticada a un servicio de LLM externo (ej. Google AI, OpenAI).
*   Las claves de la API deben ser gestionadas de forma segura a través del sistema de configuración de Laravel.

**RF-AI-04: Procesamiento de la Respuesta y Pre-llenado del Formulario**
*   El sistema debe recibir la respuesta del LLM y validar que sea un JSON bien formado.
*   El frontend debe usar los datos del JSON para rellenar los valores de los campos correspondientes en el formulario de especificaciones dinámico.
*   Si el LLM devuelve un valor que no es válido para un campo (ej. una opción incorrecta para un `select`), ese campo debe permanecer vacío.

**RF-AI-05: Flujo de Confirmación del Usuario**
*   El usuario **siempre** debe tener la capacidad de revisar, editar y corregir cualquier dato pre-rellenado por la IA.
*   El envío final del formulario sigue el flujo de guardado y validación estándar, tratando los datos pre-rellenados como si hubieran sido ingresados manualmente.

**RF-AI-06: Manejo de Errores**
*   Si la llamada a la API del LLM falla (timeout, error 500, etc.), el sistema debe mostrar una notificación no bloqueante al usuario (ej. "El asistente IA no está disponible en este momento, por favor complete el formulario manualmente").
*   El formulario manual debe permanecer 100% funcional en todo momento, independientemente del estado del asistente de IA.

### 5. Flujo de Usuario Detallado

1.  El Analista selecciona una Subcategoría (ej. "Poleras").
2.  Aparece el formulario dinámico de especificaciones para "Poleras" y, adicionalmente, un campo de texto para el asistente de IA.
3.  El Analista escribe en el campo de texto: `polera de algodón 180g, tallas S a XL, para hombre` y presiona "Generar".
4.  La interfaz muestra un indicador de carga (ej. `spinner`).
5.  El backend envía la petición al LLM.
6.  El LLM responde con un JSON: `{"material": "Algodón", "gsm": 180, "tallas": ["S", "M", "L", "XL"], "corte": "Hombre"}`.
7.  El frontend recibe el JSON y rellena los campos correspondientes del formulario.
8.  El Analista revisa los datos, quizás cambia el `material` a `Algodón Orgánico`, y presiona "Guardar Producto".

### 6. Requisitos No Funcionales

*   **Rendimiento:** El tiempo total desde que el usuario presiona "Generar" hasta que el formulario está poblado no debe exceder los 7 segundos para mantener una buena experiencia de usuario.
*   **Costos:** Se debe implementar un sistema de monitoreo y logging para rastrear el número de llamadas a la API y estimar los costos operativos de esta funcionalidad.
*   **Dependencia:** El sistema debe degradarse con elegancia. La indisponibilidad del servicio de IA no debe impedir ni afectar la capacidad de los usuarios para completar el formulario de forma manual.

### 7. Criterios de Aceptación

*   **Dado** que un usuario selecciona la subcategoría "Poleras" y escribe "polera de algodón 180g", **cuando** presiona "Generar", **entonces** el campo "Material" se rellena con la opción correcta para "Algodón" y el campo "GSM" se rellena con el valor `180`.
*   **Dado** que el usuario genera especificaciones con la IA, **cuando** modifica manualmente un campo y guarda, **entonces** el valor modificado por el usuario es el que se guarda en la base de datos.
*   **Dado** que la API del LLM devuelve un error o un JSON malformado, **cuando** el usuario presiona "Generar", **entonces** se muestra un mensaje de error y el formulario permanece sin cambios.
*   **Dado** que la API del LLM no está disponible, **cuando** el usuario selecciona una subcategoría, **entonces** el campo de texto del asistente de IA no aparece o se muestra como deshabilitado.

### 8. Consideraciones de UI/UX

*   Se debe mostrar un indicador de carga claro mientras el LLM procesa la petición.
*   Los campos rellenados por la IA podrían destacarse sutilmente por unos segundos (ej. con un breve parpadeo de fondo de color) para que el usuario note qué ha cambiado.
*   Incluir un `tooltip` o un pequeño texto de ayuda que explique la naturaleza experimental de la funcionalidad y la necesidad de revisar los datos.

### 9. Fuera de Alcance (Para esta Versión)

*   **Automatización Completa:** El sistema no guardará los datos sin la confirmación explícita del usuario.
*   **Conversaciones o Refinamientos:** El asistente no mantendrá una conversación. Es una interacción de una sola vez por cada generación.
*   **Generación de Contenido No Estructurado:** El asistente no generará descripciones de marketing ni otro contenido creativo; su único propósito es rellenar el formulario de especificaciones técnicas.
