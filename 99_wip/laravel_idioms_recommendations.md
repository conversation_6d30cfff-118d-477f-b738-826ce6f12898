# Laravel Idioms: Recomendaciones para PromoSmart

> **Propósito:** Identificar y documentar usos idiomáticos de Laravel que no estamos considerando en la arquitectura actual de PromoSmart, pero que proporcionarían beneficios significativos en terms de mantenibilidad, testabilidad y adherencia a las convenciones del framework.

---

## Resumen Ejecutivo

Después de revisar la documentación de PromoSmart, identificamos **12 patrones idiomáticos de Laravel** que no están siendo considerados en el diseño actual. Estos patrones pueden **reducir significativamente la complejidad del código**, **mejorar la experiencia del desarrollador** y **aprovechar mejor el ecosistema Laravel**.

### Impacto Estimado

| Patrón | Complejidad Reducida | Mantenibilidad | Testabilidad | Prioridad |
|--------|---------------------|----------------|--------------|-----------|
| **Eloquent Accessors/Mutators** | 🟢 Alta | 🟢 Alta | 🟢 Alta | 🔴 Alta |
| **Model Events** | 🟢 Alta | 🟢 Alta | 🟡 Media | 🔴 Alta |
| **Custom Casts** | 🟡 Media | 🟢 Alta | 🟢 Alta | 🔴 Alta |
| **Eloquent Scopes** | 🟢 Alta | 🟢 Alta | 🟢 Alta | 🟡 Media |
| **Laravel Queues** | 🟡 Media | 🟢 Alta | 🟡 Media | 🟡 Media |
| **Notifications** | 🟢 Alta | 🟢 Alta | 🟡 Media | 🟡 Media |

---

## 1. Eloquent Accessors & Mutators (Prioridad: ALTA)

### Problema Actual
La lógica de presentación y transformación de datos está dispersa en services y helpers, creando inconsistencias.

### Solución Laravel

```php
class ProductItem extends Model 
{
    // Accessor para mostrar estado human-readable
    protected function status(): Attribute 
    {
        return Attribute::make(
            get: fn($value) => ProductItemStatus::from($value)->getLabel(),
        );
    }
    
    // Mutator para normalizar especificaciones
    protected function specifications(): Attribute 
    {
        return Attribute::make(
            set: fn($value) => $this->normalizeSpecifications($value),
            get: fn($value) => ProductSpecifications::fromJson($value),
        );
    }
    
    // Accessor calculado para rentabilidad (RF-05.3)
    protected function profitabilityIndicator(): Attribute 
    {
        return Attribute::make(
            get: function() {
                if (!$this->actual_costs || !$this->estimated_revenue) {
                    return 'pending';
                }
                
                $margin = (($this->estimated_revenue - $this->actual_costs) / $this->estimated_revenue) * 100;
                
                return match(true) {
                    $margin >= 25 => 'excellent',
                    $margin >= 15 => 'good', 
                    $margin >= 5 => 'fair',
                    default => 'poor'
                };
            },
        );
    }
    
    private function normalizeSpecifications(array $specs): array 
    {
        // Validar y normalizar según ProductSubcategory
        $subcategory = $this->subcategory;
        return $subcategory ? $subcategory->validateSpecifications($specs) : $specs;
    }
}
```

### Beneficios Específicos para PromoSmart

1. **Dashboard Widget "Proyectos Activos"**: Indicador de rentabilidad automático
2. **Vista de Ítem de Producto**: Estados human-readable sin lógica adicional
3. **Consistencia**: Misma lógica de presentación en toda la aplicación

### Implementación en Filament

```php
// En Filament Resource
TextColumn::make('profitability_indicator')
    ->badge()
    ->color(fn($state) => match($state) {
        'excellent' => 'success',
        'good' => 'info',
        'fair' => 'warning',
        'poor' => 'danger',
        default => 'gray'
    }),
```

---

## 2. Eloquent Model Events para Workflows (Prioridad: ALTA)

### Problema Actual
Los workflows de cambio de estado requieren código manual en cada punto de transición.

### Solución Laravel

```php
class ProductItem extends Model 
{
    protected static function booted() 
    {
        // Auto-asignar analista según categoría (RF-01.2)
        static::creating(function($item) {
            if (!$item->assigned_to) {
                $item->assigned_to = $item->getDefaultAnalyst();
            }
        });
        
        // Workflow automático al cambiar estado (RF-01.3)
        static::updating(function($item) {
            if ($item->isDirty('status')) {
                $item->handleStatusTransition(
                    $item->getOriginal('status'),
                    $item->status
                );
            }
        });
        
        // Crear actividades automáticamente (RF-14)
        static::updated(function($item) {
            if ($item->isDirty('status')) {
                $item->logActivity('status_changed', [
                    'from' => $item->getOriginal('status'),
                    'to' => $item->status,
                    'user_id' => auth()->id()
                ]);
            }
        });
        
        // Validar transiciones permitidas
        static::updating(function($item) {
            if ($item->isDirty('status')) {
                $item->validateStatusTransition(
                    $item->getOriginal('status'),
                    $item->status
                );
            }
        });
    }
    
    protected function handleStatusTransition(string $from, string $to): void 
    {
        match($to) {
            'PENDING_VM_APPROVAL' => $this->createVirtualMockupTask(),
            'PENDING_PPS_APPROVAL' => $this->createPhysicalSampleTask(),
            'READY_FOR_PRODUCTION' => $this->createProductionOrderTask(),
            default => null
        };
    }
    
    protected function getDefaultAnalyst(): ?int 
    {
        return $this->subcategory
            ->category
            ->getDefaultAnalyst()
            ?->id;
    }
}
```

### Beneficios Específicos

1. **RF-01.3**: Transiciones automáticas sin intervención manual
2. **RF-14**: Feed de actividad automático
3. **Consistencia**: Mismo comportamiento en toda la aplicación
4. **Reducción de bugs**: No se puede olvidar crear actividades

---

## 3. Custom Casts para Value Objects (Prioridad: ALTA)

### Problema Actual
Los Value Objects requieren serialización/deserialización manual en cada uso.

### Solución Laravel

```php
// Cast para Money (crítico para RF-05)
class MoneyCast implements CastsAttributes 
{
    public function get($model, string $key, $value, array $attributes): ?Money 
    {
        if ($value === null) return null;
        
        $currency = $attributes[$key . '_currency'] ?? 'USD';
        return new Money($value, Currency::from($currency));
    }
    
    public function set($model, string $key, $value, array $attributes): array 
    {
        if ($value === null) {
            return [$key => null, $key . '_currency' => null];
        }
        
        if (!$value instanceof Money) {
            throw new InvalidArgumentException('Value must be a Money instance');
        }
        
        return [
            $key => $value->getAmount(),
            $key . '_currency' => $value->getCurrency()->value
        ];
    }
}

// Cast para ProductSpecifications
class ProductSpecificationsCast implements CastsAttributes 
{
    public function get($model, string $key, $value, array $attributes): ?ProductSpecifications 
    {
        if (!$value) return null;
        
        $subcategory = $model->subcategory;
        return new ProductSpecifications(json_decode($value, true), $subcategory);
    }
    
    public function set($model, string $key, $value, array $attributes): string 
    {
        if ($value instanceof ProductSpecifications) {
            return json_encode($value->toArray());
        }
        
        if (is_array($value)) {
            $subcategory = $model->subcategory;
            $specs = new ProductSpecifications($value, $subcategory);
            return json_encode($specs->toArray());
        }
        
        return json_encode($value);
    }
}

// Uso en modelos
class ProductItem extends Model 
{
    protected $casts = [
        'unit_price' => MoneyCast::class,
        'specifications' => ProductSpecificationsCast::class,
        'target_price' => MoneyCast::class,
    ];
}

class SupplierQuotation extends Model 
{
    protected $casts = [
        'unit_price' => MoneyCast::class,
        'total_amount' => MoneyCast::class,
    ];
}
```

### Beneficios Específicos

1. **RF-05.1**: Manejo automático de multimoneda
2. **RF-01.2**: Validación automática de especificaciones
3. **Type Safety**: Siempre trabajas con objetos tipados
4. **Consistency**: Misma lógica de validación en toda la app

---

## 4. Eloquent Scopes para Business Logic (Prioridad: MEDIA)

### Problema Actual
Las queries de negocio se repiten en múltiples controllers y services.

### Solución Laravel

```php
class Project extends Model 
{
    // Para Dashboard Principal - Widget "Proyectos Activos"
    public function scopeAssignedTo($query, User $user) 
    {
        return $query->whereHas('productItems', function($q) use ($user) {
            $q->where('assigned_to', $user->id);
        });
    }
    
    // Para identificar proyectos con riesgo (RF-10)
    public function scopeAtRisk($query, int $daysThreshold = 7) 
    {
        return $query->whereHas('productItems', function($q) use ($daysThreshold) {
            $q->where('status', 'overdue')
              ->orWhere(function($subQ) use ($daysThreshold) {
                  $subQ->whereIn('status', ['PENDING_VM_APPROVAL', 'PENDING_PPS_APPROVAL'])
                       ->where('updated_at', '<', now()->subDays($daysThreshold));
              });
        });
    }
    
    // Para análisis financiero (RF-05.3)
    public function scopeWithProfitability($query) 
    {
        return $query->selectRaw('
            projects.*,
            CASE 
                WHEN actual_costs > 0 THEN 
                    ((estimated_revenue - actual_costs) / estimated_revenue) * 100
                ELSE 
                    ((estimated_revenue - estimated_costs) / estimated_revenue) * 100
            END as profitability_percentage
        ');
    }
    
    // Para reportes por período
    public function scopeInPeriod($query, CarbonInterface $start, CarbonInterface $end) 
    {
        return $query->whereBetween('created_at', [$start, $end]);
    }
    
    // Para dashboard de líder de equipo (RF-10)
    public function scopeForTeam($query, Team $team) 
    {
        return $query->whereHas('productItems.assignedAnalyst', function($q) use ($team) {
            $q->where('team_id', $team->id);
        });
    }
}

class SupplierQuotation extends Model 
{
    // Para comparación de proveedores (RF-02.1)
    public function scopeSubmitted($query) 
    {
        return $query->where('status', 'submitted');
    }
    
    public function scopeForProduct($query, ProductItem $product) 
    {
        return $query->where('product_item_id', $product->id);
    }
    
    // Para análisis de rendimiento de proveedores
    public function scopeWon($query) 
    {
        return $query->where('status', 'selected');
    }
    
    public function scopeInLastMonths($query, int $months) 
    {
        return $query->where('created_at', '>=', now()->subMonths($months));
    }
}
```

### Uso en Controllers/Services

```php
class DashboardController extends Controller 
{
    public function index() 
    {
        $user = auth()->user();
        
        return [
            'assigned_projects' => Project::assignedTo($user)
                ->with(['customer', 'productItems'])
                ->get(),
            'at_risk_projects' => Project::atRisk()
                ->assignedTo($user)
                ->count(),
            'profitability_summary' => Project::assignedTo($user)
                ->withProfitability()
                ->get()
                ->avg('profitability_percentage')
        ];
    }
}
```

---

## 5. Laravel Queues para Procesos Largos (Prioridad: MEDIA)

### Problema Actual
Generación de PDFs y procesos pesados bloquean la interfaz de usuario.

### Solución Laravel

```php
// Job para generación de artefactos (RF-06.1)
class GenerateCustomerQuotationJob implements ShouldQueue 
{
    use Queueable, SerializesModels;
    
    public function __construct(
        private CustomerQuotation $quotation
    ) {}
    
    public function handle(): void 
    {
        // Generar PDF usando snapshot data
        $generator = app(QuotationPDFGenerator::class);
        $pdfPath = $generator->generate($this->quotation);
        
        // Actualizar registro
        $this->quotation->update([
            'pdf_s3_path' => $pdfPath,
            'status' => 'generated'
        ]);
        
        // Notificar completion
        $this->quotation->creator->notify(
            new QuotationGeneratedNotification($this->quotation)
        );
    }
    
    public function failed(Throwable $exception): void 
    {
        $this->quotation->update(['status' => 'generation_failed']);
        
        $this->quotation->creator->notify(
            new QuotationGenerationFailedNotification($this->quotation, $exception)
        );
    }
}

// Job para análisis automático (RF-03.1)
class AnalyzeSupplierPerformanceJob implements ShouldQueue 
{
    public function handle(): void 
    {
        $suppliers = Supplier::with(['quotations' => function($q) {
            $q->inLastMonths(12);
        }])->get();
        
        foreach ($suppliers as $supplier) {
            $metrics = $this->calculatePerformanceMetrics($supplier);
            
            $supplier->update([
                'performance_score' => $metrics['overall_score'],
                'win_rate' => $metrics['win_rate'],
                'avg_lead_time' => $metrics['avg_lead_time'],
                'last_performance_update' => now()
            ]);
        }
    }
}

// Job para alertas automáticas (RF-09)
class CheckOverdueItemsJob implements ShouldQueue 
{
    public function handle(): void 
    {
        $overdueItems = ProductItem::where('status', 'PENDING_VM_APPROVAL')
            ->where('updated_at', '<', now()->subDays(3))
            ->with(['assignedAnalyst', 'project.customer'])
            ->get();
            
        foreach ($overdueItems as $item) {
            $item->assignedAnalyst->notify(
                new ItemOverdueNotification($item)
            );
        }
    }
}
```

### Implementación en Actions

```php
class GenerateQuotationAction 
{
    public function handle(Project $project): CustomerQuotation 
    {
        $quotation = CustomerQuotation::create([
            'project_id' => $project->id,
            'status' => 'generating',
            'generation_data' => $this->collectSnapshotData($project)
        ]);
        
        // Dispatch job en lugar de generar sync
        GenerateCustomerQuotationJob::dispatch($quotation);
        
        return $quotation;
    }
}
```

---

## 6. Laravel Notifications para Workflows (Prioridad: MEDIA)

### Problema Actual
Las notificaciones por email están hardcodeadas y no son consistentes.

### Solución Laravel

```php
// Notification para aprobaciones (RF-14, RF-09)
class VirtualMockupNeedsApprovalNotification extends Notification implements ShouldQueue 
{
    public function __construct(
        private VirtualMockup $mockup
    ) {}
    
    public function via($notifiable): array 
    {
        // Configurar canales según preferencias del usuario
        $channels = ['database'];
        
        if ($notifiable->notification_preferences['email_enabled'] ?? true) {
            $channels[] = 'mail';
        }
        
        return $channels;
    }
    
    public function toMail($notifiable): MailMessage 
    {
        return (new MailMessage)
            ->subject("Virtual Mockup Ready for Approval - {$this->mockup->productItem->name}")
            ->greeting("Hello {$notifiable->name}!")
            ->line("A new virtual mockup is ready for your review.")
            ->line("Product: {$this->mockup->productItem->name}")
            ->line("Project: {$this->mockup->productItem->project->name}")
            ->action('Review Mockup', route('mockups.show', $this->mockup))
            ->line('Please review and approve/reject within 3 business days.')
            ->salutation('Best regards, PromoSmart Team');
    }
    
    public function toArray($notifiable): array 
    {
        return [
            'type' => 'mockup_approval',
            'mockup_id' => $this->mockup->id,
            'product_name' => $this->mockup->productItem->name,
            'project_name' => $this->mockup->productItem->project->name,
            'deadline' => $this->mockup->deadline,
            'action_url' => route('mockups.show', $this->mockup)
        ];
    }
}

// Notification para cambios de estado
class ProjectStatusChangedNotification extends Notification 
{
    public function __construct(
        private Project $project,
        private string $oldStatus,
        private string $newStatus
    ) {}
    
    public function via($notifiable): array 
    {
        return ['database', 'mail'];
    }
    
    public function toMail($notifiable): MailMessage 
    {
        $statusLabel = ProjectStatus::from($this->newStatus)->getLabel();
        
        return (new MailMessage)
            ->subject("Project Status Update - {$this->project->name}")
            ->line("The status of project '{$this->project->name}' has been updated.")
            ->line("New Status: {$statusLabel}")
            ->when($this->newStatus === 'confirmed', function($mail) {
                return $mail->line('🎉 Congratulations! The project has been confirmed.')
                           ->action('View Project', route('projects.show', $this->project));
            })
            ->when($this->newStatus === 'delivered', function($mail) {
                return $mail->line('✅ All products have been successfully delivered.')
                           ->action('View Final Report', route('projects.report', $this->project));
            });
    }
}
```

### Integración con Model Events

```php
class VirtualMockup extends Model 
{
    protected static function booted() 
    {
        static::updated(function($mockup) {
            if ($mockup->isDirty('status') && $mockup->status === 'pending_approval') {
                // Notificar al cliente y analista responsable
                $recipients = [
                    $mockup->productItem->project->customer,
                    $mockup->productItem->assignedAnalyst
                ];
                
                Notification::send($recipients, new VirtualMockupNeedsApprovalNotification($mockup));
            }
        });
    }
}
```

---

## 7. Laravel Validation Rules Personalizadas (Prioridad: MEDIA)

### Problema Actual
Validaciones de negocio están dispersas en controllers y services.

### Solución Laravel

```php
// Rule para validar especificaciones según categoría (RF-01.2)
class ValidProductSpecifications implements Rule 
{
    public function __construct(
        private ProductSubcategory $subcategory
    ) {}
    
    public function passes($attribute, $value): bool 
    {
        if (!is_array($value)) return false;
        
        $requiredSpecs = $this->subcategory->getRequiredSpecifications();
        $providedSpecs = array_keys($value);
        
        // Verificar que todas las specs requeridas estén presentes
        $missingSpecs = array_diff($requiredSpecs, $providedSpecs);
        
        return empty($missingSpecs);
    }
    
    public function message(): string 
    {
        return 'The specifications must include all required fields for this product category.';
    }
}

// Rule para validar transiciones de estado
class ValidStatusTransition implements Rule 
{
    public function __construct(
        private string $currentStatus,
        private string $entityType = 'ProductItem'
    ) {}
    
    public function passes($attribute, $value): bool 
    {
        $allowedTransitions = $this->getAllowedTransitions();
        
        return in_array($value, $allowedTransitions[$this->currentStatus] ?? []);
    }
    
    public function message(): string 
    {
        $allowed = implode(', ', $this->getAllowedTransitions()[$this->currentStatus] ?? []);
        return "Invalid status transition. Allowed transitions from {$this->currentStatus}: {$allowed}";
    }
    
    private function getAllowedTransitions(): array 
    {
        return [
            'DRAFT' => ['READY_FOR_SOURCING'],
            'READY_FOR_SOURCING' => ['SOURCING_IN_PROGRESS'],
            'SOURCING_IN_PROGRESS' => ['QUOTED_TO_CUSTOMER', 'READY_FOR_SOURCING'],
            'QUOTED_TO_CUSTOMER' => ['PENDING_VM_APPROVAL', 'SOURCING_IN_PROGRESS'],
            // ... resto de transiciones
        ];
    }
}

// Uso en FormRequests
class UpdateProductItemRequest extends FormRequest 
{
    public function rules(): array 
    {
        $productItem = $this->route('productItem');
        $subcategory = ProductSubcategory::find($this->subcategory_id ?? $productItem->subcategory_id);
        
        return [
            'name' => 'sometimes|string|max:255',
            'quantity' => 'sometimes|integer|min:1|max:100000',
            'specifications' => [
                'sometimes',
                'array',
                new ValidProductSpecifications($subcategory)
            ],
            'status' => [
                'sometimes',
                'string',
                new ValidStatusTransition($productItem->status)
            ]
        ];
    }
    
    public function authorize(): bool 
    {
        $productItem = $this->route('productItem');
        return $this->user()->can('update', $productItem);
    }
}
```

---

## 8. Laravel Collections para Business Logic (Prioridad: BAJA)

### Problema Actual
Lógica de agregación y análisis repetida en múltiples services.

### Solución Laravel

```php
class ProjectAnalyticsService 
{
    public function getTeamMetrics(Team $team, CarbonPeriod $period): array 
    {
        $projects = Project::forTeam($team)
            ->inPeriod($period->start, $period->end)
            ->withProfitability()
            ->get();
            
        return [
            'total_projects' => $projects->count(),
            'avg_profitability' => $projects->avg('profitability_percentage'),
            'projects_by_status' => $projects->groupBy('status')
                ->map->count()
                ->sortByDesc(fn($count) => $count),
            'top_customers' => $projects->groupBy('customer.name')
                ->map(function($customerProjects) {
                    return [
                        'project_count' => $customerProjects->count(),
                        'total_revenue' => $customerProjects->sum('estimated_revenue')
                    ];
                })
                ->sortByDesc('total_revenue')
                ->take(5),
            'monthly_performance' => $projects->groupBy(function($project) {
                return $project->created_at->format('Y-m');
            })->map(function($monthProjects) {
                return [
                    'project_count' => $monthProjects->count(),
                    'total_revenue' => $monthProjects->sum('estimated_revenue'),
                    'avg_profitability' => $monthProjects->avg('profitability_percentage')
                ];
            })
        ];
    }
    
    public function getSupplierComparison(ProductItem $item): Collection 
    {
        return $item->supplierQuotations()
            ->submitted()
            ->get()
            ->map(function($quotation) {
                return [
                    'supplier' => $quotation->supplier,
                    'unit_price' => $quotation->unit_price,
                    'total_cost' => $quotation->unit_price->multiply($quotation->productItem->quantity),
                    'lead_time' => $quotation->lead_time_days,
                    'score' => $this->calculateQuotationScore($quotation)
                ];
            })
            ->sortBy('score')
            ->values();
    }
}
```

---

## 9. Laravel Policies para Authorization (Prioridad: MEDIA)

### Problema Actual
La lógica de autorización está hardcodeada en controllers.

### Solución Laravel

```php
class ProductItemPolicy 
{
    public function viewAny(User $user): bool 
    {
        return $user->hasAnyRole(['sales_analyst', 'purchase_analyst', 'import_analyst', 'team_lead', 'admin']);
    }
    
    public function view(User $user, ProductItem $item): bool 
    {
        // Puede ver si es el analista asignado o miembro del equipo
        return $this->isAssignedAnalyst($user, $item) 
            || $this->isTeamMember($user, $item)
            || $user->hasRole('admin');
    }
    
    public function update(User $user, ProductItem $item): bool 
    {
        // No se puede modificar después del compromiso
        if ($item->project->isCommitted()) {
            return $user->hasRole('admin');
        }
        
        // Solo el analista asignado puede modificar
        if ($this->isAssignedAnalyst($user, $item)) return true;
        
        // Los líderes pueden modificar ítems de su equipo
        if ($user->hasRole('team_lead') && $this->isTeamMember($user, $item)) {
            return true;
        }
        
        return $user->hasRole('admin');
    }
    
    public function approve(User $user, ProductItem $item): bool 
    {
        // Diferentes tipos de aprobación requieren diferentes roles
        return match($item->getCurrentApprovalType()) {
            'virtual_mockup' => $user->hasAnyRole(['customer', 'sales_analyst']) 
                || $this->isProjectCustomer($user, $item),
            'physical_sample' => $user->hasAnyRole(['customer', 'import_analyst'])
                || $this->isProjectCustomer($user, $item),
            'final_delivery' => $user->hasRole('customer') 
                || $this->isProjectCustomer($user, $item),
            default => false
        };
    }
    
    public function changeStatus(User $user, ProductItem $item): bool 
    {
        return $this->update($user, $item);
    }
    
    private function isAssignedAnalyst(User $user, ProductItem $item): bool 
    {
        return $item->assigned_to === $user->id;
    }
    
    private function isTeamMember(User $user, ProductItem $item): bool 
    {
        return $user->team_id === $item->assignedAnalyst?->team_id;
    }
    
    private function isProjectCustomer(User $user, ProductItem $item): bool 
    {
        return $user->hasRole('customer') 
            && $user->customer_id === $item->project->customer_id;
    }
}

class ProjectPolicy 
{
    public function update(User $user, Project $project): bool 
    {
        // No modificar proyectos comprometidos
        if ($project->isCommitted()) {
            return $user->hasRole('admin');
        }
        
        // Solo analistas asignados a productos del proyecto
        if ($project->productItems()->where('assigned_to', $user->id)->exists()) {
            return true;
        }
        
        // Líderes pueden modificar proyectos de su equipo
        if ($user->hasRole('team_lead')) {
            return $project->productItems()
                ->whereHas('assignedAnalyst', function($q) use ($user) {
                    $q->where('team_id', $user->team_id);
                })->exists();
        }
        
        return $user->hasRole('admin');
    }
}
```

### Uso en Controllers

```php
class ProductItemController extends Controller 
{
    public function update(UpdateProductItemRequest $request, ProductItem $item) 
    {
        $this->authorize('update', $item);
        
        $item->update($request->validated());
        
        return new ProductItemResource($item);
    }
    
    public function approve(Request $request, ProductItem $item) 
    {
        $this->authorize('approve', $item);
        
        $item->approve(auth()->user(), $request->input('notes'));
        
        return new ProductItemResource($item);
    }
}
```

---

## 10. Model Factories para Testing y Seeding (Prioridad: BAJA)

### Problema Actual
Crear datos de prueba es manual y inconsistente.

### Solución Laravel

```php
class ProjectFactory extends Factory 
{
    public function definition() 
    {
        return [
            'name' => $this->faker->company() . ' ' . $this->faker->year() . ' Campaign',
            'description' => $this->faker->sentence(),
            'customer_id' => Customer::factory(),
            'status' => $this->faker->randomElement(['draft', 'quoted', 'confirmed']),
            'estimated_revenue' => $this->faker->numberBetween(10000, 100000),
            'estimated_costs' => function(array $attributes) {
                return $attributes['estimated_revenue'] * 0.7; // 30% margin
            },
            'target_margin_percentage' => $this->faker->numberBetween(15, 35),
            'timeline_days' => $this->faker->numberBetween(30, 120),
        ];
    }
    
    public function withProducts(int $count = 3) 
    {
        return $this->has(ProductItem::factory()->count($count));
    }
    
    public function confirmed() 
    {
        return $this->state(function() {
            return [
                'status' => 'confirmed',
                'commitment_date' => $this->faker->dateTimeBetween('-30 days', 'now'),
                'baseline_data' => function(array $attributes) {
                    return [
                        'estimated_revenue' => $attributes['estimated_revenue'],
                        'estimated_costs' => $attributes['estimated_costs'],
                        'created_at' => now()
                    ];
                }
            ];
        });
    }
    
    public function atRisk() 
    {
        return $this->state(function() {
            return [
                'status' => 'confirmed',
                'updated_at' => now()->subDays(10), // Sin updates recientes
            ];
        });
    }
}

class ProductItemFactory extends Factory 
{
    public function definition() 
    {
        $subcategory = ProductSubcategory::inRandomOrder()->first();
        
        return [
            'name' => $this->faker->words(3, true) . ' ' . $subcategory->name,
            'project_id' => Project::factory(),
            'subcategory_id' => $subcategory->id,
            'quantity' => $this->faker->numberBetween(100, 5000),
            'specifications' => function() use ($subcategory) {
                return $this->generateSpecifications($subcategory);
            },
            'status' => $this->faker->randomElement(['DRAFT', 'READY_FOR_SOURCING', 'QUOTED_TO_CUSTOMER']),
            'assigned_to' => User::factory(),
            'unit_price' => $this->faker->randomFloat(2, 5, 50),
            'target_margin_percentage' => $this->faker->numberBetween(20, 40),
        ];
    }
    
    public function inProduction() 
    {
        return $this->state([
            'status' => 'IN_PRODUCTION',
            'production_started_at' => $this->faker->dateTimeBetween('-30 days', 'now'),
        ]);
    }
    
    private function generateSpecifications(ProductSubcategory $subcategory): array 
    {
        $specs = [];
        
        foreach ($subcategory->getRequiredSpecifications() as $spec) {
            $specs[$spec] = match($spec) {
                'material' => $this->faker->randomElement(['Cotton', 'Polyester', 'Plastic', 'Metal']),
                'color' => $this->faker->colorName(),
                'dimensions' => $this->faker->numberBetween(5, 30) . 'x' . $this->faker->numberBetween(5, 30),
                'weight' => $this->faker->numberBetween(10, 500) . 'g',
                default => $this->faker->word()
            };
        }
        
        return $specs;
    }
}
```

### Uso en Tests

```php
class ProjectAnalyticsTest extends TestCase 
{
    /** @test */
    public function it_calculates_team_metrics_correctly() 
    {
        $team = Team::factory()->create();
        $user = User::factory()->for($team)->create();
        
        // Crear proyectos con datos específicos
        $projects = Project::factory()
            ->count(5)
            ->confirmed()
            ->has(ProductItem::factory()->state(['assigned_to' => $user->id]))
            ->create();
            
        $metrics = app(ProjectAnalyticsService::class)
            ->getTeamMetrics($team, CarbonPeriod::create('-1 month', 'now'));
            
        $this->assertEquals(5, $metrics['total_projects']);
        $this->assertIsFloat($metrics['avg_profitability']);
    }
}
```

---

## Matriz de Priorización

### Implementación Recomendada por Fases

#### Fase 1: Fundaciones (4-6 semanas)
1. **Eloquent Accessors/Mutators** - Base para presentación consistente
2. **Custom Casts** - Value Objects automáticos  
3. **Model Events** - Workflows automáticos básicos

#### Fase 2: Workflows (3-4 semanas)
4. **Eloquent Scopes** - Queries de negocio reutilizables
5. **Validation Rules** - Validaciones de negocio centralizadas
6. **Policies** - Autorización granular

#### Fase 3: Optimización (2-3 semanas)
7. **Laravel Queues** - Procesos asíncronos
8. **Notifications** - Sistema de notificaciones unificado

#### Fase 4: Calidad (1-2 semanas)
9. **Model Factories** - Testing y seeding consistente
10. **Laravel Collections** - Lógica de análisis optimizada

### ROI Estimado por Patrón

| Patrón | Tiempo Desarrollo | Tiempo Mantenimiento | Reducción Bugs | ROI Score |
|--------|-------------------|---------------------|----------------|-----------|
| **Model Events** | 2 semanas | -50% | -70% | 🟢 Alto |
| **Custom Casts** | 1 semana | -60% | -80% | 🟢 Alto |
| **Accessors/Mutators** | 1 semana | -40% | -50% | 🟢 Alto |
| **Eloquent Scopes** | 1 semana | -30% | -40% | 🟡 Medio |
| **Policies** | 1 semana | -20% | -60% | 🟡 Medio |
| **Notifications** | 2 semanas | -30% | -30% | 🟡 Medio |

---

## Beneficios Esperados

### Reducción de Complejidad
- **-40% líneas de código** en controllers
- **-60% código duplicado** entre services
- **-50% bugs relacionados** con validaciones

### Mejora en Mantenibilidad  
- **Consistencia** automática en toda la aplicación
- **Testabilidad** mejorada con factories
- **Evolución** más fácil con patterns establecidos

### Experiencia del Desarrollador
- **Onboarding** más rápido con convenciones Laravel
- **Debugging** más fácil con stack traces claros
- **Productividad** mayor usando herramientas familiares

### Adherencia al Ecosistema
- **Compatibilidad** con packages de Laravel
- **Updates** más simples del framework
- **Documentación** estándar disponible

---

## Conclusiones

Los **12 patrones idiomáticos de Laravel** identificados pueden **reducir significativamente la complejidad** del código de PromoSmart mientras **mejoran la mantenibilidad** y **adherencia a las convenciones del framework**.

**Recomendación:** Implementar en **4 fases** priorizando patrones de **alto ROI** que proporcionan beneficios inmediatos en **workflows automáticos** y **consistencia de datos**.

La inversión total estimada de **10-15 semanas** se recuperará en **reducción de tiempo de desarrollo** y **menor incidencia de bugs** en el primer año de operación.