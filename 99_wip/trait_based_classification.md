# Trait-Based Classification: Clasificación por Comportamiento en PromoSmart

> **Propósito:** Documentar la estrategia de clasificación de entidades basada en traits funcionales en lugar de categorías artificiales como "artefactos vs modelos", promoviendo composición de comportamientos y reutilización de código.

---

## Problema: Clasificación Artificial de Entidades

### El Anti-Patrón Tradicional

En el diseño inicial de PromoSmart, surgió la tendencia de clasificar entidades como "artefactos" vs "modelos normales":

```php
// Clasificación artificial problemática
"Artefactos":
- CustomerQuotation
- SupplierPurchaseOrder  
- VirtualMockup
- PreProductionSample

"Modelos Normales":
- Project
- ProductItem
- Customer
- Supplier
```

### Problemas de esta Clasificación

1. **Límites difusos**: ¿ProductItem es "artefacto" cuando genera snapshots?
2. **Funcionalidad mixta**: Algunas entidades tienen comportamientos de ambas categorías
3. **Evolución compleja**: ¿Qué pasa cuando un "modelo normal" necesita generar documentos?
4. **Código duplicado**: Comportamientos similares implementados múltiples veces

---

## Solución: Trait-Based Classification

### Filosofía Central

**En lugar de "qué ES una entidad", enfocarse en "qué HACE una entidad"**

```php
// Enfoque basado en comportamientos
interface DocumentGenerator 
{
    public function generateDocument(): string;
}

interface SendableToRecipient 
{
    public function send(): void;
}

interface RequiresApproval 
{
    public function approve(User $approver): void;
    public function reject(User $rejector, string $reason): void;
}

// Composición de comportamientos
class CustomerQuotation extends Model implements 
    DocumentGenerator, 
    SendableToRecipient, 
    RequiresApproval 
{
    use GeneratesDocuments, SendableDocument, ApprovableDocument;
}
```

### Matriz de Comportamientos

| Entidad | Genera Docs | Enviable | Aprobable | Inmutable | Versionable | Trackeable |
|---------|-------------|----------|-----------|-----------|-------------|------------|
| **CustomerQuotation** | ✅ PDF | ✅ Cliente | ✅ Cliente | ✅ Post-envío | ✅ Explícito | ✅ |
| **SupplierPO** | ✅ PDF | ✅ Proveedor | ✅ Proveedor | ✅ Post-envío | ✅ Explícito | ✅ |
| **VirtualMockup** | ✅ Images | ✅ Cliente | ✅ Cliente | ✅ Post-aprobación | ✅ Revisiones | ✅ |
| **PreProductionSample** | ❌ | ❌ | ✅ Cliente | ✅ Post-aprobación | ❌ | ✅ |
| **ProductItem** | ❌ | ❌ | ❌ | ❌ | ✅ Snapshots | ✅ |
| **Project** | ❌ | ❌ | ❌ | ✅ Post-compromiso | ✅ Baseline | ✅ |
| **Customer** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **Supplier** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ |

---

## Implementación de Traits Funcionales

### 1. Trait: GeneratesDocuments

```php
interface DocumentGenerator 
{
    public function generateDocument(): string;
    public function getDocumentUrl(): string;
    public function regenerateDocument(): string;
}

trait GeneratesDocuments 
{
    public function generateDocument(): string 
    {
        $this->validateForGeneration();
        
        $generator = app($this->getGeneratorClass());
        $filePath = $generator->generate($this);
        
        $this->update([$this->getFilePathColumn() => $filePath]);
        $this->logActivity('document_generated');
        
        return $filePath;
    }
    
    public function getDocumentUrl(): string 
    {
        $path = $this->getAttribute($this->getFilePathColumn());
        
        if (!$path) {
            throw new DocumentNotGeneratedException();
        }
        
        return Storage::disk('s3')->temporaryUrl($path, now()->addHours(1));
    }
    
    public function regenerateDocument(): string 
    {
        if ($this->isImmutable()) {
            throw new ImmutableDocumentException();
        }
        
        return $this->generateDocument();
    }
    
    // Template methods para implementar en cada clase
    abstract protected function getGeneratorClass(): string;
    abstract protected function getFilePathColumn(): string;
    abstract protected function validateForGeneration(): void;
}

// Implementación específica
class CustomerQuotation extends Model implements DocumentGenerator 
{
    use GeneratesDocuments;
    
    protected function getGeneratorClass(): string 
    {
        return CustomerQuotationPDFGenerator::class;
    }
    
    protected function getFilePathColumn(): string 
    {
        return 'pdf_s3_path';
    }
    
    protected function validateForGeneration(): void 
    {
        if ($this->project->productItems->isEmpty()) {
            throw new InvalidGenerationException('No products in project');
        }
        
        if (!$this->project->customer) {
            throw new InvalidGenerationException('No customer assigned');
        }
    }
}
```

### 2. Trait: SendableDocument

```php
interface SendableToRecipient 
{
    public function send(): void;
    public function markAsSent(): void;
    public function canBeSent(): bool;
}

trait SendableDocument 
{
    public function send(): void 
    {
        if (!$this->canBeSent()) {
            throw new CannotSendDocumentException($this->getSendBlockers());
        }
        
        $this->beforeSending();
        $this->performSend();
        $this->afterSending();
    }
    
    public function canBeSent(): bool 
    {
        return empty($this->getSendBlockers());
    }
    
    protected function beforeSending(): void 
    {
        // Asegurar que el documento esté generado
        if (!$this->hasGeneratedDocument()) {
            $this->generateDocument();
        }
        
        // Crear snapshot inmutable antes del envío
        if (in_array(HasSelectiveVersioning::class, class_uses($this))) {
            $this->createVersion('before_sending');
        }
    }
    
    protected function performSend(): void 
    {
        $recipients = $this->getRecipients();
        $notification = $this->getNotificationClass();
        
        foreach ($recipients as $recipient) {
            $recipient->notify(new $notification($this));
        }
    }
    
    protected function afterSending(): void 
    {
        $this->markAsSent();
        $this->makeImmutable();
        $this->logActivity('document_sent');
    }
    
    public function markAsSent(): void 
    {
        $this->update([
            'status' => 'sent',
            'sent_at' => now(),
            'sent_by' => auth()->id()
        ]);
    }
    
    // Template methods
    abstract protected function getRecipients(): Collection;
    abstract protected function getNotificationClass(): string;
    abstract protected function getSendBlockers(): array;
}

// Implementación específica
class CustomerQuotation extends Model implements SendableToRecipient 
{
    use SendableDocument;
    
    protected function getRecipients(): Collection 
    {
        // Obtener contactos del cliente
        return $this->project->customer->contacts;
    }
    
    protected function getNotificationClass(): string 
    {
        return CustomerQuotationSentNotification::class;
    }
    
    protected function getSendBlockers(): array 
    {
        $blockers = [];
        
        if ($this->status === 'sent') {
            $blockers[] = 'Document already sent';
        }
        
        if (!$this->project->customer->hasValidEmail()) {
            $blockers[] = 'Customer has no valid email';
        }
        
        if ($this->total_amount <= 0) {
            $blockers[] = 'Invalid total amount';
        }
        
        return $blockers;
    }
}
```

### 3. Trait: ApprovableDocument

```php
interface RequiresApproval 
{
    public function approve(User $approver, string $notes = null): void;
    public function reject(User $rejector, string $reason): void;
    public function requestRevision(string $feedback): void;
}

trait ApprovableDocument 
{
    public function approve(User $approver, string $notes = null): void 
    {
        if (!$this->canBeApproved()) {
            throw new CannotApproveException($this->getApprovalBlockers());
        }
        
        DB::transaction(function() use ($approver, $notes) {
            $this->update([
                'status' => 'approved',
                'approved_by' => $approver->id,
                'approved_at' => now(),
                'approval_notes' => $notes
            ]);
            
            $this->makeImmutable();
            $this->onApproved($approver);
            $this->logActivity('document_approved', [
                'approver' => $approver->name,
                'notes' => $notes
            ]);
        });
    }
    
    public function reject(User $rejector, string $reason): void 
    {
        if (!$this->canBeRejected()) {
            throw new CannotRejectException();
        }
        
        DB::transaction(function() use ($rejector, $reason) {
            $this->update([
                'status' => 'rejected',
                'rejected_by' => $rejector->id,
                'rejected_at' => now(),
                'rejection_reason' => $reason
            ]);
            
            $this->onRejected($rejector, $reason);
            $this->logActivity('document_rejected', [
                'rejector' => $rejector->name,
                'reason' => $reason
            ]);
        });
    }
    
    public function requestRevision(string $feedback): void 
    {
        if ($this->supportsRevisions()) {
            $newVersion = $this->createNewVersion();
            $newVersion->update([
                'status' => 'revision_requested',
                'revision_feedback' => $feedback
            ]);
            
            $this->update(['status' => 'superseded']);
            $this->onRevisionRequested($newVersion, $feedback);
        } else {
            $this->update([
                'status' => 'revision_requested',
                'revision_feedback' => $feedback
            ]);
            
            $this->onRevisionRequested(null, $feedback);
        }
    }
    
    // Template methods
    abstract protected function onApproved(User $approver): void;
    abstract protected function onRejected(User $rejector, string $reason): void;
    abstract protected function onRevisionRequested($newVersion, string $feedback): void;
    
    protected function canBeApproved(): bool 
    {
        return in_array($this->status, ['pending_approval', 'submitted']);
    }
    
    protected function canBeRejected(): bool 
    {
        return in_array($this->status, ['pending_approval', 'submitted']);
    }
    
    protected function getApprovalBlockers(): array 
    {
        $blockers = [];
        
        if ($this->status === 'approved') {
            $blockers[] = 'Already approved';
        }
        
        if ($this->status === 'rejected') {
            $blockers[] = 'Already rejected';
        }
        
        return $blockers;
    }
    
    protected function supportsRevisions(): bool 
    {
        return in_array(HasExplicitVersioning::class, class_uses($this));
    }
}

// Implementación específica
class VirtualMockup extends Model implements RequiresApproval 
{
    use ApprovableDocument;
    
    protected function onApproved(User $approver): void 
    {
        // Avanzar el ProductItem al siguiente estado
        $this->productItem->update(['status' => 'VM_APPROVED']);
        
        // Crear tarea automática para siguiente paso
        $this->productItem->assignTask(
            'Prepare for PPS submission',
            $this->productItem->getAssignedAnalyst('import'),
            now()->addDays(2)
        );
    }
    
    protected function onRejected(User $rejector, string $reason): void 
    {
        // Devolver ProductItem a estado anterior
        $this->productItem->update(['status' => 'READY_FOR_SOURCING']);
        
        // Crear tarea para el diseñador
        $this->productItem->assignTask(
            'Revisar diseño rechazado: ' . $reason,
            $this->creator,
            now()->addDays(2)
        );
    }
    
    protected function onRevisionRequested($newVersion, string $feedback): void 
    {
        if ($newVersion) {
            // Notificar al diseñador sobre nueva versión
            $this->creator->notify(new VirtualMockupRevisionRequestedNotification($newVersion, $feedback));
        }
    }
}
```

### 4. Trait: ImmutableAfterAction

```php
trait ImmutableAfterAction 
{
    protected $immutableTriggers = ['sent', 'approved', 'delivered'];
    
    public function makeImmutable(): void 
    {
        $this->update(['is_immutable' => true]);
    }
    
    public function isImmutable(): bool 
    {
        return $this->is_immutable === true || $this->isTriggeredImmutable();
    }
    
    protected function isTriggeredImmutable(): bool 
    {
        return in_array($this->status, $this->immutableTriggers);
    }
    
    protected static function bootImmutableAfterAction(): void 
    {
        static::updating(function($model) {
            if ($model->isImmutable() && $model->isDirty()) {
                $allowedFields = $model->getImmutableExceptions();
                $dirtyFields = array_keys($model->getDirty());
                $blockedFields = array_diff($dirtyFields, $allowedFields);
                
                if (!empty($blockedFields)) {
                    throw new ImmutableModelException(
                        "Cannot modify immutable fields: " . implode(', ', $blockedFields)
                    );
                }
            }
        });
    }
    
    protected function getImmutableExceptions(): array 
    {
        // Campos que pueden modificarse incluso cuando es inmutable
        return ['updated_at', 'last_accessed_at'];
    }
}
```

### 5. Trait: HasExplicitVersioning

```php
trait HasExplicitVersioning 
{
    public function createNewVersion(): self 
    {
        $newVersion = $this->replicate();
        $newVersion->version_number = $this->getNextVersionNumber();
        $newVersion->parent_version_id = $this->id;
        $newVersion->status = 'draft';
        $newVersion->is_immutable = false;
        $newVersion->save();
        
        // Copiar relaciones necesarias
        $this->copyRelationsToNewVersion($newVersion);
        
        return $newVersion;
    }
    
    public function getNextVersionNumber(): int 
    {
        return static::where($this->getVersionGroupingKey(), $this->getVersionGroupingValue())
            ->max('version_number') + 1;
    }
    
    public function getAllVersions(): Collection 
    {
        return static::where($this->getVersionGroupingKey(), $this->getVersionGroupingValue())
            ->orderBy('version_number')
            ->get();
    }
    
    public function getLatestVersion(): ?self 
    {
        return static::where($this->getVersionGroupingKey(), $this->getVersionGroupingValue())
            ->orderBy('version_number', 'desc')
            ->first();
    }
    
    public function getPreviousVersion(): ?self 
    {
        return static::where($this->getVersionGroupingKey(), $this->getVersionGroupingValue())
            ->where('version_number', '<', $this->version_number)
            ->orderBy('version_number', 'desc')
            ->first();
    }
    
    // Template methods
    abstract protected function getVersionGroupingKey(): string;
    abstract protected function getVersionGroupingValue(): mixed;
    abstract protected function copyRelationsToNewVersion(self $newVersion): void;
}

// Implementación específica
class CustomerQuotation extends Model 
{
    use HasExplicitVersioning;
    
    protected function getVersionGroupingKey(): string 
    {
        return 'project_id';
    }
    
    protected function getVersionGroupingValue(): mixed 
    {
        return $this->project_id;
    }
    
    protected function copyRelationsToNewVersion(self $newVersion): void 
    {
        // Las cotizaciones no tienen relaciones que copiar
        // pero otros modelos podrían necesitarlo
    }
}
```

### 6. Trait: TrackableActivity

```php
trait TrackableActivity 
{
    public function activities() 
    {
        return $this->morphMany(Activity::class, 'subject');
    }
    
    public function logActivity(string $type, array $metadata = []): Activity 
    {
        return $this->activities()->create([
            'activity_type' => $type,
            'title' => $this->getActivityTitle($type),
            'description' => $this->getActivityDescription($type, $metadata),
            'metadata' => $metadata,
            'user_id' => auth()->id(),
            'is_system_generated' => $this->isSystemGeneratedActivity($type)
        ]);
    }
    
    public function getRecentActivity(int $days = 30): Collection 
    {
        return $this->activities()
            ->where('created_at', '>=', now()->subDays($days))
            ->orderBy('created_at', 'desc')
            ->get();
    }
    
    // Template methods
    protected function getActivityTitle(string $type): string 
    {
        return ucwords(str_replace('_', ' ', $type));
    }
    
    protected function getActivityDescription(string $type, array $metadata): string 
    {
        return "Activity of type {$type} occurred";
    }
    
    protected function isSystemGeneratedActivity(string $type): bool 
    {
        $systemTypes = ['status_changed', 'document_generated', 'auto_notification'];
        return in_array($type, $systemTypes);
    }
}
```

---

## Composición de Comportamientos

### Ejemplo: CustomerQuotation Completa

```php
class CustomerQuotation extends Model implements 
    DocumentGenerator,
    SendableToRecipient,
    RequiresApproval
{
    use GeneratesDocuments,
        SendableDocument,
        ApprovableDocument,
        ImmutableAfterAction,
        HasExplicitVersioning,
        TrackableActivity,
        HasActivities;
    
    protected $fillable = [
        'document_number',
        'project_id',
        'version_number',
        'status',
        'total_amount',
        'currency',
        'valid_until',
        'generation_data',
        'pdf_s3_path',
        'sent_at',
        'approved_at',
        'is_immutable'
    ];
    
    protected $casts = [
        'generation_data' => 'array',
        'total_amount' => 'decimal:2',
        'valid_until' => 'date',
        'sent_at' => 'datetime',
        'approved_at' => 'datetime',
        'is_immutable' => 'boolean'
    ];
    
    // Implementaciones específicas de template methods
    protected function getGeneratorClass(): string 
    {
        return CustomerQuotationPDFGenerator::class;
    }
    
    protected function getFilePathColumn(): string 
    {
        return 'pdf_s3_path';
    }
    
    protected function validateForGeneration(): void 
    {
        if ($this->project->productItems->isEmpty()) {
            throw new InvalidGenerationException('No products in project');
        }
    }
    
    protected function getRecipients(): Collection 
    {
        return $this->project->customer->contacts;
    }
    
    protected function getNotificationClass(): string 
    {
        return CustomerQuotationSentNotification::class;
    }
    
    protected function onApproved(User $approver): void 
    {
        $this->project->update(['status' => 'confirmed']);
        $this->project->createCommitmentBaseline();
    }
    
    protected function onRejected(User $rejector, string $reason): void 
    {
        $this->project->update(['status' => 'quoted']);
        // Posiblemente crear una nueva versión automáticamente
    }
    
    protected function getVersionGroupingKey(): string 
    {
        return 'project_id';
    }
    
    protected function getVersionGroupingValue(): mixed 
    {
        return $this->project_id;
    }
}
```

### Ejemplo: VirtualMockup Especializada

```php
class VirtualMockup extends Model implements RequiresApproval
{
    use ApprovableDocument,
        ImmutableAfterAction,
        HasExplicitVersioning,
        TrackableActivity;
    
    protected $fillable = [
        'mockup_number',
        'product_item_id',
        'version',
        'status',
        'design_files_s3_paths',
        'design_notes',
        'client_feedback',
        'approved_at',
        'is_immutable'
    ];
    
    protected $casts = [
        'design_files_s3_paths' => 'array',
        'approved_at' => 'datetime',
        'is_immutable' => 'boolean'
    ];
    
    // Comportamientos específicos
    protected function onApproved(User $approver): void 
    {
        $this->productItem->update(['status' => 'VM_APPROVED']);
    }
    
    protected function onRejected(User $rejector, string $reason): void 
    {
        $this->productItem->update(['status' => 'READY_FOR_SOURCING']);
        $this->createRevisionTask($reason);
    }
    
    protected function getVersionGroupingKey(): string 
    {
        return 'product_item_id';
    }
    
    protected function getVersionGroupingValue(): mixed 
    {
        return $this->product_item_id;
    }
    
    private function createRevisionTask(string $reason): void 
    {
        $this->productItem->assignTask(
            'Revisar diseño rechazado: ' . $reason,
            $this->creator,
            now()->addDays(2)
        );
    }
}
```

---

## Beneficios de la Clasificación por Traits

### 1. Reutilización de Código

```php
// El mismo trait puede usarse en múltiples entidades
class SupplierPurchaseOrder extends Model implements DocumentGenerator, SendableToRecipient 
{
    use GeneratesDocuments, SendableDocument, TrackableActivity;
}

class ProformaInvoice extends Model implements DocumentGenerator 
{
    use GeneratesDocuments, TrackableActivity;
}

class ImportCertificate extends Model implements DocumentGenerator, RequiresApproval 
{
    use GeneratesDocuments, ApprovableDocument, TrackableActivity;
}
```

### 2. Composición Flexible

```php
// Diferentes combinaciones según necesidades específicas
class ProductionOrder extends Model implements SendableToRecipient, RequiresApproval 
{
    use SendableDocument, ApprovableDocument, TrackableActivity;
    // No genera documentos, pero sí se envía y requiere aprobación
}

class QualityReport extends Model implements DocumentGenerator, ImmutableDocument 
{
    use GeneratesDocuments, ImmutableAfterAction, TrackableActivity;
    // Genera documentos inmutables pero no requiere envío específico
}
```

### 3. Evolución Sin Fricción

```php
// Agregar nuevos comportamientos fácilmente
class ProductSpecificationSheet extends Model 
{
    use TrackableActivity; // Inicialmente solo trackeable
}

// Más tarde, agregar capacidad de generación
class ProductSpecificationSheet extends Model implements DocumentGenerator 
{
    use TrackableActivity, GeneratesDocuments;
}

// Aún más tarde, agregar aprobación
class ProductSpecificationSheet extends Model implements DocumentGenerator, RequiresApproval 
{
    use TrackableActivity, GeneratesDocuments, ApprovableDocument;
}
```

### 4. Testing Granular

```php
// Tests específicos por comportamiento
class GeneratesDocumentsTraitTest extends TestCase 
{
    use RefreshDatabase;
    
    /** @test */
    public function it_generates_document_successfully() 
    {
        $quotation = CustomerQuotation::factory()->create();
        
        $filePath = $quotation->generateDocument();
        
        $this->assertNotNull($filePath);
        $this->assertNotNull($quotation->pdf_s3_path);
        $this->assertTrue(Storage::disk('s3')->exists($filePath));
    }
    
    /** @test */
    public function it_prevents_generation_without_valid_data() 
    {
        $quotation = CustomerQuotation::factory()->create(['project_id' => null]);
        
        $this->expectException(InvalidGenerationException::class);
        $quotation->generateDocument();
    }
}

class ApprovableDocumentTraitTest extends TestCase 
{
    /** @test */
    public function it_approves_document_successfully() 
    {
        $mockup = VirtualMockup::factory()->create(['status' => 'pending_approval']);
        $approver = User::factory()->create();
        
        $mockup->approve($approver, 'Looks great!');
        
        $this->assertEquals('approved', $mockup->status);
        $this->assertEquals($approver->id, $mockup->approved_by);
        $this->assertTrue($mockup->isImmutable());
    }
}
```

---

## Patterns Avanzados

### 1. Trait Conditions

```php
trait ConditionalBehavior 
{
    public function executeConditionalAction(string $action) 
    {
        $method = 'canExecute' . ucfirst($action);
        
        if (method_exists($this, $method) && $this->$method()) {
            $this->{'execute' . ucfirst($action)}();
        } else {
            throw new ActionNotAllowedException("Cannot execute {$action}");
        }
    }
}

class CustomerQuotation extends Model 
{
    use ConditionalBehavior;
    
    protected function canExecuteSend(): bool 
    {
        return $this->status === 'draft' && $this->hasGeneratedDocument();
    }
    
    protected function executeSend(): void 
    {
        $this->send();
    }
}
```

### 2. Trait Communication

```php
trait NotifiesOnStatusChange 
{
    protected static function bootNotifiesOnStatusChange(): void 
    {
        static::updating(function($model) {
            if ($model->isDirty('status')) {
                $model->notifyStatusChange(
                    $model->getOriginal('status'),
                    $model->status
                );
            }
        });
    }
    
    protected function notifyStatusChange(string $from, string $to): void 
    {
        $recipients = $this->getStatusChangeRecipients($from, $to);
        $notification = $this->getStatusChangeNotification($from, $to);
        
        foreach ($recipients as $recipient) {
            $recipient->notify($notification);
        }
    }
    
    abstract protected function getStatusChangeRecipients(string $from, string $to): Collection;
    abstract protected function getStatusChangeNotification(string $from, string $to): Notification;
}
```

### 3. Trait Dependencies

```php
trait RequiresDocumentGeneration 
{
    public function initializeRequiresDocumentGeneration(): void 
    {
        if (!in_array(GeneratesDocuments::class, class_uses($this))) {
            throw new TraitDependencyException(
                'RequiresDocumentGeneration trait requires GeneratesDocuments trait'
            );
        }
    }
    
    public function sendWithDocument(): void 
    {
        if (!$this->hasGeneratedDocument()) {
            $this->generateDocument();
        }
        
        $this->send();
    }
}
```

---

## Recomendaciones de Implementación

### 1. Orden de Traits

```php
class CustomerQuotation extends Model 
{
    // Orden recomendado:
    use HasFactory,           // Laravel traits primero
        SoftDeletes,
        GeneratesDocuments,   // Funcionalidad core
        SendableDocument,     // Funcionalidad específica
        ApprovableDocument,   // Funcionalidad específica  
        ImmutableAfterAction, // Comportamiento transversal
        HasExplicitVersioning,// Comportamiento transversal
        TrackableActivity;    // Logging/auditoría último
}
```

### 2. Documentación de Comportamientos

```php
/**
 * CustomerQuotation
 * 
 * Behaviors:
 * - DocumentGenerator: Generates PDF quotations
 * - SendableToRecipient: Can be sent to customer contacts
 * - RequiresApproval: Customer can approve/reject
 * - ImmutableAfterAction: Becomes read-only after sending
 * - HasExplicitVersioning: Supports multiple versions per project
 * - TrackableActivity: Logs all significant actions
 * 
 * Workflows:
 * 1. Generate → Send → Approve/Reject
 * 2. Generate → Send → Request Revision → Generate v2
 */
class CustomerQuotation extends Model 
{
    // Implementation...
}
```

### 3. Interface Segregation

```php
// Interfaces específicas por dominio
interface BusinessDocument extends DocumentGenerator, TrackableActivity 
{
    public function getBusinessValue(): Money;
    public function getBusinessOwner(): User;
}

interface ClientFacingDocument extends BusinessDocument, SendableToRecipient 
{
    public function getClientContacts(): Collection;
    public function getDeliveryMethod(): string;
}

interface InternalDocument extends BusinessDocument 
{
    public function getInternalRecipients(): Collection;
    public function getConfidentialityLevel(): string;
}

// Implementación específica
class CustomerQuotation extends Model implements ClientFacingDocument, RequiresApproval 
{
    use GeneratesDocuments, SendableDocument, ApprovableDocument, TrackableActivity;
}
```

---

## Comparación: Antes vs Después

### Antes (Clasificación Artificial)

```php
// Código duplicado y rígido
class CustomerQuotation extends Model 
{
    public function generatePDF() { /* implementación específica */ }
    public function sendToCustomer() { /* implementación específica */ }
    public function approve() { /* implementación específica */ }
}

class SupplierPurchaseOrder extends Model 
{
    public function generatePDF() { /* código similar duplicado */ }
    public function sendToSupplier() { /* código similar duplicado */ }
    public function confirm() { /* código similar duplicado */ }
}

class VirtualMockup extends Model 
{
    public function approve() { /* código similar duplicado */ }
    public function reject() { /* código similar duplicado */ }
    // No genera PDF - funcionalidad inconsistente
}
```

### Después (Trait-Based)

```php
// Código reutilizable y consistente
class CustomerQuotation extends Model implements DocumentGenerator, SendableToRecipient, RequiresApproval 
{
    use GeneratesDocuments, SendableDocument, ApprovableDocument;
    
    // Solo implementaciones específicas del dominio
    protected function getRecipients(): Collection { return $this->project->customer->contacts; }
    protected function onApproved(User $approver): void { $this->project->update(['status' => 'confirmed']); }
}

class SupplierPurchaseOrder extends Model implements DocumentGenerator, SendableToRecipient 
{
    use GeneratesDocuments, SendableDocument;
    
    protected function getRecipients(): Collection { return collect([$this->supplier]); }
}

class VirtualMockup extends Model implements RequiresApproval 
{
    use ApprovableDocument;
    
    protected function onApproved(User $approver): void { $this->productItem->update(['status' => 'VM_APPROVED']); }
}
```

---

## Conclusiones

### Beneficios Clave

1. **Reutilización**: Comportamientos compartidos entre entidades similares
2. **Flexibilidad**: Composición de funcionalidades según necesidades específicas
3. **Mantenibilidad**: Cambios en un trait se propagan automáticamente
4. **Testabilidad**: Tests granulares por comportamiento
5. **Evolución**: Fácil agregar/quitar comportamientos sin refactoring masivo
6. **Consistencia**: Mismo comportamiento garantizado entre entidades

### Principios de Diseño

1. **Single Responsibility**: Cada trait tiene una responsabilidad específica
2. **Open/Closed**: Fácil extensión sin modificación de código existente
3. **Interface Segregation**: Interfaces específicas por tipo de comportamiento
4. **Dependency Inversion**: Dependencias en abstracciones, no en concreciones

### Recomendaciones Finales

1. **Empezar simple**: Implementar traits básicos primero
2. **Documentar comportamientos**: Claridad sobre qué hace cada trait
3. **Testear individualmente**: Tests específicos por trait
4. **Evitar over-engineering**: No crear traits para comportamientos únicos
5. **Mantener cohesión**: Traits deben ser conceptualmente cohesivos

**La clasificación trait-based es superior a categorías artificiales porque se enfoca en comportamientos reales del dominio de negocio, promoviendo código más mantenible, testeable y reutilizable.**