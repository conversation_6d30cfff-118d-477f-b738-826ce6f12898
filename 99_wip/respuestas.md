# Respuestas y Alternativas a Conceptos de Diseño

> Respuestas técnicas a las ideas planteadas en `ideas.md`, basadas en el análisis de la documentación de PromoSmart y mejores prácticas arquitectónicas.

---

## 1. Artefactos con Nombres Especiales

### Problema
¿Cómo implementar artefactos con nomenclatura especial como `PROY_2025_001`, `OC_2025_001`?

### Solución Recomendada: Service + Enum Pattern

```php
enum DocumentType: string 
{
    case PROJECT = 'PROY';
    case PURCHASE_ORDER = 'OC';
    case QUOTATION = 'COT';
    case IMPORT_SHIPMENT = 'ENV';
    
    public function getPrefix(): string 
    {
        return $this->value;
    }
}

class DocumentNumberService 
{
    public function generate(DocumentType $type, int $year = null): string 
    {
        $year = $year ?? date('Y');
        $sequence = $this->getNextSequence($type, $year);
        
        return sprintf('%s_%d_%03d', 
            $type->getPrefix(), 
            $year, 
            $sequence
        );
    }
}
```

### Implementación en Filament
- **Observer Pattern**: Auto-generar en `creating()` event
- **Immutable**: Una vez asignado, no cambia
- **Unique Index**: BD garantiza unicidad
- **Display**: Mostrar en lugar del ID interno en interfaces

---

## 2. Registro Histórico de Entidades

### Problema
¿Qué se registra? ¿Copias inmutables? ¿Archivos adjuntos?

### Enfoque: Versionado Selectivo (Anti Over-Engineering)

**Principio:** Solo versionar cambios de **impacto comercial**, no todos los cambios.

#### Entidades que SÍ requieren versionado:
- **ProjectItem**: Re-cotizaciones, cambios post-compromiso
- **CustomerQuotation**: Versiones de cotizaciones al cliente
- **ProjectBaseline**: Línea base financiera al momento del compromiso

#### Entidades que NO requieren versionado:
- **Project**: Solo auditoría básica (created_at, updated_at)
- **Customer/Supplier**: Cambios simples, no críticos
- **ImportShipmentRecord**: Estados logísticos, no versiones

### Implementación Técnica

```php
// Trait para entidades versionables
trait HasVersioning 
{
    public function versions() 
    {
        return $this->morphMany(EntityVersion::class, 'versionable');
    }
    
    public function createVersion(string $reason = null) 
    {
        $this->versions()->create([
            'version_number' => $this->getNextVersionNumber(),
            'data_snapshot' => $this->toArray(),
            'created_by' => auth()->id(),
            'reason' => $reason
        ]);
    }
}

// Modelo genérico para versiones
class EntityVersion extends Model 
{
    protected $casts = [
        'data_snapshot' => 'array'
    ];
}
```

#### Archivos Adjuntos
- **Estrategia**: Inmutabilidad por referencia
- **Storage**: AWS S3 con versionado automático
- **Approach**: Nunca eliminar, solo marcar como obsoleto
- **File Path Pattern**: `{project_id}/{entity_type}/{version}/{filename}`

---

## 3. Value Objects

### ¿Para qué sirven?
Encapsulan **conceptos de dominio** que:
- No tienen identidad propia (no necesitan ID)
- Son inmutables
- Tienen reglas de validación específicas
- Se comparan por valor, no por referencia

### Value Objects Recomendados para PromoSmart

#### Money (Ya documentado en ADR-011)
```php
class Money 
{
    public function __construct(
        private string $amount,
        private Currency $currency
    ) {
        $this->validateAmount($amount);
    }
    
    public function add(Money $other): Money 
    {
        $this->ensureSameCurrency($other);
        return new self(
            bcadd($this->amount, $other->amount, 2),
            $this->currency
        );
    }
}
```

#### ProductSpecifications
```php
class ProductSpecifications 
{
    public function __construct(
        private array $specifications,
        private ProductSubcategory $subcategory
    ) {
        $this->validate();
    }
    
    private function validate(): void 
    {
        $required = $this->subcategory->getRequiredSpecifications();
        // Validar que todas las specs requeridas estén presentes
    }
}
```

#### Timeline
```php
class Timeline 
{
    public function __construct(
        private CarbonImmutable $startDate,
        private CarbonImmutable $endDate
    ) {
        if ($startDate->gte($endDate)) {
            throw new InvalidTimelineException();
        }
    }
    
    public function getDurationInDays(): int 
    {
        return $this->startDate->diffInDays($this->endDate);
    }
}
```

---

## 4. DTOs (Data Transfer Objects)

### ¿Para qué sirven?
- **Contratos claros** entre capas (UI ↔ Service ↔ Repository)
- **Validación centralizada** de datos de entrada
- **Inmutabilidad** de datos en tránsito
- **Transformation** de datos para APIs o reportes

### DTOs Recomendados

#### CreateProjectItemRequest
```php
class CreateProjectItemRequest extends DTO 
{
    public function __construct(
        public readonly int $projectId,
        public readonly int $subcategoryId,
        public readonly array $specifications,
        public readonly int $quantity,
        public readonly ?string $description = null
    ) {}
    
    public static function fromArray(array $data): self 
    {
        return new self(
            projectId: $data['project_id'],
            subcategoryId: $data['subcategory_id'],
            specifications: $data['specifications'],
            quantity: $data['quantity'],
            description: $data['description'] ?? null
        );
    }
}
```

#### QuotationData
```php
class QuotationData extends DTO 
{
    public function __construct(
        public readonly array $items,
        public readonly Money $totalAmount,
        public readonly CarbonImmutable $validUntil,
        public readonly array $terms
    ) {}
}
```

---

## 5. Cotizaciones: Diseño de Entidad

### Problema Complejo
¿La cotización es entidad separada? ¿Parte de ProductItem? ¿Inmutable?

### Solución: Entidad Separada + Snapshot Pattern

#### Justificación
1. **Múltiples cotizaciones** por ProductItem (diferentes proveedores)
2. **Versionado independiente** de cotizaciones
3. **Comparación** lado a lado
4. **Audit trail** de decisiones comerciales

#### Diseño Propuesto

```php
// Entidad principal - Mutable para workflow
class SupplierQuotation extends Model 
{
    protected $fillable = [
        'product_item_id',
        'supplier_id', 
        'unit_price',
        'currency',
        'lead_time_days',
        'moq',
        'status', // draft, sent_to_client, selected, rejected
        'valid_until'
    ];
    
    // Relación a snapshots inmutables
    public function snapshots() 
    {
        return $this->hasMany(QuotationSnapshot::class);
    }
}

// Snapshots inmutables para artefactos
class QuotationSnapshot extends Model 
{
    protected $fillable = [
        'quotation_id',
        'snapshot_data', // JSON inmutable
        'version',
        'created_reason' // "initial", "price_change", "terms_update"
    ];
    
    protected $casts = [
        'snapshot_data' => 'array'
    ];
}
```

#### Workflow Propuesto
1. **Cotización mutable** para trabajo diario
2. **Snapshot automático** al cambiar status a "sent_to_client"
3. **Artefactos generados** desde snapshots (inmutables)
4. **Comparación** usando datos actuales (mutables)

---

## 6. Comentarios y Tareas

### Feature Sugerida: Activity Stream Pattern

#### Implementación Polimórfica
```php
class Activity extends Model 
{
    protected $fillable = [
        'subject_type', // ProductItem, Project, Quotation
        'subject_id',
        'activity_type', // comment, task, status_change, approval
        'description',
        'metadata', // JSON adicional
        'user_id',
        'due_date' // Para tareas
    ];
    
    public function subject() 
    {
        return $this->morphTo();
    }
}

// Trait para entidades que tienen actividades
trait HasActivities 
{
    public function activities() 
    {
        return $this->morphMany(Activity::class, 'subject')
                   ->orderBy('created_at', 'desc');
    }
    
    public function addComment(string $comment): Activity 
    {
        return $this->activities()->create([
            'activity_type' => 'comment',
            'description' => $comment,
            'user_id' => auth()->id()
        ]);
    }
}
```

#### Integración con Filament
- **Relation Manager** para mostrar timeline
- **Widgets** para tareas pendientes por usuario
- **Notifications** para menciones (@usuario)

---

## 7. Archivos en Entidades

### Problema
Imágenes referenciales, adjuntos en cotizaciones, documentos técnicos.

### Solución: Polimorphic File Management + S3

#### Diseño de Entidades
```php
class Attachment extends Model 
{
    protected $fillable = [
        'attachable_type', // ProductItem, Quotation, Project
        'attachable_id',
        'file_name',
        'file_path', // S3 path
        'file_type', // image, document, cad_file
        'file_size',
        'uploaded_by',
        'is_active' // Soft delete sin perder referencia
    ];
    
    public function attachable() 
    {
        return $this->morphTo();
    }
}

trait HasAttachments 
{
    public function attachments() 
    {
        return $this->morphMany(Attachment::class, 'attachable')
                   ->where('is_active', true);
    }
    
    public function images() 
    {
        return $this->attachments()->where('file_type', 'image');
    }
}
```

#### Storage Strategy
- **S3 Bucket**: `promosmart-files`
- **Path Structure**: `{env}/{entity_type}/{entity_id}/{timestamp}_{filename}`
- **CDN**: CloudFront para acceso optimizado
- **Security**: Pre-signed URLs para acceso temporal

---

## 8. Registro Histórico vs Versionamiento

### Diferencias Conceptuales

| Concepto | Propósito | Cuándo Usar | Implementación |
|----------|-----------|-------------|----------------|
| **Registro Histórico** | Auditoría, compliance | Todos los cambios | created_at, updated_at, audit log |
| **Versionamiento** | Recuperación, comparación | Cambios significativos | Snapshots inmutables |

### Matriz de Aplicación por Entidad

| Entidad | Registro Histórico | Versionamiento | Justificación |
|---------|-------------------|----------------|---------------|
| **Project** | ✅ Básico | ❌ | Cambios simples, no críticos |
| **ProductItem** | ✅ Completo | ✅ Selectivo | Crítico para negocio, re-cotizaciones |
| **Customer** | ✅ Básico | ❌ | Datos maestros, cambios menores |
| **Supplier** | ✅ Básico | ❌ | Datos maestros, cambios menores |
| **Quotation** | ✅ Completo | ✅ Completo | Impacto comercial directo |
| **ImportShipmentRecord** | ✅ Básico | ❌ | Estados logísticos, no versiones |

### Implementación Técnica

#### Registro Histórico (Todos)
```php
// Usar paquete spatie/laravel-activitylog
use Spatie\Activitylog\Traits\LogsActivity;

class ProductItem extends Model 
{
    use LogsActivity;
    
    protected static $logAttributes = ['*'];
    protected static $logOnlyDirty = true;
}
```

#### Versionamiento (Selectivo)
```php
// Observer para crear versiones automáticamente
class ProductItemObserver 
{
    public function updating(ProductItem $item): void 
    {
        if ($this->shouldCreateVersion($item)) {
            $item->createVersion('Specification changes');
        }
    }
    
    private function shouldCreateVersion(ProductItem $item): bool 
    {
        $criticalFields = ['specifications', 'quantity', 'unit_price'];
        return collect($criticalFields)->some(fn($field) => $item->isDirty($field));
    }
}
```

---

## Recomendaciones de Implementación

### Orden de Prioridad
1. **Value Objects** (Money, ProductSpecifications) - Base sólida
2. **DTOs** para requests - Contratos claros
3. **Artefactos con nomenclatura** - Funcionalidad visible
4. **Versionamiento selectivo** - ProductItem y Quotations
5. **Attachments polimórficos** - Gestión de archivos
6. **Activity Stream** - Comentarios y tareas
7. **Registro histórico completo** - Compliance y auditoría

### Principios Arquitectónicos
- **Anti Over-Engineering**: Solo versionar lo crítico
- **Immutability**: Snapshots para artefactos
- **Polymorphism**: Reutilizar patrones (attachments, activities)
- **Domain-Driven**: Value Objects para conceptos de negocio
- **TALL Stack**: Mantener coherencia con Filament y Laravel