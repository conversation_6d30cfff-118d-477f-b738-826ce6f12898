# Respuestas Finales: Conceptos de Implementación PromoSmart

> Síntesis de las mejores prácticas arquitectónicas combinando adherencia documental, implementación práctica y claridad conceptual para el sistema PromoSmart.

---

## 1. Implementación de Artefactos con Nomenclatura Especial

### Contexto Documental
**RF-06.1** establece que los artefactos se generan usando `data snapshots`, asegurando inmutabilidad. El **08_Diccionario_Negocio_Sistema.md** define los tipos de documentos principales: Customer Quotation, Supplier Purchase Order, etc.

### Solución: Service + Enum Pattern + Counter Centralizado

```php
enum DocumentType: string 
{
    case PROJECT = 'PROY';
    case CUSTOMER_QUOTATION = 'COT';
    case SUPPLIER_PURCHASE_ORDER = 'OCP';
    case IMPORT_SHIPMENT = 'ENV';
    
    public function getPrefix(): string 
    {
        return $this->value;
    }
    
    public function getDisplayName(): string 
    {
        return match($this) {
            self::PROJECT => 'Proyecto',
            self::CUSTOMER_QUOTATION => 'Cotización Cliente',
            self::SUPPLIER_PURCHASE_ORDER => 'Orden Compra Proveedor',
            self::IMPORT_SHIPMENT => 'Envío Importación'
        };
    }
}

class DocumentNumberService 
{
    public function generate(DocumentType $type, int $year = null): string 
    {
        $year = $year ?? date('Y');
        
        DB::transaction(function() use ($type, $year, &$sequence) {
            $counter = DB::table('document_sequences')
                ->where('type', $type->value)
                ->where('year', $year)
                ->lockForUpdate()
                ->first();
                
            if (!$counter) {
                DB::table('document_sequences')->insert([
                    'type' => $type->value,
                    'year' => $year,
                    'last_value' => 1
                ]);
                $sequence = 1;
            } else {
                $sequence = $counter->last_value + 1;
                DB::table('document_sequences')
                    ->where('id', $counter->id)
                    ->update(['last_value' => $sequence]);
            }
        });
        
        return sprintf('%s_%d_%03d', $type->getPrefix(), $year, $sequence);
    }
}
```

### Implementación en Filament
- **Observer Pattern**: Auto-generar en `creating()` event
- **Immutable Display**: Mostrar número en lugar de ID interno
- **Database**: Unique constraint en número generado
- **Storage**: Referencias almacenadas en S3 con path estructurado

---

## 2. Registro Histórico vs. Versionamiento: Enfoque Selectivo

### Filosofía Documental
**09_Auditoria_y_Trazabilidad.md** establece **"Auditoría Selectiva con Propósito"** - no se registra todo, solo lo que tiene valor de negocio, evitando over-engineering.

### Diferencias Conceptuales Clarificadas

| Concepto | Propósito | Pregunta que Responde | Implementación |
|----------|-----------|----------------------|----------------|
| **Registro Histórico** | Auditoría operativa | "¿Qué pasó y cuándo?" | Activity log cronológico |
| **Versionamiento** | Recuperación de estado | "¿Cómo se veía en ese momento?" | Snapshots JSON inmutables |

### Matriz de Aplicación por Entidad

| Entidad | Registro Histórico | Versionamiento | Justificación |
|---------|-------------------|----------------|---------------|
| **Project** | ✅ Hitos principales | ✅ Baseline únicamente | Línea base crítica, hitos de negocio |
| **ProductItem** | ✅ Esencial (RF-18) | ✅ Selectivo | Centro del proceso, cambios críticos |
| **CustomerQuotation** | ✅ Mínimo | ✅ Cada versión | Cada envío = nueva versión inmutable |
| **SupplierQuotation** | ✅ Básico | ❌ | Datos de sourcing, no crítico versionar |
| **Customer/Supplier** | ✅ Básico | ❌ | Datos maestros, cambios menores |
| **ImportShipmentRecord** | ✅ Estados | ❌ | Solo seguimiento logístico |

### Implementación Técnica

#### Registro Histórico (Activity Log)
```php
// Usando spatie/laravel-activitylog + personalización
trait HasBusinessActivityLog 
{
    use LogsActivity;
    
    protected static $logAttributes = ['*'];
    protected static $logOnlyDirty = true;
    
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly($this->getLoggableAttributes())
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
    
    // Sobrescribir en cada modelo
    protected function getLoggableAttributes(): array 
    {
        return ['*']; // Por defecto todo, personalizar por entidad
    }
}
```

#### Versionamiento Selectivo
```php
trait HasSelectiveVersioning 
{
    public function versions() 
    {
        return $this->morphMany(EntityVersion::class, 'versionable')
                   ->orderBy('version_number', 'desc');
    }
    
    public function createVersion(string $reason = null, array $criticalData = null): EntityVersion 
    {
        return $this->versions()->create([
            'version_number' => $this->getNextVersionNumber(),
            'data_snapshot' => $criticalData ?? $this->getVersionableData(),
            'created_by' => auth()->id(),
            'reason' => $reason,
            'created_at' => now()
        ]);
    }
    
    // Sobrescribir en cada modelo para definir qué datos versionar
    protected function getVersionableData(): array 
    {
        return $this->toArray();
    }
}

// Observer para versionado automático
class ProductItemObserver 
{
    public function updating(ProductItem $item): void 
    {
        if ($this->shouldCreateVersion($item)) {
            $reason = $this->getVersionReason($item);
            $item->createVersion($reason);
        }
    }
    
    private function shouldCreateVersion(ProductItem $item): bool 
    {
        $criticalFields = ['specifications', 'quantity', 'unit_price', 'status'];
        return collect($criticalFields)->some(fn($field) => $item->isDirty($field));
    }
}
```

#### Archivos Adjuntos en Versiones
```php
class EntityVersion extends Model 
{
    protected $casts = [
        'data_snapshot' => 'array',
        'file_references' => 'array' // URLs S3 inmutables por versión
    ];
    
    public function getFileUrl(string $fileName): ?string 
    {
        $references = $this->file_references ?? [];
        return $references[$fileName] ?? null;
    }
}
```

---

## 3. Value Objects: Encapsulación de Conceptos de Dominio

### Propósito y Beneficios
Los Value Objects encapsulan **conceptos de dominio** sin identidad propia, proporcionando inmutabilidad y validaciones específicas, evitando el uso de tipos primitivos para conceptos complejos.

### Value Objects Recomendados

#### Money (Crítico - ADR-011)
```php
class Money 
{
    public function __construct(
        private string $amount,
        private Currency $currency
    ) {
        $this->validateAmount($amount);
    }
    
    public function add(Money $other): Money 
    {
        $this->ensureSameCurrency($other);
        return new self(
            bcadd($this->amount, $other->amount, 2),
            $this->currency
        );
    }
    
    public function toArray(): array 
    {
        return [
            'amount' => $this->amount,
            'currency' => $this->currency->value
        ];
    }
    
    public static function fromArray(array $data): self 
    {
        return new self($data['amount'], Currency::from($data['currency']));
    }
}

enum Currency: string 
{
    case USD = 'USD';
    case CLP = 'CLP';
    case EUR = 'EUR';
}
```

#### ProductSpecifications
```php
class ProductSpecifications 
{
    public function __construct(
        private array $specifications,
        private ProductSubcategory $subcategory
    ) {
        $this->validate();
    }
    
    private function validate(): void 
    {
        $required = $this->subcategory->getRequiredSpecifications();
        $missing = array_diff($required, array_keys($this->specifications));
        
        if (!empty($missing)) {
            throw new InvalidSpecificationException(
                "Missing required specifications: " . implode(', ', $missing)
            );
        }
    }
    
    public function get(string $key): mixed 
    {
        return $this->specifications[$key] ?? null;
    }
    
    public function toArray(): array 
    {
        return $this->specifications;
    }
}
```

#### Timeline
```php
class Timeline 
{
    public function __construct(
        private CarbonImmutable $startDate,
        private CarbonImmutable $endDate
    ) {
        if ($startDate->gte($endDate)) {
            throw new InvalidTimelineException('Start date must be before end date');
        }
    }
    
    public function getDurationInDays(): int 
    {
        return $this->startDate->diffInDays($this->endDate);
    }
    
    public function isOverdue(): bool 
    {
        return $this->endDate->isPast();
    }
}
```

---

## 4. DTOs: Contratos Claros Entre Capas

### Propósito
Los DTOs transfieren datos entre capas con **estructura definida por el consumidor**, no por la base de datos, proporcionando contratos claros y optimización de cargas.

### DTOs por Contexto de Uso

#### Requests (Input DTOs)
```php
class CreateProductItemRequest extends DTO 
{
    public function __construct(
        public readonly int $projectId,
        public readonly int $subcategoryId,
        public readonly array $specifications,
        public readonly int $quantity,
        public readonly ?string $description = null
    ) {
        $this->validate();
    }
    
    public static function fromArray(array $data): self 
    {
        return new self(
            projectId: $data['project_id'],
            subcategoryId: $data['subcategory_id'],
            specifications: $data['specifications'],
            quantity: $data['quantity'],
            description: $data['description'] ?? null
        );
    }
    
    private function validate(): void 
    {
        if ($this->quantity <= 0 || $this->quantity > 100000) {
            throw new InvalidQuantityException();
        }
    }
}
```

#### Views (Output DTOs)
```php
class ProjectSummaryDTO extends DTO 
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly string $customerName,
        public readonly ProjectStatus $status,
        public readonly ?string $profitabilityIndicator = null
    ) {}
    
    public static function fromProject(Project $project): self 
    {
        return new self(
            id: $project->document_number, // PROY_2025_001
            name: $project->name,
            customerName: $project->customer->name,
            status: $project->status,
            profitabilityIndicator: $project->getProfitabilityIndicator()
        );
    }
}

class ProductItemDetailsDTO extends DTO 
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly ProductSpecifications $specifications,
        public readonly array $quotations,
        public readonly array $approvalHistory,
        public readonly ProductItemStatus $status
    ) {}
}
```

---

## 5. Cotizaciones: Separación Clara de Responsabilidades

### Clarificación Conceptual
La documentación (**08_Diccionario_Negocio_Sistema.md**) distingue claramente:
- **Customer Quotation**: Propuesta comercial formal enviada al cliente
- **Supplier Quotation**: Cotización recibida del proveedor (proceso de sourcing)

### Modelo de Entidades Separadas

#### SupplierQuotation (Sourcing)
```php
class SupplierQuotation extends Model 
{
    use HasBusinessActivityLog;
    
    protected $fillable = [
        'product_item_id',
        'supplier_id',
        'unit_price',
        'currency',
        'lead_time_days',
        'moq',
        'status', // draft, received, under_review, selected, rejected
        'valid_until',
        'notes'
    ];
    
    protected $casts = [
        'unit_price' => 'decimal:2',
        'currency' => Currency::class,
        'status' => SupplierQuotationStatus::class,
        'valid_until' => 'datetime'
    ];
    
    // Relaciones
    public function productItem() 
    {
        return $this->belongsTo(ProductItem::class);
    }
    
    public function supplier() 
    {
        return $this->belongsTo(Supplier::class);
    }
}

enum SupplierQuotationStatus: string 
{
    case DRAFT = 'draft';
    case RECEIVED = 'received';
    case UNDER_REVIEW = 'under_review';
    case SELECTED = 'selected';
    case REJECTED = 'rejected';
}
```

#### CustomerQuotation (Ventas)
```php
class CustomerQuotation extends Model 
{
    use HasBusinessActivityLog, HasSelectiveVersioning;
    
    protected $fillable = [
        'project_id',
        'version_number',
        'total_amount',
        'currency',
        'status', // draft, sent_to_client, approved, rejected, expired
        'valid_until',
        'terms_and_conditions',
        'document_number' // COT_2025_001
    ];
    
    protected $casts = [
        'total_amount' => 'decimal:2',
        'currency' => Currency::class,
        'status' => CustomerQuotationStatus::class,
        'valid_until' => 'datetime',
        'terms_and_conditions' => 'array'
    ];
    
    // Observer auto-genera document_number
    protected static function boot() 
    {
        parent::boot();
        
        static::creating(function (CustomerQuotation $quotation) {
            $quotation->document_number = app(DocumentNumberService::class)
                ->generate(DocumentType::CUSTOMER_QUOTATION);
        });
    }
    
    // Versionamiento automático al enviar
    public function sendToClient(): void 
    {
        $this->createVersion('Sent to client');
        $this->status = CustomerQuotationStatus::SENT_TO_CLIENT;
        $this->save();
    }
    
    // Snapshot para artefactos
    protected function getVersionableData(): array 
    {
        return [
            'project_data' => $this->project->getQuotationSnapshot(),
            'items_data' => $this->project->productItems->map->getQuotationSnapshot(),
            'quotation_data' => $this->toArray()
        ];
    }
}

enum CustomerQuotationStatus: string 
{
    case DRAFT = 'draft';
    case SENT_TO_CLIENT = 'sent_to_client';
    case APPROVED = 'approved';
    case REJECTED = 'rejected';
    case EXPIRED = 'expired';
}
```

### Workflow Separado pero Coordinado

1. **Sourcing (SupplierQuotation)**:
   - Analista de Adquisiciones gestiona múltiples cotizaciones de proveedores
   - Comparación lado a lado
   - Selección de mejor opción

2. **Ventas (CustomerQuotation)**:
   - Analista de Ventas genera cotización formal al cliente
   - Usa datos de SupplierQuotation seleccionada + margen
   - Cada envío = nueva versión inmutable

---

## 6. Comentarios y Tareas: Activity Stream Unificado

### Contexto Documental
**RF-14: Colaboración en Contexto** especifica esta funcionalidad. La **ProductItem View** en **07_Interfaces_y_Flujos.md** visualiza el feed de actividad.

### Implementación Polimórfica Unificada

```php
class Activity extends Model 
{
    protected $fillable = [
        'subject_type', // ProductItem, Project, CustomerQuotation
        'subject_id',
        'activity_type', // comment, task, status_change, approval, mention
        'title',
        'description',
        'metadata', // JSON para datos específicos del tipo
        'user_id',
        'assignee_id', // Para tareas
        'due_date', // Para tareas
        'completed_at', // Para tareas
        'is_system_generated' // true para cambios automáticos
    ];
    
    protected $casts = [
        'metadata' => 'array',
        'due_date' => 'datetime',
        'completed_at' => 'datetime',
        'is_system_generated' => 'boolean'
    ];
    
    // Relaciones polimórficas
    public function subject() 
    {
        return $this->morphTo();
    }
    
    public function user() 
    {
        return $this->belongsTo(User::class);
    }
    
    public function assignee() 
    {
        return $this->belongsTo(User::class, 'assignee_id');
    }
    
    // Scopes
    public function scopeComments($query) 
    {
        return $query->where('activity_type', 'comment');
    }
    
    public function scopeTasks($query) 
    {
        return $query->where('activity_type', 'task');
    }
    
    public function scopePendingTasks($query) 
    {
        return $query->where('activity_type', 'task')
                    ->whereNull('completed_at');
    }
}

// Trait para entidades que tienen actividades
trait HasActivities 
{
    public function activities() 
    {
        return $this->morphMany(Activity::class, 'subject')
                   ->orderBy('created_at', 'desc');
    }
    
    public function comments() 
    {
        return $this->activities()->comments();
    }
    
    public function tasks() 
    {
        return $this->activities()->tasks();
    }
    
    public function addComment(string $comment): Activity 
    {
        $activity = $this->activities()->create([
            'activity_type' => 'comment',
            'description' => $comment,
            'user_id' => auth()->id()
        ]);
        
        // Procesar menciones
        $this->processMentions($comment, $activity);
        
        return $activity;
    }
    
    public function assignTask(string $title, User $assignee, CarbonInterface $dueDate = null): Activity 
    {
        return $this->activities()->create([
            'activity_type' => 'task',
            'title' => $title,
            'user_id' => auth()->id(),
            'assignee_id' => $assignee->id,
            'due_date' => $dueDate
        ]);
    }
    
    private function processMentions(string $content, Activity $activity): void 
    {
        preg_match_all('/@(\w+)/', $content, $matches);
        
        foreach ($matches[1] as $username) {
            $user = User::where('username', $username)->first();
            if ($user) {
                $this->activities()->create([
                    'activity_type' => 'mention',
                    'description' => "Mentioned in: {$content}",
                    'user_id' => auth()->id(),
                    'assignee_id' => $user->id,
                    'metadata' => ['original_activity_id' => $activity->id]
                ]);
            }
        }
    }
}
```

### Observer para Actividades Automáticas

```php
class ProductItemObserver 
{
    public function updated(ProductItem $item): void 
    {
        if ($item->isDirty('status')) {
            $item->activities()->create([
                'activity_type' => 'status_change',
                'title' => 'Status Updated',
                'description' => "Status changed from {$item->getOriginal('status')} to {$item->status}",
                'user_id' => auth()->id(),
                'is_system_generated' => false,
                'metadata' => [
                    'old_status' => $item->getOriginal('status'),
                    'new_status' => $item->status
                ]
            ]);
            
            // Auto-crear tareas según el nuevo estado
            $this->createAutoTasks($item);
        }
    }
    
    private function createAutoTasks(ProductItem $item): void 
    {
        $taskRules = [
            'PendingVmApproval' => ['role' => 'design_analyst', 'title' => 'Create Virtual Mockup'],
            'PendingPpsApproval' => ['role' => 'import_analyst', 'title' => 'Send Physical Sample'],
        ];
        
        if (isset($taskRules[$item->status->value])) {
            $rule = $taskRules[$item->status->value];
            $assignee = User::role($rule['role'])->first();
            
            if ($assignee) {
                $item->assignTask($rule['title'], $assignee, now()->addDays(3));
            }
        }
    }
}
```

---

## 7. Gestión de Archivos: S3 + Polimorfismo

### Contexto Documental
**RF-06.2** especifica almacenamiento seguro y organizado. La implementación debe soportar versionado de archivos para trazabilidad.

### Solución: Attachments Polimórficos + S3

```php
class Attachment extends Model 
{
    protected $fillable = [
        'attachable_type',
        'attachable_id',
        'file_name',
        'original_name',
        's3_path',
        'file_type', // image, document, cad_file, sample_photo
        'mime_type',
        'file_size',
        'uploaded_by',
        'version', // Para versionado de archivos
        'is_active', // Soft delete manteniendo referencia
        'metadata' // JSON para datos específicos por tipo
    ];
    
    protected $casts = [
        'file_size' => 'integer',
        'is_active' => 'boolean',
        'metadata' => 'array'
    ];
    
    public function attachable() 
    {
        return $this->morphTo();
    }
    
    public function uploader() 
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
    
    // Generar URL pre-firmada para descarga segura
    public function getDownloadUrl(int $expiresInMinutes = 60): string 
    {
        return Storage::disk('s3')->temporaryUrl(
            $this->s3_path,
            now()->addMinutes($expiresInMinutes)
        );
    }
    
    // Scopes por tipo
    public function scopeImages($query) 
    {
        return $query->where('file_type', 'image');
    }
    
    public function scopeDocuments($query) 
    {
        return $query->where('file_type', 'document');
    }
}

trait HasAttachments 
{
    public function attachments() 
    {
        return $this->morphMany(Attachment::class, 'attachable')
                   ->where('is_active', true)
                   ->orderBy('created_at', 'desc');
    }
    
    public function images() 
    {
        return $this->attachments()->images();
    }
    
    public function documents() 
    {
        return $this->attachments()->documents();
    }
    
    public function attachFile(
        UploadedFile $file, 
        string $type = 'document',
        array $metadata = []
    ): Attachment {
        $fileName = $this->generateFileName($file);
        $s3Path = $this->generateS3Path($fileName);
        
        // Upload a S3
        Storage::disk('s3')->putFileAs(
            dirname($s3Path),
            $file,
            basename($s3Path)
        );
        
        return $this->attachments()->create([
            'file_name' => $fileName,
            'original_name' => $file->getClientOriginalName(),
            's3_path' => $s3Path,
            'file_type' => $type,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'uploaded_by' => auth()->id(),
            'version' => $this->getNextFileVersion($file->getClientOriginalName()),
            'metadata' => $metadata
        ]);
    }
    
    private function generateS3Path(string $fileName): string 
    {
        $entityType = class_basename($this);
        $entityId = $this->getKey();
        $date = now()->format('Y/m');
        
        return "promosmart/{$entityType}/{$entityId}/{$date}/{$fileName}";
    }
    
    private function generateFileName(UploadedFile $file): string 
    {
        $timestamp = now()->format('YmdHis');
        $extension = $file->getClientOriginalExtension();
        $hash = substr(md5($file->getClientOriginalName()), 0, 8);
        
        return "{$timestamp}_{$hash}.{$extension}";
    }
}
```

### Service para Upload Directo

```php
class FileUploadService 
{
    public function generatePresignedUploadUrl(
        string $entityType,
        string $entityId,
        string $fileName,
        int $expiresInMinutes = 15
    ): array {
        $s3Path = "promosmart/{$entityType}/{$entityId}/" . now()->format('Y/m') . "/{$fileName}";
        
        $client = Storage::disk('s3')->getClient();
        $cmd = $client->getCommand('PutObject', [
            'Bucket' => config('filesystems.disks.s3.bucket'),
            'Key' => $s3Path,
            'ContentType' => $this->guessMimeType($fileName)
        ]);
        
        $presignedUrl = (string) $client->createPresignedRequest(
            $cmd,
            "+{$expiresInMinutes} minutes"
        )->getUri();
        
        return [
            'upload_url' => $presignedUrl,
            's3_path' => $s3Path,
            'expires_at' => now()->addMinutes($expiresInMinutes)
        ];
    }
}
```

---

## Matriz de Prioridades de Implementación

### Fase 1: Fundaciones (Crítico)
1. **Value Objects (Money, ProductSpecifications)** - Base sólida para lógica de negocio
2. **Document Number Service** - Nomenclatura visible al usuario
3. **Activity Log básico** - Auditoría esencial (RF-18)

### Fase 2: Funcionalidades Core (Alto)
4. **DTOs para requests principales** - Contratos claros en APIs
5. **CustomerQuotation + SupplierQuotation separadas** - Claridad de negocio
6. **Versionamiento selectivo** - ProductItem y CustomerQuotation únicamente

### Fase 3: Colaboración (Medio)
7. **Activity Stream completo** - Comentarios y tareas
8. **Attachments polimórficos** - Gestión de archivos
9. **Mention system** - Notificaciones @usuario

### Fase 4: Optimización (Bajo)
10. **File versioning automático** - Trazabilidad de archivos
11. **Pre-signed URLs** - Performance de uploads
12. **Metadata extensible** - Futuras funcionalidades

---

## Principios Arquitectónicos Aplicados

### Anti Over-Engineering
- **Versionado selectivo**: Solo entidades críticas (ProductItem, CustomerQuotation)
- **Activity log simplificado**: No event sourcing completo
- **DTOs específicos**: Solo para casos de uso claros

### Adherencia Documental
- **Referencias RF explícitas**: Cada decisión mapea a requerimiento
- **Terminología consistente**: CustomerQuotation vs SupplierQuotation
- **Filosofía de snapshots**: Inmutabilidad para artefactos

### TALL Stack Integration
- **Filament Observers**: Auto-generación de números
- **Laravel Enums**: Type-safety para estados
- **Livewire Components**: Real-time activity feeds
- **Alpine.js**: Interacciones de UI fluidas

### Domain-Driven Design
- **Value Objects**: Conceptos de dominio encapsulados
- **Aggregates**: Project como aggregate root
- **Ubiquitous Language**: Términos de negocio en código