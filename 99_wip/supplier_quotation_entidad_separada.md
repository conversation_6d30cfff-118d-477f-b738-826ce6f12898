# SupplierQuotation como Entidad Separada: Análisis Arquitectónico

> **Propósito:** Justificación técnica y de negocio para implementar SupplierQuotation como entidad independiente en PromoSmart, en lugar de como campos dentro de ProductItem.

---

## El Problema de Negocio Real

Imagina que tienes un `ProductItem`: **"1000 hieleras personalizadas BeerCO"**

El Analista de Adquisiciones necesita:
1. Solicitar cotizaciones a **5 proveedores diferentes**
2. **Comparar** ofertas lado a lado
3. **Seleccionar** la mejor opción
4. **Mantener histórico** de todas las opciones para auditoría

---

## ❌ Enfoque Incorrecto: SupplierQuotation como Parte de ProductItem

### Implementación Problemática

```php
// ENFOQUE INCORRECTO
class ProductItem extends Model 
{
    protected $fillable = [
        'name',
        'specifications',
        'quantity',
        // ¿Solo UN proveedor?
        'supplier_id',
        'unit_price',
        'lead_time',
        'moq'
    ];
}
```

### Problemas Críticos

- ✖️ **Solo UN proveedor** por producto
- ✖️ **No comparación** posible  
- ✖️ **Sin histórico** de ofertas rechazadas
- ✖️ **Pérdida de información** valiosa para futuras negociaciones
- ✖️ **Violación del RF-02.1**: No permite "interfaz de comparación lado a lado"

---

## ✅ Enfoque Correcto: SupplierQuotation como Entidad Separada

### Modelo de Datos Propuesto

```php
class ProductItem extends Model 
{
    protected $fillable = [
        'name',
        'specifications', 
        'quantity',
        'target_unit_price', // Precio objetivo interno
        'selected_supplier_quotation_id' // FK a la cotización elegida
    ];
    
    // Relaciones
    public function supplierQuotations() 
    {
        return $this->hasMany(SupplierQuotation::class);
    }
    
    public function selectedQuotation() 
    {
        return $this->belongsTo(SupplierQuotation::class, 'selected_supplier_quotation_id');
    }
}

class SupplierQuotation extends Model 
{
    protected $fillable = [
        'product_item_id',
        'supplier_id',
        'unit_price',
        'currency',
        'moq',
        'lead_time_days',
        'valid_until',
        'status', // draft, submitted, under_review, selected, rejected
        'payment_terms',
        'delivery_terms',
        'quote_reference', // Número de cotización del proveedor
        'notes'
    ];
    
    protected $casts = [
        'valid_until' => 'date',
        'status' => SupplierQuotationStatus::class
    ];
}

enum SupplierQuotationStatus: string 
{
    case DRAFT = 'draft';
    case SUBMITTED = 'submitted';
    case UNDER_REVIEW = 'under_review';
    case SELECTED = 'selected';
    case REJECTED = 'rejected';
}
```

### Estructura de Base de Datos

```sql
-- Tabla ProductItem (simplificada)
CREATE TABLE product_items (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255),
    specifications JSONB,
    quantity INTEGER,
    target_unit_price DECIMAL(10,2),
    selected_supplier_quotation_id INTEGER REFERENCES supplier_quotations(id),
    project_id INTEGER REFERENCES projects(id),
    created_at TIMESTAMPTZ DEFAULT now()
);

-- Tabla SupplierQuotation (nueva entidad)
CREATE TABLE supplier_quotations (
    id SERIAL PRIMARY KEY,
    product_item_id INTEGER REFERENCES product_items(id),
    supplier_id INTEGER REFERENCES suppliers(id),
    unit_price DECIMAL(10,2),
    currency VARCHAR(3),
    moq INTEGER,
    lead_time_days INTEGER,
    valid_until DATE,
    status VARCHAR(20),
    payment_terms TEXT,
    delivery_terms TEXT,
    quote_reference VARCHAR(100),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT now(),
    
    INDEX(product_item_id),
    INDEX(supplier_id),
    INDEX(status),
    INDEX(valid_until)
);
```

---

## Flujo de Trabajo Completo

### 1. Solicitud de Cotizaciones

```php
class SourcingService 
{
    public function requestQuotations(ProductItem $item, array $supplierIds): array 
    {
        $quotations = [];
        
        foreach ($supplierIds as $supplierId) {
            $quotations[] = SupplierQuotation::create([
                'product_item_id' => $item->id,
                'supplier_id' => $supplierId,
                'status' => SupplierQuotationStatus::DRAFT
            ]);
        }
        
        // Enviar RFQ emails automáticos
        $this->sendRFQEmails($quotations);
        
        return $quotations;
    }
}

// Usage:
$supplierIds = [101, 102, 103, 104, 105]; // 5 proveedores
$quotations = app(SourcingService::class)->requestQuotations($productItem, $supplierIds);
// Resultado: 5 registros SupplierQuotation en estado DRAFT
```

### 2. Recepción y Registro de Ofertas

```php
// Proveedor A responde
SupplierQuotation::where('product_item_id', $item->id)
    ->where('supplier_id', 101)
    ->update([
        'unit_price' => 12.50,
        'currency' => 'USD',
        'moq' => 500,
        'lead_time_days' => 21,
        'valid_until' => '2024-02-15',
        'status' => SupplierQuotationStatus::SUBMITTED,
        'quote_reference' => 'PROV-A-2024-001'
    ]);

// Proveedor B responde
SupplierQuotation::where('product_item_id', $item->id)
    ->where('supplier_id', 102)
    ->update([
        'unit_price' => 11.80,
        'currency' => 'USD', 
        'moq' => 1000,
        'lead_time_days' => 28,
        'valid_until' => '2024-02-10',
        'status' => SupplierQuotationStatus::SUBMITTED,
        'quote_reference' => 'PROV-B-2024-078'
    ]);

// ... y así con los demás proveedores
```

### 3. Comparación y Análisis

```php
class QuotationComparisonService 
{
    public function getComparison(ProductItem $item): array 
    {
        $quotations = $item->supplierQuotations()
            ->where('status', SupplierQuotationStatus::SUBMITTED)
            ->with('supplier')
            ->get();
            
        return $quotations->map(function($quote) {
            return [
                'supplier_name' => $quote->supplier->name,
                'unit_price' => $quote->unit_price,
                'total_cost' => $quote->unit_price * $quote->productItem->quantity,
                'moq' => $quote->moq,
                'lead_time' => $quote->lead_time_days,
                'valid_until' => $quote->valid_until,
                'score' => $this->calculateScore($quote)
            ];
        })->sortBy('score')->values();
    }
    
    private function calculateScore(SupplierQuotation $quote): float 
    {
        // Algoritmo de scoring personalizable
        $priceWeight = 0.4;
        $timeWeight = 0.3;
        $qualityWeight = 0.3;
        
        $priceScore = (20 - $quote->unit_price) / 20; // Normalizar
        $timeScore = (45 - $quote->lead_time_days) / 45;
        $qualityScore = $quote->supplier->quality_rating / 10;
        
        return ($priceScore * $priceWeight) + 
               ($timeScore * $timeWeight) + 
               ($qualityScore * $qualityWeight);
    }
}
```

### 4. Selección y Rechazo

```php
class QuotationSelectionService 
{
    public function selectQuotation(SupplierQuotation $selectedQuote): void 
    {
        DB::transaction(function() use ($selectedQuote) {
            // Marcar como seleccionada
            $selectedQuote->update(['status' => SupplierQuotationStatus::SELECTED]);
            
            // Rechazar las demás
            SupplierQuotation::where('product_item_id', $selectedQuote->product_item_id)
                ->where('id', '!=', $selectedQuote->id)
                ->update(['status' => SupplierQuotationStatus::REJECTED]);
            
            // Actualizar ProductItem
            $selectedQuote->productItem->update([
                'selected_supplier_quotation_id' => $selectedQuote->id,
                'status' => 'SOURCING_COMPLETED'
            ]);
            
            // Registrar actividad
            $selectedQuote->productItem->activities()->create([
                'activity_type' => 'supplier_selected',
                'title' => 'Supplier Selected',
                'description' => "Selected {$selectedQuote->supplier->name} at ${$selectedQuote->unit_price}/unit",
                'user_id' => auth()->id()
            ]);
        });
    }
}
```

---

## Interface de Comparación (RF-02.1)

### Implementación en Filament

```php
// Filament Table para comparación lado a lado
class SupplierQuotationComparisonPage extends Page 
{
    public ProductItem $productItem;
    
    public function table(Table $table): Table 
    {
        return $table
            ->query(
                SupplierQuotation::where('product_item_id', $this->productItem->id)
                    ->where('status', SupplierQuotationStatus::SUBMITTED)
            )
            ->columns([
                TextColumn::make('supplier.name')->label('Proveedor'),
                TextColumn::make('unit_price')
                    ->money('USD')
                    ->sortable()
                    ->color(fn($state) => $this->getPriceColor($state)),
                TextColumn::make('total_cost')
                    ->label('Costo Total')
                    ->getStateUsing(fn($record) => $record->unit_price * $this->productItem->quantity)
                    ->money('USD'),
                TextColumn::make('moq')->label('MOQ'),
                TextColumn::make('lead_time_days')->label('Tiempo (días)'),
                TextColumn::make('valid_until')->date(),
                TextColumn::make('supplier.quality_rating')
                    ->label('Rating')
                    ->badge()
                    ->color(fn($state) => $state >= 8 ? 'success' : ($state >= 6 ? 'warning' : 'danger'))
            ])
            ->actions([
                Action::make('select')
                    ->button()
                    ->color('success')
                    ->action(fn($record) => app(QuotationSelectionService::class)->selectQuotation($record))
            ]);
    }
    
    private function getPriceColor($price): string 
    {
        if ($price <= 10) return 'success';
        if ($price <= 15) return 'warning';
        return 'danger';
    }
}
```

---

## Beneficios de Negocio Concretos

### 1. Negociación Informada

```php
// Análisis histórico para futuras negociaciones
class SupplierNegotiationService 
{
    public function getHistoricalData(int $supplierId, string $category = null): array 
    {
        $query = SupplierQuotation::where('supplier_id', $supplierId)
            ->where('status', 'selected')
            ->where('created_at', '>=', now()->subYear());
            
        if ($category) {
            $query->whereHas('productItem.subcategory.category', function($q) use ($category) {
                $q->where('name', $category);
            });
        }
        
        $data = $query->selectRaw('AVG(unit_price) as avg_price, COUNT(*) as projects_won')
            ->first();
            
        return [
            'avg_winning_price' => $data->avg_price,
            'projects_won' => $data->projects_won,
            'win_rate' => $this->calculateWinRate($supplierId),
            'last_quote_date' => $query->max('created_at')
        ];
    }
}

// "Proveedor A ha ganado 8 proyectos con precio promedio $11.20"
```

### 2. Análisis de Proveedores

```php
class SupplierPerformanceService 
{
    public function getSupplierStats(Supplier $supplier): array 
    {
        $quotations = SupplierQuotation::where('supplier_id', $supplier->id)->get();
        
        return [
            'total_quotations' => $quotations->count(),
            'won_quotations' => $quotations->where('status', 'selected')->count(),
            'win_rate' => $quotations->where('status', 'selected')->count() / $quotations->count(),
            'avg_price' => $quotations->where('status', 'selected')->avg('unit_price'),
            'avg_lead_time' => $quotations->where('status', 'selected')->avg('lead_time_days'),
            'last_quote_date' => $quotations->max('created_at'),
            'categories_served' => $this->getCategoriesServed($supplier)
        ];
    }
    
    private function getCategoriesServed(Supplier $supplier): array 
    {
        return SupplierQuotation::where('supplier_id', $supplier->id)
            ->where('status', 'selected')
            ->join('product_items', 'supplier_quotations.product_item_id', '=', 'product_items.id')
            ->join('product_subcategories', 'product_items.subcategory_id', '=', 'product_subcategories.id')
            ->join('product_categories', 'product_subcategories.category_id', '=', 'product_categories.id')
            ->groupBy('product_categories.name')
            ->pluck('product_categories.name')
            ->toArray();
    }
}
```

### 3. Optimización de Proceso

```sql
-- Dashboard query: "Proveedores más competitivos por categoría"
SELECT 
    s.name as supplier_name,
    pc.name as category,
    COUNT(*) as quotations_won,
    AVG(sq.unit_price) as avg_winning_price,
    AVG(sq.lead_time_days) as avg_lead_time,
    MIN(sq.unit_price) as best_price_ever,
    MAX(sq.created_at) as last_win_date
FROM supplier_quotations sq
JOIN suppliers s ON sq.supplier_id = s.id  
JOIN product_items pi ON sq.product_item_id = pi.id
JOIN product_subcategories psc ON pi.subcategory_id = psc.id
JOIN product_categories pc ON psc.category_id = pc.id
WHERE sq.status = 'selected'
  AND sq.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
GROUP BY s.id, pc.id
HAVING quotations_won >= 2  -- Solo proveedores con múltiples wins
ORDER BY quotations_won DESC, avg_winning_price ASC;
```

### 4. Alertas y Automatización

```php
class QuotationMonitoringService 
{
    public function checkExpiredQuotations(): void 
    {
        $expiredQuotations = SupplierQuotation::where('status', 'submitted')
            ->where('valid_until', '<', now())
            ->with(['productItem', 'supplier'])
            ->get();
            
        foreach ($expiredQuotations as $quotation) {
            // Marcar como expirada
            $quotation->update(['status' => 'expired']);
            
            // Notificar al analista
            $this->notifyAnalyst($quotation);
            
            // Auto-solicitar extensión si es el único proveedor
            if ($this->isOnlyQuotation($quotation)) {
                $this->requestQuotationExtension($quotation);
            }
        }
    }
    
    public function identifyPriceOutliers(ProductItem $item): array 
    {
        $quotations = $item->supplierQuotations()
            ->where('status', 'submitted')
            ->get();
            
        if ($quotations->count() < 3) return [];
        
        $prices = $quotations->pluck('unit_price');
        $mean = $prices->avg();
        $stdDev = $this->calculateStandardDeviation($prices);
        
        return $quotations->filter(function($quote) use ($mean, $stdDev) {
            return abs($quote->unit_price - $mean) > (2 * $stdDev);
        })->values()->toArray();
    }
}
```

---

## Casos de Uso de Reporting

### Dashboard Ejecutivo

```php
class ExecutiveDashboardService 
{
    public function getSourcingMetrics(CarbonPeriod $period): array 
    {
        return [
            'total_quotations_requested' => $this->getTotalQuotationsRequested($period),
            'avg_quotations_per_product' => $this->getAvgQuotationsPerProduct($period),
            'avg_response_time_hours' => $this->getAvgResponseTime($period),
            'cost_savings_achieved' => $this->getCostSavings($period),
            'top_suppliers_by_wins' => $this->getTopSuppliers($period),
            'categories_with_best_competition' => $this->getBestCompetition($period)
        ];
    }
    
    private function getCostSavings(CarbonPeriod $period): array 
    {
        $quotations = SupplierQuotation::whereBetween('created_at', [$period->start, $period->end])
            ->where('status', '!=', 'draft')
            ->get()
            ->groupBy('product_item_id');
            
        $totalSavings = 0;
        $productsAnalyzed = 0;
        
        foreach ($quotations as $productQuotations) {
            if ($productQuotations->count() > 1) {
                $prices = $productQuotations->pluck('unit_price');
                $savings = $prices->max() - $prices->min();
                $totalSavings += $savings;
                $productsAnalyzed++;
            }
        }
        
        return [
            'total_savings' => $totalSavings,
            'products_analyzed' => $productsAnalyzed,
            'avg_savings_per_product' => $productsAnalyzed > 0 ? $totalSavings / $productsAnalyzed : 0
        ];
    }
}
```

### Análisis de Tendencias

```sql
-- Query: "Evolución de precios por categoría en el tiempo"
WITH monthly_prices AS (
    SELECT 
        DATE_TRUNC('month', sq.created_at) as month,
        pc.name as category,
        AVG(sq.unit_price) as avg_price,
        COUNT(*) as quotations_count
    FROM supplier_quotations sq
    JOIN product_items pi ON sq.product_item_id = pi.id
    JOIN product_subcategories psc ON pi.subcategory_id = psc.id
    JOIN product_categories pc ON psc.category_id = pc.id
    WHERE sq.status = 'selected'
      AND sq.created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
    GROUP BY month, pc.id, pc.name
    HAVING quotations_count >= 3  -- Solo meses con suficientes datos
)
SELECT 
    category,
    month,
    avg_price,
    LAG(avg_price) OVER (PARTITION BY category ORDER BY month) as prev_month_price,
    ((avg_price - LAG(avg_price) OVER (PARTITION BY category ORDER BY month)) / 
     LAG(avg_price) OVER (PARTITION BY category ORDER BY month)) * 100 as price_change_pct
FROM monthly_prices
ORDER BY category, month;
```

---

## Justificación Arquitectónica

### 1. Relación Uno-a-Muchos (One-to-Many)

**Argumento fundamental:** Para un único `ProductItem` (ej. "1000 hieleras"), el Analista de Adquisiciones necesita registrar y comparar ofertas de **múltiples proveedores**. Si los datos de la cotización fueran campos dentro del `ProductItem`, solo podrías almacenar la información de un proveedor a la vez.

### 2. Cumplimiento del RF-02.1

**Requisito explícito:** El requisito `RF-02.1` exige una "interfaz de comparación lado a lado" para las cotizaciones de proveedores. Esto solo es posible si cada cotización es un registro separado en la base de datos, vinculado a un `ProductItem` común.

### 3. Ciclo de Vida y Datos Propios

**Autonomía de la entidad:** Una cotización de proveedor tiene su propio ciclo de vida (`DRAFT`, `SUBMITTED`, `SELECTED`, `REJECTED`) y atributos que no pertenecen al producto, como:
- Fecha de validez de la oferta (`valid_until`)
- Tiempo de producción específico del proveedor (`lead_time_days`)
- Cantidad mínima de pedido (`moq`)
- Términos comerciales específicos (`payment_terms`, `delivery_terms`)

### 4. Claridad del Modelo de Dominio

**Separación de responsabilidades:**
- **`ProductItem`**: Responde a "¿Qué es este producto?"
- **`SupplierQuotation`**: Responde a "¿Qué oferta nos hizo este proveedor para fabricar este producto?"

### 5. Escalabilidad y Mantenibilidad

**Beneficios técnicos:**
- Queries optimizadas con índices específicos
- Posibilidad de archivado selectivo de cotizaciones antiguas
- Extensibilidad para nuevos campos sin afectar ProductItem
- Reutilización de proveedores entre productos

---

## Consideraciones de Implementación

### Performance Optimization

```php
// Eager loading para evitar N+1 queries
$productItems = ProductItem::with([
    'supplierQuotations' => function($query) {
        $query->where('status', 'submitted')
              ->with('supplier:id,name,quality_rating')
              ->orderBy('unit_price');
    }
])->get();

// Índices recomendados
/*
CREATE INDEX idx_supplier_quotations_lookup ON supplier_quotations 
(product_item_id, status, unit_price);

CREATE INDEX idx_supplier_quotations_reporting ON supplier_quotations 
(supplier_id, status, created_at) 
WHERE status = 'selected';
*/
```

### Data Integrity

```php
// Constraints de base de datos
/*
ALTER TABLE supplier_quotations 
ADD CONSTRAINT chk_valid_status 
CHECK (status IN ('draft', 'submitted', 'under_review', 'selected', 'rejected', 'expired'));

ALTER TABLE supplier_quotations 
ADD CONSTRAINT chk_positive_price 
CHECK (unit_price > 0);

ALTER TABLE supplier_quotations 
ADD CONSTRAINT chk_valid_dates 
CHECK (valid_until >= created_at::date);
*/

// Business rules en el modelo
class SupplierQuotation extends Model 
{
    protected static function boot() 
    {
        parent::boot();
        
        static::updating(function($quotation) {
            if ($quotation->isDirty('status') && $quotation->getOriginal('status') === 'selected') {
                throw new BusinessRuleException('Cannot change status of selected quotation');
            }
        });
    }
}
```

---

## Conclusión

**SupplierQuotation DEBE ser entidad separada** porque:

1. **Relación 1:N real**: Un producto requiere múltiples cotizaciones para comparación
2. **Proceso de negocio**: La comparación es un requisito funcional explícito (RF-02.1)
3. **Valor de datos**: El histórico es crítico para análisis y negociaciones futuras
4. **Separación de responsabilidades**: Producto ≠ Oferta del proveedor
5. **Auditoría y transparencia**: Trazabilidad completa en la selección de proveedores
6. **Escalabilidad**: Soporte para casos de uso avanzados como análisis de tendencias
7. **Integridad del modelo**: Mantiene la coherencia del dominio de negocio

**Es arquitectónicamente correcto y funcionalmente necesario** para cumplir con los requisitos de PromoSmart y proporcionar una base sólida para la optimización continua del proceso de sourcing.

---

**Fecha de creación:** $(date)  
**Versión:** 1.0  
**Revisado por:** Equipo de Arquitectura PromoSmart