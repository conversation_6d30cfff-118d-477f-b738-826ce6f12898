- artefactos, como se pueden implementar? queremos que tengan nombres especiales como PROY_2025_001, OC_2025_001, etc.
- alternativas detalladas del registro historico de entidades importantes. se registra todo? se guarda copia inmutable de todo? que pasa si tienen archivos adjuntos?
- value objects, de que sirven? cuales entidades podrian ser value objects?
- dto, para que sirven? cuales entidades podrian ser dtos?
- sobre producto:
    - la cotizacion (sourcing de proveedor) es una entidad distinta y vinculada a producto? o es parte de producto? o es separada pero se guarda un registro inmutable dentro de producto cuando ocurre algun cambio de estado? que otras alternativas existen? las cotizaciones se guardan en los artefactos? la cotizacion es inmutable?
- feature de comentarios y tareas dentro de entidades como producto, cotizacion
- archivos en entidades (imagenes referenciales de productos, adjuntos en cotizaciones de producto, etc.). almacenar en s3?
- registro historico y versionamiento. detalles, alternativas. cuales entidades requieren registro historico y/o versionamiento, cual es la diferencia, como se manejan

cuales son los pros y contras de usar json en vez de modelo relacional para las caracteristicas de los productos? como eso afecta otras cosas   │
│    como por ejemplo la generacion de forms dinamicos en filament?    