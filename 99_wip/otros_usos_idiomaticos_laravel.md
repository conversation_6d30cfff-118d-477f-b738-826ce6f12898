# Otras Ideas de Arquitectura Idiomática Laravel para PromoSmart

Basado en un análisis de los documentos de `1_overview`, y más allá de lo que ya hemos discutido, aquí hay algunas ideas adicionales de arquitectura, puramente idiomáticas de Laravel, que serían extremadamente útiles para el proyecto PromoSmart.

Estas ideas se centran en resolver los problemas de negocio documentados de una manera que sea elegante, mantenible y que aproveche al máximo el framework.

---

### 1. Gestión de Procesos Complejos con Máquinas de Estado (State Machines)

*   **Problema de Negocio:** El ciclo de vida de un `ProductItem` es complejo, con muchos estados y transiciones específicas (Borrador → Sourcing → Aprobación VM → etc.), como se detalla en `03_Modelo_de_Negocio.md`. Gestionar esto con sentencias `if/else` en los modelos o controladores se vuelve frágil y difícil de mantener.

*   **Solución Idiomática Laravel:** Utilizar un paquete de **Máquina de Estados** como `spatie/laravel-model-states`. Esta es una práctica muy extendida en la comunidad Laravel para manejar flujos de trabajo complejos.

*   **Cómo se Aplica a PromoSmart:**
    *   Definirías cada estado (`Draft`, `SourcingInProgress`, `PendingVmApproval`) como una clase separada.
    *   Cada clase de estado define explícitamente a qué otros estados puede transicionar. Por ejemplo, la clase `SourcingInProgress` solo permitiría la transición a `QuotedToCustomer` o `RevisionRequested`.
    *   El sistema lanzaría un error si se intenta una transición inválida (ej. de `Draft` a `InProduction`), garantizando la integridad del proceso.

*   **Beneficios:**
    *   **Claridad:** El flujo de trabajo del negocio vive en clases dedicadas, auto-documentando el proceso.
    *   **Seguridad:** Previene transiciones de estado imposibles o no permitidas.
    *   **Mantenibilidad:** Añadir un nuevo estado o cambiar una transición es tan simple como modificar una clase, sin tener que buscar la lógica por todo el código.

---

### 2. Clases de Petición Personalizadas (Custom Form Requests)

*   **Problema de Negocio:** La creación y actualización de entidades como `ProductItem` tiene reglas de validación complejas que dependen de la categoría del producto (RF-19). Por ejemplo, si la categoría es "Textiles", el campo "GSM" es obligatorio. Poner esta lógica en el controlador lo ensucia.

*   **Solución Idiomática Laravel:** Usar **Form Requests**. En lugar de validar en el controlador, creas una clase dedicada como `StoreProductItemRequest`.

*   **Cómo se Aplica a PromoSmart:**
    ```php
    // En el controlador, la validación es invisible y automática
    public function store(StoreProductItemRequest $request)
    {
        // Si llegamos aquí, la data ya está validada.
        $validatedData = $request->validated();
        // ... crear el ProductItem
    }

    // En la clase StoreProductItemRequest
    public function rules(): array
    {
        $rules = [
            'project_id' => 'required|exists:projects,id',
            'subcategory_id' => 'required|exists:product_subcategories,id',
            // ... reglas comunes
        ];

        // Lógica de validación condicional
        if ($this->input('subcategory_id') === 1) { // Asumimos 1 = Textiles
            $rules['specifications.gsm'] = 'required|numeric';
        }

        return $rules;
    }
    ```

*   **Beneficios:**
    *   **Controladores Limpios:** Los controladores se enfocan en su responsabilidad principal, no en la validación.
    *   **Reutilización:** La misma `FormRequest` puede ser usada para el endpoint de la API y el formulario web.
    *   **Organización:** La lógica de validación para una acción compleja vive en su propia clase.

---

### 3. Scopes de Consulta y API Resources para Lógica de Roles

*   **Problema de Negocio:** Diferentes roles ven diferentes conjuntos de datos. Un `sales_analyst` solo ve sus proyectos, mientras que un `team_leader` los ve todos (`06_Roles_y_Permisos.md`). Además, la información mostrada debe variar (un diseñador no debe ver los márgenes de ganancia).

*   **Solución Idiomática Laravel:** Combinar **Query Scopes** y **API Resources**.

*   **Cómo se Aplica a PromoSmart:**
    *   **Query Scopes:** En el modelo `Project`, defines un scope.
        ```php
        // En el modelo Project.php
        public function scopeForUser(Builder $query, User $user): Builder
        {
            if ($user->hasRole('team_leader') || $user->hasRole('financial_analyst')) {
                return $query; // El líder ve todo
            }
            // Un analista de ventas solo ve los proyectos que le pertenecen
            return $query->where('sales_analyst_id', $user->id);
        }
        ```
        En el controlador, la consulta se vuelve trivial: `Project::forUser(auth()->user())->get();`.

    *   **API Resources:** Para controlar los datos que se exponen en la API.
        ```php
        // En ProjectResource.php
        public function toArray(Request $request): array
        {
            return [
                'id' => $this->id,
                'name' => $this->name,
                'status' => $this->status,
                // Carga condicional de datos sensibles
                'financials' => $this->when(
                    $request->user()->can('view financials'),
                    fn () => new FinancialsResource($this->financials)
                ),
            ];
        }
        ```

*   **Beneficios:**
    *   **DRY (Don't Repeat Yourself):** La lógica de permisos de consulta está en un solo lugar (el scope).
    *   **Seguridad:** Se asegura de no exponer accidentalmente datos sensibles en la API.
    *   **Claridad:** El código es expresivo y fácil de leer.

---

### 4. Colas y Trabajos (Queues & Jobs) para Tareas Lentas

*   **Problema de Negocio:** Algunas acciones toman tiempo y no deberían hacer esperar al usuario, como generar un PDF (`RF-06.1`), enviar notificaciones por email (`RF-09`), o contactar una API externa de logística (`RI-04`).

*   **Solución Idiomática Laravel:** Usar el sistema de **Queues**.

*   **Cómo se Aplica a PromoSmart:**
    *   Cuando un usuario solicita generar una cotización, en lugar de generarla en el momento, se despacha un trabajo: `GenerateQuotationPdf::dispatch($quotation);`.
    *   El usuario recibe una respuesta inmediata ("Tu cotización se está generando y te notificaremos cuando esté lista").
    *   Un proceso "worker" en el servidor se encarga de ejecutar el trabajo en segundo plano.
    *   Todas las notificaciones por email deben ser enviadas a través de la cola para no ralentizar las respuestas de la aplicación.

*   **Beneficios:**
    *   **Experiencia de Usuario (UX) Fluida:** La aplicación se siente instantánea.
    *   **Robustez:** Si la generación del PDF falla, el trabajo puede reintentarse automáticamente sin que el usuario tenga que hacer nada.
    *   **Escalabilidad:** Puedes añadir más "workers" para procesar más trabajos en paralelo si la carga aumenta.

---

### Ideas Adicionales (Nivel Avanzado)

Aquí hay algunas ideas más sutiles, pero que son la marca de una aplicación Laravel verdaderamente idiomática y bien construida.

#### 5. Casts de Atributos Personalizados (Custom Attribute Casts)

*   **Problema de Negocio:** Se usarán **Value Objects** como `Money` o `ProductSpecifications`. Se busca que Eloquent trabaje con estos objetos de forma nativa, sin tener que instanciarlos manualmente.

*   **Solución Idiomática Laravel:** Crear **Casts de Atributos personalizados**.

*   **Cómo se Aplica a PromoSmart:**
    ```php
    use App\Casts\MoneyCast;

    class SupplierQuotation extends Model
    {
        protected $casts = [
            // Cuando accedas a $quotation->unit_price,
            // obtendrás un objeto Money automáticamente.
            'unit_price' => MoneyCast::class,
        ];
    }
    ```

*   **Beneficios:**
    *   **Código Expresivo:** Interactúas con objetos ricos y seguros en lugar de tipos primitivos.
    *   **Encapsulación:** La lógica de conversión vive en un solo lugar (la clase del Cast).
    *   **Seguridad de Tipos:** Reduce errores al asegurar que siempre trabajas con los objetos correctos.

#### 6. Reglas de Validación Personalizadas (Custom Validation Rules)

*   **Problema de Negocio:** Algunas validaciones son complejas o se reutilizan (ej. "verificar que un proveedor esté activo").

*   **Solución Idiomática Laravel:** Crear tus propias **clases de Reglas de Validación**.

*   **Cómo se Aplica a PromoSmart:**
    ```php
    use App\Rules\IsActiveSupplier;

    public function rules(): array
    {
        return [
            'supplier_id' => ['required', 'exists:suppliers,id', new IsActiveSupplier],
        ];
    }
    ```

*   **Beneficios:**
    *   **Reutilizable y legible:** La regla `new IsActiveSupplier` es mucho más clara que una Closure de validación.
    *   **Testeable:** Puedes probar la lógica de la regla de forma aislada.

#### 7. Carga Ansiosa por Defecto (Eager Loading by Default)

*   **Problema de Negocio:** Prevenir el problema de "N+1 queries" para relaciones que casi siempre se usan (ej. `Project` con su `Customer`).

*   **Solución Idiomática Laravel:** Definir la propiedad **`$with`** en el modelo de Eloquent.

*   **Cómo se Aplica a PromoSmart:**
    ```php
    // En el modelo Project.php
    class Project extends Model
    {
        // Laravel cargará automáticamente estas relaciones
        // cada vez que se consulte un Proyecto.
        protected $with = ['customer', 'salesAnalyst'];
    }
    ```

*   **Beneficios:**
    *   **Prevención de Errores de Rendimiento:** Elimina la causa más común de consultas lentas.
    *   **Conveniencia:** No tienes que recordar añadir `.with(...)` en cada consulta.

#### 8. Archivos de Configuración Personalizados

*   **Problema de Negocio:** Gestionar parámetros de negocio (márgenes, umbrales, etc., de RF-22) de forma centralizada y bajo control de versiones.

*   **Solución Idiomática Laravel:** Crear un archivo de configuración personalizado en la carpeta `config/` (ej. `config/promosmart.php`).

*   **Cómo se Aplica a PromoSmart:**
    ```php
    // En config/promosmart.php
    return [
        'default_margin_percent' => 35,
        'critical_delay_threshold_days' => 7,
    ];
    
    // En el código: 
    $margin = config('promosmart.default_margin_percent');
    ```

*   **Beneficios:**
    *   **Centralización:** Toda la configuración de negocio vive en un solo lugar.
    *   **Control de Versiones:** El archivo se guarda en Git.
    *   **Seguridad:** Separa la configuración de la aplicación de los secretos del entorno (`.env`).

#### 9. Laravel Scout para Búsqueda Global

*   **Problema de Negocio:** El requisito de búsqueda global (RF-13) necesita ser rápido y buscar en múltiples modelos, lo cual es ineficiente con SQL `LIKE`.

*   **Solución Idiomática Laravel:** Usar **Laravel Scout**.

*   **Cómo se Aplica a PromoSmart:**
    1.  Instalas Scout y un "driver" (ej. Meilisearch, Algolia).
    2.  Añades el trait `Searchable` a tus modelos (`Project`, `ProductItem`, etc.).
    3.  Scout mantiene los datos sincronizados con un motor de búsqueda optimizado.
    4.  La búsqueda se vuelve trivial y ultra-rápida: `ProductItem::search('polera premium')->get();`.

*   **Beneficios:**
    *   **Rendimiento Extremo:** Ofrece una experiencia de búsqueda instantánea.
    *   **Relevancia:** Resultados de búsqueda de mayor calidad.
    *   **Abstracción:** El código es simple, sin importar el motor de búsqueda subyacente.