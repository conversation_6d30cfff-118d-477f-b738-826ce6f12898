# Respuestas a Ideas y Conceptos de Implementación

A continuación se detallan respuestas, explicaciones y alternativas a los puntos planteados en `99_wip/ideas.md`, basadas en la documentación del proyecto.

---

## 1. Implementación de Artefactos y Nomenclatura

> **Pregunta:** artefactos, como se pueden implementar? queremos que tengan nombres especiales como PROY_2025_001, OC_2025_001, etc.

La documentación (RF-06.1) especifica que los artefactos (Cotizaciones, Órdenes de Compra, etc.) se generan a partir de `data snapshots`, asegurando que sean representaciones inmutables de la información en un momento dado.

### Implementación Sugerida

1.  **Generación de Nombres (Correlativos):**
    Se puede implementar un sistema de contadores centralizado.
    *   Una tabla en la base de datos llamada `sequences` o `counters`.
    *   Columnas: `name` (ej: "project", "supplier_po"), `year`, `last_value`.
    *   Cuando se crea un nuevo artefacto, una transacción se encarga de:
        1.  Buscar el contador para el tipo de artefacto y el año actual.
        2.  Incrementar `last_value` en 1.
        3.  Construir el nombre: `f"{prefix}_{year}_{new_value:03d}"`.
    *   **Ejemplo de Nombres:**
        *   Proyecto: `PROY_2025_001`
        *   Cotización a Cliente: `COT_2025_001`
        *   Orden de Compra a Proveedor: `OCP_2025_001`

2.  **Almacenamiento:**
    *   El **archivo físico** (ej. PDF) se guarda en un almacenamiento de objetos como AWS S3.
    *   La **referencia** al archivo y su **nombre único** (`PROY_2025_001`) se guardan en una tabla en la base de datos, por ejemplo `documents` o `artifacts`, vinculada al proyecto o producto correspondiente.
    *   El **snapshot de datos (JSON)** usado para generar el artefacto también se guarda, como se describe en `09_Auditoria_y_Trazabilidad.md`, para permitir la regeneración o auditoría.

---

## 2. Registro Histórico de Entidades (Auditoría)

> **Pregunta:** alternativas detalladas del registro historico de entidades importantes. se registra todo? se guarda copia inmutable de todo? que pasa si tienen archivos adjuntos?

El documento `09_Auditoria_y_Trazabilidad.md` establece una filosofía clara: **Auditoría Selectiva con Propósito**. No se registra todo, solo lo que tiene valor de negocio.

### Modelo Propuesto (Basado en la Documentación)

*   **Qué se guarda:** Se guarda un **snapshot JSON inmutable** del estado de la entidad en momentos críticos del negocio (ej. al crear la línea base de un proyecto, al enviar una cotización, al pasar a producción).
*   **Entidades Clave:** `Project`, `ProductItem`, `CustomerQuotation`, `SupplierPurchaseOrder`.
*   **Archivos Adjuntos:** El snapshot JSON no contiene el archivo en sí. Contiene una **referencia inmutable** a la versión específica del archivo almacenado en S3. Si un archivo adjunto cambia, se sube una nueva versión a S3 y el nuevo snapshot apuntará a la nueva versión, manteniendo el historial intacto.

### Alternativas al Modelo de Snapshot JSON

1.  **Tablas de Auditoría Separadas (`Audit Tables`):**
    *   **Descripción:** Por cada tabla principal (ej. `product_items`), existe una tabla `product_items_history`. Se inserta un registro en la tabla de historial con cada cambio.
    *   **Ventajas:** Más normalizado, puede ser más eficiente en espacio si los cambios son pequeños.
    *   **Desventajas:** Reconstruir el estado de un objeto en un punto del tiempo requiere procesar múltiples registros, lo cual es complejo y lento. Genera una gran cantidad de tablas adicionales.

2.  **Event Sourcing:**
    *   **Descripción:** Es el enfoque más radical. No se guarda el estado actual de la entidad, solo se guarda una secuencia de eventos que han ocurrido (`ProjectCreated`, `ProductAdded`, `StatusChangedToInProduction`). El estado actual se reconstruye aplicando todos los eventos.
    *   **Ventajas:** Ofrece la auditoría más completa posible. Es la "fuente de la verdad" definitiva.
    *   **Desventajas:** Extremadamente complejo de implementar y mantener. Requiere un cambio de paradigma en todo el sistema y es probablemente una sobre-ingeniería para este caso, como bien advierte la documentación.

El **enfoque de snapshot JSON** propuesto en la documentación es un excelente punto medio que ofrece gran parte de los beneficios de la auditoría con una complejidad mucho menor.

---

## 3. Value Objects (VOs)

> **Pregunta:** value objects, de que sirven? cuales entidades podrian ser value objects?

Un **Value Object** es un objeto cuya identidad se basa en los valores de sus atributos, no en un ID único. Son inmutables por definición. Sirven para encapsular lógica y validaciones relacionadas con un concepto del dominio, evitando errores y el uso de tipos primitivos (como `string` o `decimal`) para conceptos complejos.

### Candidatos a Value Objects en PromoSmart

*   **Money:**
    *   **Atributos:** `amount` (decimal), `currency` (enum).
    *   **Utilidad:** Evita errores de moneda. Encapsula operaciones como suma, resta o comparación, asegurando que solo se operen montos en la misma moneda.
*   **ProductSpecification:**
    *   **Atributos:** Un conjunto de atributos que definen un producto (material, dimensiones, GSM, etc.).
    *   **Utilidad:** Encapsula toda la complejidad de las especificaciones de un producto. Al ser inmutable, garantiza que una vez definido (especialmente en la línea base), no pueda ser alterado accidentalmente.
*   **Dimensions:**
    *   **Atributos:** `length`, `width`, `height`, `unit` (cm, m).
    *   **Utilidad:** Agrupa las dimensiones y la unidad, permitiendo conversiones y validaciones (ej. `largo > ancho`).
*   **FileReference:**
    *   **Atributos:** `storage_path` (ej. S3 key), `filename`, `mime_type`, `size`.
    *   **Utilidad:** Representa un archivo almacenado, encapsulando su información esencial.

---

## 4. Data Transfer Objects (DTOs)

> **Pregunta:** dto, para que sirven? cuales entidades podrian ser dtos?

Un **DTO** es un objeto plano cuya única finalidad es transferir datos entre diferentes capas o procesos (ej. del API backend al cliente frontend). No contienen lógica de negocio. Su estructura está definida por lo que el consumidor necesita ver, no por la estructura de la base de datos.

### Ejemplos de DTOs en PromoSmart

Casi todas las entidades del dominio tendrían uno o más DTOs asociados para diferentes vistas de la UI:

*   **Para el Dashboard Principal:**
    *   `ProjectSummaryDTO`: Contendría solo `id`, `name`, `customer_name`, `status`, `profitability_indicator`.
    *   `MyTasksDTO`: Contendría `item_id`, `item_name`, `project_name`, `action_required`.
*   **Para la Vista de Proyecto:**
    *   `ProjectDetailsDTO`: Un DTO mucho más rico que el `ProjectSummaryDTO`, con listas de `ProductItemSummaryDTO`, datos financieros, etc.
*   **Para la Vista de Ítem de Producto:**
    *   `ProductItemDetailsDTO`: Un DTO con todas las especificaciones, el historial de aprobaciones, las cotizaciones de proveedores, etc.

El uso de DTOs es crucial para desacoplar la capa de presentación del modelo de dominio y para optimizar las cargas de datos, enviando solo la información necesaria para cada vista.

---

## 5. Relación entre Producto y Cotización

> **Pregunta:** la cotizacion es una entidad distinta y vinculada a producto? o es parte de producto? ... que otras alternativas existen?

La documentación (`RF-02.2`, `RF-02.3`, `09_Auditoria_y_Trazabilidad.md`) deja claro el modelo a seguir:

1.  **La Cotización es una Entidad/Artefacto Distinto:** Una `CustomerQuotation` no es parte del `ProductItem`. Es un documento formal que se genera en un momento dado.
2.  **Está Vinculada:** Se vincula a un `Project` y, por ende, a la lista de `ProductItems` que contiene ese proyecto en el momento de la cotización.
3.  **Es Inmutable y Versionada:** Cada vez que se envía una cotización al cliente, se crea una nueva versión inmutable. Esto es clave para la trazabilidad comercial. El sistema guarda tanto el PDF generado (el artefacto) como un snapshot JSON de los datos.

### Flujo de Trabajo

1.  El Analista de Ventas trabaja en los `ProductItems` dentro de un `Project`.
2.  Cuando está listo, presiona "Generar Cotización".
3.  El sistema crea una nueva entidad `CustomerQuotation` (versión 1).
4.  Esta cotización toma los datos actuales de los productos y los congela en un snapshot.
5.  Se genera un PDF y se envía.
6.  Si el cliente pide un cambio, el analista modifica el `ProductItem` y genera una nueva `CustomerQuotation` (versión 2), que se vincula al mismo proyecto. La versión 1 se mantiene intacta para el historial.

### Alternativas (y por qué son peores)

*   **Cotización como parte del Producto:** Si los datos de la cotización (ej. `quoted_price`) fueran solo campos en la tabla `product_items`, se perdería el historial. ¿Qué pasa si se envían 3 cotizaciones con precios diferentes? Se sobrescribirían los datos.
*   **Una sola Cotización mutable por Proyecto:** Esto tampoco funciona, por la misma razón. No se podría consultar qué se cotizó en la v1 versus la v2.

El modelo propuesto en la documentación es el correcto y estándar para este tipo de sistemas.

---

## 6. Feature de Comentarios y Tareas

> **Pregunta:** feature de comentarios y tareas dentro de entidades como producto, cotizacion

Esta funcionalidad está explícitamente definida en el requisito `RF-14: Colaboración en Contexto` y visualizada en la `ProductItem View` (`07_Interfaces_y_Flujos.md`).

### Implementación Sugerida

*   **Comentarios:**
    *   Se puede usar una tabla `comments` con una **relación polimórfica**.
    *   Columnas: `id`, `user_id`, `content`, `commentable_id`, `commentable_type`.
    *   `commentable_id` sería el ID de la entidad (ej. el ID del `ProductItem`).
    *   `commentable_type` sería el nombre del modelo (ej. `'App\Models\ProductItem'`).
    *   Esto permite que una sola tabla de comentarios sirva para `ProductItems`, `Projects`, etc.
    *   El sistema de `@menciones` analizaría el `content` para buscar `@username`, buscaría el `user_id` y crearía una notificación.

*   **Tareas:**
    *   El widget "Mis Tareas Pendientes" implica un sistema de tareas.
    *   Se podría implementar con una tabla `tasks`.
    *   Columnas: `id`, `title`, `assigned_to_user_id`, `due_date`, `completed_at`, y una relación polimórfica (`taskable_id`, `taskable_type`) para vincular la tarea a un `ProductItem` o `Project`.
    *   El sistema crearía tareas automáticamente en ciertas transiciones de estado (ej. cuando un producto pasa a `PendingVmApproval`, se crea una tarea para el `design_analyst`).

---

## 7. Almacenamiento de Archivos

> **Pregunta:** archivos en entidades (imagenes referenciales de productos, adjuntos en cotizaciones de producto, etc.). almacenar en s3?

Sí, sin duda. El requisito `RF-06.2` habla de un almacenamiento seguro y organizado. Almacenar archivos en el sistema de archivos del servidor de la aplicación o en la base de datos es un anti-patrón.

### Solución: AWS S3 (o similar)

*   **Por qué:**
    *   **Escalabilidad y Durabilidad:** Prácticamente infinitas. S3 está diseñado para una durabilidad del 99.999999999%.
    *   **Seguridad:** Permite un control de acceso muy granular (ej. `pre-signed URLs` para que los clientes suban archivos de forma segura y temporal sin darles acceso a la cuenta).
    *   **Rendimiento:** Desacopla la carga de archivos del servidor de la aplicación. Los usuarios pueden descargar/subir directamente desde/hacia S3.
    *   **Costo-Efectividad:** Generalmente más barato que el almacenamiento en disco de los servidores de aplicaciones.

*   **Implementación:**
    1.  El frontend solicita una URL pre-firmada (`pre-signed URL`) al backend para subir un archivo.
    2.  El backend genera esta URL segura que da permiso temporal para subir un objeto con un nombre específico a un bucket de S3.
    3.  El frontend sube el archivo directamente a S3 usando esa URL.
    4.  Una vez subido, el frontend notifica al backend, que guarda la referencia (la ruta o `key` del objeto en S3) en la base de datos, asociada a la entidad correspondiente (ej. `ProductItem`).

---

## 8. Registro Histórico vs. Versionamiento

> **Pregunta:** registro historico y versionamiento. detalles, alternativas. cuales entidades requieren registro historico y/o versionamiento, cual es la diferencia, como se manejan

Este punto es crucial y se basa en `09_Auditoria_y_Trazabilidad.md` y `RF-18`.

### Diferencia Clave

*   **Registro Histórico (Log de Hitos / Auditoría):** Es una **lista cronológica de eventos** o acciones que ocurrieron. Responde a la pregunta "¿Qué pasó?". Es un `feed` de actividad.
    *   *Ejemplo:* "15-Ene 10:30: Roberto (Ventas) cambió el estado a `QuotedToCustomer`."
    *   *Implementación:* Una tabla de `activity_logs` o `milestones` con relaciones polimórficas.

*   **Versionamiento (Snapshotting):** Es guardar una **copia completa del estado** de un objeto en un punto del tiempo. Responde a la pregunta "¿Cómo se veía el objeto en ese momento?".
    *   *Ejemplo:* Un snapshot JSON del `ProductItem` con todas sus especificaciones y costos en el momento en que se cotizó.
    *   *Implementación:* El campo `version_history (JSON[])` propuesto en la documentación.

### Qué necesita cada entidad

| Entidad | Registro Histórico (Log) | Versionamiento (Snapshots) |
| :--- | :---: | :---: |
| **Project** | ✅ (Hitos principales) | ✅ (La `baseline` es una forma de versionado) |
| **ProductItem** | ✅ (Esencial, el `RF-18` lo exige) | ✅ (En momentos críticos definidos) |
| **CustomerQuotation** | ❌ (No necesita log propio) | ✅ (Cada versión enviada es un nuevo snapshot) |
| **SupplierPurchaseOrder** | ❌ (No necesita log propio) | ✅ (La versión enviada es un snapshot) |

Ambos mecanismos son necesarios y se complementan. El **Log de Hitos** da una visión rápida y legible de la historia operativa, mientras que los **Snapshots** son la fuente de la verdad para auditorías profundas y para entender el contexto exacto en momentos clave de decisión comercial.

---

## 9. Cotización de Sourcing de Proveedor (Aclaración)

> **Pregunta (Aclaración):** ¿La cotización de sourcing de un proveedor es una entidad separada del `ProductItem`?

Absolutamente, sí. La cotización de sourcing de un proveedor **debe ser una entidad separada** del `ProductItem`. No es solo una buena práctica, es un requisito fundamental que emana directamente del proceso de negocio de PromoSmart.

### Justificación Arquitectónica

1.  **Relación Uno-a-Muchos (One-to-Many):**
    Este es el argumento más importante. Para un único `ProductItem` (ej. "1000 hieleras"), el Analista de Adquisiciones necesita registrar y comparar ofertas de **múltiples proveedores**. Si los datos de la cotización fueran campos dentro del `ProductItem`, solo podrías almacenar la información de un proveedor a la vez.

2.  **Permite la Comparación (Requisito RF-02.1):**
    El requisito `RF-02.1` exige explícitamente una "interfaz de comparación lado a lado" para las cotizaciones de proveedores. Esto solo es posible si cada cotización es un registro separado en la base de datos, vinculado a un `ProductItem` común.

3.  **Ciclo de Vida y Datos Propios:**
    Una cotización de proveedor tiene su propio ciclo de vida (`DRAFT`, `SUBMITTED`, `SELECTED`, `REJECTED`) y atributos que no pertenecen al producto, como la fecha de validez de la oferta (`valid_until`), el tiempo de producción de ese proveedor (`lead_time_days`), y la cantidad mínima de pedido (`moq`).

4.  **Claridad del Modelo de Dominio:**
    La separación mantiene el sistema comprensible:
    *   **`ProductItem`**: Responde a la pregunta "¿Qué es este producto?".
    *   **`SupplierQuotation`**: Responde a la pregunta "¿Qué oferta nos hizo este proveedor para fabricar este producto?".

### Modelo de Datos Propuesto

```
ProductItem
-----------
id
project_id
name
specifications (JSON)
...

SupplierQuotation
-----------------
id
product_item_id  (Foreign Key a ProductItem)
supplier_id      (Foreign Key a Supplier)
unit_price
currency
moq
lead_time_days
valid_until
status           (DRAFT, SUBMITTED, SELECTED, REJECTED)
...
```

### Conclusión
Tratar la cotización de sourcing como una entidad separada es la única manera correcta de modelar el proceso de negocio de PromoSmart. La cotización de proveedor que resulte **seleccionada** (`status = 'SELECTED'`) se convierte en la base para calcular los costos que se usarán en la **cotización al cliente**, que es, a su vez, otro artefacto distinto.