# 10. Reportes e Indicadores

## Filosofía: Reportes Simples con Valor de Negocio
- Principio: Solo reportes que respondan preguntas específicas de negocio
- Anti-patrón: Dashboards complejos con métricas sin valor

## Dashboards por Rol
### Dashboard Analista de Ventas
- Proyectos asignados y su estado
- Pipeline de cotizaciones pendientes
- Métricas de conversión de cotizaciones

### Dashboard Líder de Equipo  
- Carga de trabajo por analista
- Proyectos con riesgo de atraso
- Métricas operativas (tiempos de ciclo)

### Dashboard Analista Financiero
- Rentabilidad por proyecto vs. línea base
- Variaciones de costos por categoría
- Análisis de proveedores (OTD, calidad)

## Reportes Específicos
### Análisis de Proveedores
- Entregas a tiempo (OTD) por proveedor
- Tasa de rechazo de muestras (PPS)
- Evolución de precios por categoría de producto

### Análisis de Rentabilidad
- Margen real vs. planificado por proyecto
- Productos/categorías más rentables
- Identificación de proyectos problemáticos

## Implementación Simple
- Queries directos a BD (sin OLAP)
- Gráficos simples (barras, líneas, torta)
- Exportación a Excel como alternativa
- Sin sistemas BI complejos