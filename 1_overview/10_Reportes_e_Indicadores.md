# 10. Reportes e Indicadores Clave

> **Propósito:** Este documento describe los reportes y dashboards que estarán disponibles en la V1.0 del sistema, enfocándose en las preguntas de negocio que podrán ser respondidas. También clarifica las limitaciones del sistema de reportes inicial para alinear expectativas.

---

## Filosofía de Reporting (V1.0)

Para la primera versión del sistema, la estrategia de reporting se basa en dos principios fundamentales:

1.  **Foco en la Eficiencia Operativa:** Los reportes están diseñados para responder preguntas que ayuden a los analistas y líderes a gestionar el día a día, identificar cuellos de botella y controlar la rentabilidad de los proyectos en curso.
2.  **Simplicidad Técnica para Acelerar la Entrega:** Se ha optado por una implementación técnica directa y sin herramientas de Business Intelligence (BI) complejas. Esto permite lanzar el sistema más rápido y validar su valor central, a la vez que establece una base de datos sólida para futuros análisis más avanzados.

---

## Preguntas de Negocio que SÍ se Podrán Responder

El sistema V1.0 estará capacitado para responder de forma rápida y visual a las siguientes preguntas críticas para la operación.

### Sobre la Operación y Flujos de Trabajo
- ¿Cuál es el estado actual de todos mis proyectos asignados?
- ¿Qué tareas específicas requieren mi atención hoy?
- ¿Qué proyectos están en riesgo de atrasarse según su cronograma planificado?
- ¿Cuál es la carga de trabajo actual de cada analista del equipo?
- ¿Cuál es el tiempo promedio que nuestros proyectos pasan en cada fase (Sourcing, Producción, etc.)?

### Sobre la Rentabilidad y Finanzas
- ¿Cuál es la rentabilidad (margen real vs. presupuestado) del proyecto "Campaña Verano BeerCO"?
- ¿Cuáles son nuestros proyectos más y menos rentables de este trimestre?
- ¿Cómo se desglosan los costos reales (proveedor, logística, aduanas) para un proyecto específico?
- ¿Qué porcentaje de nuestra exposición a divisas está cubierta por contratos FEC?

### Sobre Clientes y Proveedores
- ¿Cuál es el historial de proyectos de un cliente específico?
- ¿Cuál es nuestro proveedor más puntual para la categoría "Textiles"? (Tasa de Entrega a Tiempo - OTD)
- ¿Qué proveedor tiene la tasa más baja de rechazo de muestras de pre-producción (PPS)?
- ¿Cómo han evolucionado los costos de un producto específico con un proveedor a lo largo del tiempo?

---

## Preguntas de Negocio que NO se Podrán Responder (en V1.0)

Nuestra decisión de arquitectura de usar un sistema flexible para las **especificaciones de producto** (el "Archivador Simple" que discutimos) nos da gran velocidad y agilidad, pero implica una limitación importante: **el sistema no puede buscar, filtrar o generar reportes sobre los detalles específicos de los productos.**

Las siguientes son ejemplos de preguntas que **NO** se podrán responder en la V1.0:

- **Sobre Atributos Específicos:**
    - "¿Cuántos productos de nuestro catálogo usan 'algodón orgánico' como material?"
    - "Muéstrame todas las poleras con un grosor (GSM) entre 160 y 180."
    - "¿Qué porcentaje de las botellas que vendemos tienen una capacidad de 500ml?"

- **Sobre Tendencias de Atributos:**
    - "¿Cuál es el color más cotizado en la categoría 'Gorros' este año?"
    - "¿Ha aumentado la demanda de productos con certificaciones 'BPA-Free'?"

**Justificación:** Responder a estas preguntas requeriría una arquitectura de base de datos mucho más compleja (la "Biblioteca Detallada"), lo que habría aumentado significativamente el costo y el tiempo de desarrollo inicial. Se ha priorizado el lanzamiento del núcleo operativo del sistema, dejando la puerta abierta para implementar estas capacidades analíticas avanzadas en una futura versión (V2.0) si el negocio lo requiere.

---

## Dashboards por Rol (Resumen)

Los dashboards serán las herramientas principales para responder a las preguntas permitidas:

*   **Dashboard Analista (Ventas, Adquisiciones, etc.):** Enfocado en responder "¿Qué tengo que hacer hoy?" y "¿Cuál es el estado de mis proyectos?".
*   **Dashboard Líder de Equipo:** Responde a "¿Cómo está operando mi equipo?" y "¿Qué proyectos necesitan mi atención?".
*   **Dashboard Financiero:** Responde a "¿Estamos cumpliendo las metas de rentabilidad?" y "¿Cómo se comparan nuestros costos reales con los presupuestados?".
