# 4. Tipos de Productos y Especificaciones

> **Propósito:** Define la clasificación de productos de PromoSmart y el sistema de especificaciones dinámicas y componibles que permite una gestión flexible y escalable del catálogo.

---

## 1. Visión General del Sistema de Clasificación

La taxonomía de productos de PromoSmart es un sistema flexible que permite una definición precisa y consistente de cada artículo.

### A. Jerarquía de Productos
Los productos se organizan en una jerarquía simple y clara:
1.  **Categoría:** Agrupación de alto nivel que corresponde a una línea de negocio (ej. `Textiles`, `Merchandising`).
2.  **Subcategoría:** Clasificación específica dentro de una categoría (ej. `<PERSON>ras`, `Botellas de Agua`).

### B. Composición de Especificaciones
En lugar de que cada subcategoría tenga una lista fija y única de atributos, el sistema utiliza un modelo de **composición** más potente y reutilizable:

1.  **Grupos de Atributos:** Se definen bloques de características comunes como entidades centrales. Por ejemplo, existe un grupo "Dimensiones Físicas" (con largo, ancho, peso) y otro "Detalles de Impresión" (con área, técnicas, etc.).
2.  **Composición por Subcategoría:** Cada subcategoría **selecciona los grupos de atributos que necesita**. Una "Polera" se compone de los grupos `Material`, `Dimensiones` e `Impresión`. Un "Power Bank" se compone de `Dimensiones` y `Eléctrico`.
3.  **Almacenamiento:** Los valores finales para un producto específico (ej. una polera talla L) se guardan en una única estructura de datos (JSON) en ese producto, organizada por los grupos a los que pertenece.

### Beneficios de este Sistema
- ✅ **Reutilización y Consistencia (DRY):** El grupo "Dimensiones" se define una sola vez y se reutiliza en todas las subcategorías que lo necesiten, garantizando consistencia.
- ✅ **Escalabilidad:** Añadir una nueva subcategoría es tan simple como componerla a partir de grupos de atributos ya existentes.
- ✅ **Flexibilidad:** Permite personalizaciones a nivel de subcategoría si un grupo necesita una ligera variación (ej. diferentes opciones de materiales para poleras y para sudaderas).
- ✅ **Claridad para el Usuario:** Los formularios en la interfaz se presentarán agrupados lógicamente ("Datos de Material", "Datos de Impresión"), haciendo la entrada de datos más intuitiva.

---

## 2. Grupos de Atributos Principales

A continuación se listan ejemplos de los grupos de atributos que forman la base del sistema.

*   **Información Básica:** Nombre comercial, descripción, audiencia objetivo.
*   **Especificaciones de Material:** Tipo de material principal, composición detallada, certificaciones.
*   **Dimensiones Físicas:** Largo, ancho, alto, peso, volumen.
*   **Detalles de Impresión:** Técnicas disponibles, área máxima de impresión, número de colores.
*   **Detalles de Packaging:** Tipo de empaque individual, unidades por caja, dimensiones de la caja.
*   **Especificaciones Eléctricas:** Capacidad de batería, puertos de entrada/salida, voltaje.

Cada subcategoría, como "Poleras Premium", se construirá combinando estos bloques.

---

## 3. Casos de Uso y Ejemplos

### Ejemplo 1: Subcategoría "Polera Premium"
*   **Grupos Compuestos:** `Material` + `Dimensiones` + `Impresión` + `Packaging`.
*   **Resultado:** El formulario para crear una Polera Premium mostrará secciones claras para cada uno de estos grupos, solicitando al usuario rellenar los campos correspondientes (GSM, Tallas, Técnicas de Impresión, etc.).

### Ejemplo 2: Subcategoría "Power Bank"
*   **Grupos Compuestos:** `Material` + `Dimensiones` + `Eléctrico` + `Impresión` + `Packaging`.
*   **Resultado:** El formulario para un Power Bank mostrará secciones para Material, Dimensiones, y además una sección de "Especificaciones Eléctricas" para definir la capacidad (mAh), puertos, etc.

---

## 4. Productos con Variantes y Kits

El sistema también soporta configuraciones más complejas sobre esta base.

### Productos con Variantes de Diseño
*   **Concepto:** Permite manejar un `ProductItem` que es técnicamente idéntico pero tiene múltiples diseños (ej. 500 poleras con 5 logos diferentes).
*   **Gestión:** Se negocia el costo por la cantidad total, pero cada variante de diseño requiere su propia aprobación de maqueta virtual.

### Kits y Paquetes de Productos (Bundles)
*   **Concepto:** Permite agrupar diferentes `ProductItems` que se venden como un solo paquete comercial (ej. "Kit de Bienvenida" con una polera, una botella y un cuaderno).
*   **Gestión:** Se cotiza como una sola línea al cliente, pero cada componente del kit mantiene su propio ciclo de vida operativo y de costos.
