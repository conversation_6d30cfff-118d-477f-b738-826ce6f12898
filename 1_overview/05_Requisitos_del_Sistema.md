# 5. Requisitos del Sistema

> **Propósito:** Define qué debe hacer el sistema (requisitos funcionales) y cómo debe comportarse (requisitos no funcionales). Establece criterios de aceptación, estándares de rendimiento y limitaciones técnicas.

---

## Contenido

1. [Requisitos Funcionales Core](#requisitos-funcionales-core)
2. [Requisitos de Integración](#requisitos-de-integración)
3. [Requisitos de Interfaz de Usuario](#requisitos-de-interfaz-de-usuario)
4. [Requisitos No Funcionales](#requisitos-no-funcionales)
5. [Criterios de Aceptación y Testing](#criterios-de-aceptación-y-testing)

---

## Requisitos Funcionales Core

### RF-01: Gestión de Proyectos

#### RF-01.1: Creación y Configuración de Proyectos
> **Historia:** "Como **Analista de Ventas**, quiero **crear un nuevo proyecto en el sistema fácilmente** para que **pueda registrar y gestionar una nueva oportunidad de negocio sin perder detalles**."

- **Funcionalidad:** El sistema debe permitir la creación de nuevos registros de `Project`.
- **Datos requeridos:** Cliente, `name` (nombre), `description` (descripción), `timeline` (cronograma) estimado.
- **Validaciones:**
  - El cliente (`Customer`) debe existir en el directorio o ser creado.
  - El `name` (nombre) del proyecto debe ser único por cliente.
  - El `timeline` (cronograma) debe ser realista (entre 7 y 365 días).
- **Salida:** ID de proyecto (`Project ID`) único generado automáticamente.

#### RF-01.2: Gestión de ProductItems
- **Funcionalidad:** Cada `Project` debe soportar múltiples `ProductItem`s
- **Operaciones:** Crear, Leer, Actualizar (limitado), Eliminar (antes del **Hito de Compromiso**).
- **Restricciones:**
  - El `ProductItem` debe hacer referencia a una `ProductSubcategory` válida.
  - Las `specifications` deben cumplir con la `ProductSpecificationStructure`.
  - Las cantidades deben ser > 0 y ≤ 100,000 unidades.

#### RF-01.3: Estado y Ciclo de Vida
- **Funcionalidad:** Seguimiento automático de estados según el ciclo de vida del producto.
- **Fases principales:** Venta → Preparación → Cumplimiento y Entrega.
- **Transiciones:** Solo se permiten transiciones válidas según las reglas de negocio.
- **Disparadores:** Automáticos (sincronización logística) y manuales (acciones de usuario).

> **Referencia:** Ver [3. Modelo de Negocio - Ciclo de Vida del Producto](03_Modelo_de_Negocio.md#2-el-motor-del-proceso-el-ciclo-de-vida-del-producto) para detalles conceptuales.

### RF-02: Proceso de Cotización

#### RF-02.1: Gestión de Cotizaciones de Proveedores
- **Funcionalidad:** El sistema debe almacenar y comparar múltiples cotizaciones de proveedores.
- **Datos de seguimiento:** Costo unitario, tiempo de entrega, condiciones de pago, MOQ.
- **Comparación:** Interfaz de comparación lado a lado.
- **Selección:** Marcar opciones preferidas para cada `ProductItem`.

#### RF-02.2: Generación de Cotizaciones para Clientes
- **Funcionalidad:** Generar cotizaciones formales para clientes usando `data snapshots`.
- **Sistema de plantillas:** Generación de PDF profesional con la marca de la empresa.
- **Lógica de precios:** Aplicación de margen configurable (porcentaje o monto fijo).
- **Formatos de salida:** PDF como principal, plantilla de Word opcional.
- **Seguimiento de validez:** Fechas de vencimiento y flujos de trabajo de renovación.


#### RF-02.3: Historial de Versiones de Cotizaciones
- **Funcionalidad:** Mantener un historial de versiones principales de cotizaciones y cambios significativos.
- **Versionado:** Incremento automático (v1.0, v1.1, etc.).
- **Seguimiento de cambios:** Registrar modificaciones importantes con usuario, fecha y razón del cambio.
- **Visualización:** Ver listado de versiones con resumen de cambios principales.

### RF-03: Adquisiciones y Sourcing

#### RF-03.1: Gestión de Proveedores
- **Funcionalidad:** Directorio completo de proveedores con sus capacidades.
- **Datos almacenados:** Información de contacto, especializaciones, historial de rendimiento.
- **Seguimiento de rendimiento:** Calificaciones de calidad, rendimiento de entrega, competitividad de precios.
- **Integración:** Plantillas de email para la generación de RFQ.

#### RF-03.2: Generación de Órdenes de Compra
- **Funcionalidad:** Generar documentos formales de `SupplierPurchaseOrder`.
- **Datos requeridos:** `ProductFactsheets`, términos comerciales, requisitos de entrega.
- **Flujo de trabajo de aprobación:** Aprobación en múltiples pasos antes del envío.
- **Plantillas:** Formatos profesionales por tipo de proveedor.

#### RF-03.3: Monitoreo de Producción
- **Funcionalidad:** Rastrear el estado de la producción y las actualizaciones de progreso.
- **Seguimiento de comunicaciones:** Registrar todas las comunicaciones con los proveedores.
- **Gestión de cronograma:** Sistema de alertas para retrasos o problemas.
- **Actualizaciones de estado:** Capacidades de reporte de progreso regular.

### RF-04: Logística e Importaciones

#### RF-04.1: Gestión de Envíos de Importación
- **Funcionalidad:** Crear y gestionar Registros de Envío de Importación para consolidación.
- **Lógica de consolidación:** Optimizar múltiples productos en envíos únicos.
- **Seguimiento de estado:** Actualizaciones en tiempo real desde la recolección hasta la entrega.
- **Documentación:** Toda la documentación de importación/exportación requerida.

> **Referencia:** Ver [3. Modelo de Negocio - Registro de Envío de Importación](03_Modelo_de_Negocio.md#detalles-importantes-de-las-piezas-principales) para conceptos de negocio.

#### RF-04.2: Coordinación Logística
- **Funcionalidad:** Interfaz con agentes de carga y proveedores de logística.
- **Integración de seguimiento:** Seguimiento de envíos en tiempo real cuando esté disponible.
- **Seguimiento de costos:** Costos logísticos reales vs. estimados.
- **Manejo de excepciones:** Alertas por retrasos, daños, problemas de aduana.

#### RF-04.3: Gestión de Aduanas
- **Funcionalidad:** Apoyar el proceso de despacho de aduanas.
- **Documentación:** Generar/almacenar todos los documentos aduaneros requeridos.
- **Cálculo de aranceles:** Rastrear costos de aranceles reales vs. estimados.
- **Cumplimiento:** Asegurar que se cumplan todos los requisitos regulatorios.

### RF-05: Gestión Financiera

#### RF-05.1: Seguimiento de Costos
- **Funcionalidad:** Seguimiento integral de costos desde la cotización hasta la entrega.
- **Categorías:** Costos de proveedores, logística, aranceles, costos internos.
- **Análisis de varianza:** Comparaciones de presupuesto vs. real.
- **Multimoneda:** Soporte para USD (primario) y CLP (secundario).

#### RF-05.2: Contratos de Cambio a Plazo (FEC)
- **Funcionalidad:** Crear y rastrear contratos FEC para la gestión de riesgos cambiarios.
- **Integración:** Con instituciones financieras para la gestión de tasas.
- **Reportes:** Exposición a divisas y efectividad de la cobertura.
- **Liquidación:** Rastrear la liquidación y realización del contrato.

#### RF-05.3: Análisis de Rentabilidad
- **Funcionalidad:** Análisis de margen en tiempo real por proyecto y producto.
- **Seguimiento de baseline:** Comparar la rentabilidad real vs. la presupuestada.
- **Reportes:** Paneles de rentabilidad e informes ejecutivos.
- **Alertas:** Advertencias de degradación del margen.

### RF-06: Gestión de Documentos

#### RF-06.1: Generación de Artefactos
- **Funcionalidad:** Generar todos los documentos de negocio usando `data snapshots`.
- **Tipos soportados:** Cotizaciones, POs, facturas, fichas técnicas, informes.
- **Sistema de plantillas:** Plantillas configurables por tipo de documento.
- **Control de versiones:** Mantener versiones de artefactos e historial de cambios.

#### RF-06.2: Almacenamiento de Archivos
- **Funcionalidad:** Almacenamiento seguro para todos los archivos relacionados con el proyecto.
- **Formatos soportados:** PDF, Word, Excel, imágenes, archivos CAD.
- **Organización:** Almacenamiento jerárquico por proyecto y categoría.
- **Control de acceso:** Permisos de acceso a archivos basados en roles.

#### RF-06.3: Historial de Comunicaciones
- **Funcionalidad:** Registrar todas las comunicaciones relacionadas con cada proyecto.
- **Canales:** Email, teléfono, reuniones, notificaciones del sistema.
- **Búsqueda:** Búsqueda de texto completo en todas las comunicaciones.
- **Integración:** Integración con el sistema de correo electrónico cuando sea posible.

### RF-07: Gestión de Usuarios y Roles
> **Historia:** "Como **Líder de Equipo**, quiero **gestionar los usuarios y sus roles en el sistema** para que **pueda controlar el acceso a la información y asignar responsabilidades correctamente**."

- **Funcionalidad:**
  - Interfaz para Crear, Leer, Actualizar y Desactivar (CRUD) perfiles de usuario.
  - Asignación de roles predefinidos (ej. Analista de Ventas, Líder de Equipo) a los usuarios.
  - Funcionalidad para que un administrador pueda disparar un reseteo de contraseña.
- **Datos requeridos:** Nombre de usuario, email, rol, estado (activo/inactivo).
- **Control de Acceso:** Funcionalidad restringida a roles de `Líder de Equipo` o `Administrador`.

### RF-08: Gestión de Revisiones y Rechazos
> **Historia:** "Como **Analista de Ventas**, quiero **registrar formalmente el rechazo de una cotización de proveedor** para que **quede trazabilidad de la decisión y el Analista de Adquisiciones sepa qué acción tomar**."

- **Funcionalidad:**
  - Permitir la acción de "Rechazar" o "Solicitar Revisión" en puntos clave del flujo (ej. revisión de sourcing, aprobación de VM por el cliente).
  - Al ejecutar la acción, el sistema debe solicitar un campo de texto obligatorio con la "Razón del rechazo/revisión".
  - El sistema debe registrar quién y cuándo se realizó la acción, y notificar al responsable de la etapa anterior.
  - El estado del ítem debe transicionar automáticamente a un estado de revisión (ej. `REVISION_REQUESTED`).

### RF-09: Sistema de Notificaciones por Hitos
> **Historia:** "Como **Analista de Diseño**, quiero **recibir una notificación automática cuando un ítem está listo para crear la maqueta virtual** para que **pueda empezar mi trabajo sin demoras**."

- **Funcionalidad:**
  - Envío de notificaciones automáticas (en la aplicación y/o por email) a los usuarios relevantes cuando se registra un **Hito de Negocio** clave en el sistema.
  - Los usuarios deben poder configurar sus preferencias de notificación.
- **Hitos que disparan notificaciones (ejemplos):**
  - Asignación de un nuevo ítem o proyecto.
  - Un ítem bajo tu responsabilidad ha cambiado de estado (ej. de 'Cotizado al Cliente' a 'Pendiente Aprobación Maqueta Virtual').
  - Se requiere tu aprobación para un ítem.
  - Un plazo importante se acerca.
  - Alguien te menciona en un comentario.

### RF-10: Supervisión y Gestión de Equipo
> **Historia:** "Como **Líder de Equipo**, quiero **visualizar la carga de trabajo de mi equipo y el estado de todos los proyectos** para que **pueda identificar cuellos de botella y reasignar tareas**."

- **Funcionalidad:**
  - Un panel de control (dashboard) para roles de supervisión.
  - Widgets para visualizar la carga de trabajo por miembro del equipo (ej. número de ítems activos).
  - Vista consolidada de todos los proyectos, con capacidad de filtrar por estado o por responsable.
  - Capacidad para reasignar un ítem o proyecto de un analista a otro.
- **Control de Acceso:** Restringido a roles de `Líder de Equipo`.

### RF-11: Procesos de Soporte Operativo
#### RF-11.1: Corrección Manual de Estado
- **Funcionalidad:** Permitir a usuarios autorizados cambiar manually el estado de un `ProductItem` o `Project`.
- **Validaciones:**
  - La acción debe requerir un motivo obligatorio.
  - La acción debe quedar registrada en el historial de eventos inmutable, especificando el estado anterior, el nuevo, el usuario, la fecha y el motivo.
- **Control de Acceso:** Altamente restringido (ej. solo `Administrador`).

#### RF-11.2: Cancelación de Proyectos
- **Funcionalidad:** Permitir la transición de un proyecto a un estado `CANCELLED`.
- **Lógica de negocio:**
  - Si se cancela después del Hito de Compromiso, el sistema debe permitir registrar los costos incurridos hasta la fecha.
  - Los ítems asociados deben ser también cancelados y excluidos de las colas de trabajo activas.

#### RF-11.3: Archivo de Proyectos
- **Funcionalidad:** Implementar un mecanismo para archivar proyectos.
- **Lógica de negocio:**
  - Un proyecto puede ser archivado manual o automáticamente tras un tiempo configurable en un estado final (`COMPLETED`, `CANCELLED`).
  - Los proyectos archivados no deben aparecer en las vistas y reportes por defecto, pero deben ser accesibles a través de una búsqueda explícita.

### RF-12: Gestión de Datos Maestros (CRUD)
> **Historia:** "Como **Analista de Adquisiciones**, quiero **agregar un nuevo proveedor y mantener su información actualizada** para que **toda la empresa tenga acceso a datos confiables**."

- **Funcionalidad:**
  - Interfaces CRUD (Crear, Leer, Actualizar, Desactivar) completas para las entidades maestras del sistema.
- **Entidades cubiertas:** `Customer`, `Supplier`, `ProductCategory`, `ProductSubcategory`, `ProductSpecificationStructure`.
- **Validaciones:** Asegurar la integridad de los datos (ej. no permitir desactivar un proveedor que está asignado a un proyecto activo).

### RF-19: Gestión de Taxonomía de Productos
> **Historia:** "Como **Analista de Ventas**, quiero **seleccionar la categoría y subcategoría correcta de un producto** para que **el sistema me valide automáticamente las especificaciones obligatorias según el tipo de producto**."

#### RF-19.1: Clasificación Jerárquica de Productos
- **Funcionalidad:** Sistema de clasificación jerárquica de productos. Cada `ProductItem` pertenece a una `ProductSubcategory`, la cual define las especificaciones requeridas mediante la composición de `AttributeGroups` reutilizables.
- **Categorías principales:**
  - **Merchandising**: Productos promocionales de marca, regalos corporativos
  - **Material PDV**: Artículos para punto de venta y exhibición
  - **Textiles**: Productos cuya característica principal es construcción en tela
- **Subcategorías:** Clasificaciones específicas dentro de cada categoría (ej. drinking_items, signage_banners, garments).
- **Validaciones:**
  - Cada `ProductItem` debe estar asociado a una `ProductSubcategory` válida.
  - Las subcategorías deben tener una estructura de especificación definida.

#### RF-19.2: Validación de Especificaciones por Taxonomía
- **Funcionalidad:** Validación automática de especificaciones según la categoría del producto.
- **Validación estructural:**
  - Campos obligatorios según la `ProductSpecificationStructure` de la subcategoría.
  - Validación de tipos de datos y formatos según las reglas definidas.
- **Validación por categoría:**
  - **Merchandising**: Validar capacidades de volumen, certificaciones de materiales, cargas máximas.
  - **Material PDV**: Validar requisitos de ensamblaje, dimensiones plegadas, especificaciones de montaje.
  - **Textiles**: Validar GSM, tallas disponibles, instrucciones de cuidado, construcción de prendas.
- **Validación de integridad:** Coherencia entre categoría y especificaciones (ej. materiales textiles para categoría Textiles).

#### RF-19.3: Plantillas de Especificación Dinámicas
- **Funcionalidad:** Generación automática de formularios de especificación según la subcategoría seleccionada.
- **Campos dinámicos:** El sistema debe mostrar solo los campos relevantes para la subcategoría.
- **Ayuda contextual:** Descripción y ejemplos de cada campo según la categoría.
- **Validación en tiempo real:** Feedback inmediato sobre campos faltantes o incorrectos.

#### RF-19.4: Gestión de Estructuras de Especificación
> **Historia:** "Como **Administrador**, quiero **modificar los campos obligatorios para la subcategoría 'Poleras'** para que **el sistema se adapte a nuevos requisitos de negocio sin cambios de código**."

- **Funcionalidad:** Interfaz administrativa para gestionar las estructuras de especificación.
- **Operaciones:**
  - Definir campos obligatorios y opcionales por subcategoría.
  - Configurar reglas de validación específicas.
  - Establecer validaciones particulares por categoría.
- **Versionado:** Mantener historial de cambios en las estructuras.
- **Impacto:** Validar que los cambios no afecten `ProductItem`s existentes.

### RF-20: Gestión de Muestras y Prototipos
> **Historia:** "Como **Analista de Importaciones**, quiero **rastrear el estado de las muestras físicas enviadas a los clientes** para que **pueda hacer seguimiento efectivo y coordinar aprobaciones**."

- **Funcionalidad:**
  - Registro de envío de muestras con tracking code
  - Estados: Solicitada, Enviada, En tránsito, Entregada, Aprobada, Rechazada
  - Gestión de múltiples iteraciones por producto
  - Registro de costos asociados a cada muestra
- **Validaciones:**
  - Solo se pueden enviar muestras de productos en estado apropiado
  - Tracking de todas las versiones de muestras
- **Integración:** Coordinar con servicios de courier y agentes de carga

### RF-21: Gestión de Excepciones y Escalaciones
> **Historia:** "Como **Líder de Equipo**, quiero **manejar cambios del cliente después del compromiso** para que **pueda evaluar el impacto y decidir si proceder**."

- **Funcionalidad:**
  - Registro de change orders post-compromiso
  - Flujo de aprobación para cambios excepcionales
  - Cálculo automático de impacto en costos y cronograma
  - Escalación automática por tiempo (ej. >3 días sin respuesta)
- **Estados especiales:** 
  - PENDING_CHANGE_ORDER, CHANGE_APPROVED, CHANGE_REJECTED
- **Control:** Restricción a roles autorizados (Líder, Admin)

### RF-22: Configuración del Sistema
> **Historia:** "Como **Administrador**, quiero **configurar parámetros de negocio sin cambios de código** para que **el sistema se adapte a nuevas políticas empresariales**."

- **Funcionalidad:**
  - Configuración de márgenes por defecto por categoría de producto
  - Umbrales de alertas (ej. proyectos >X días sin avance)
  - Tiempos de escalación por tipo de proceso
  - Parámetros de integración (APIs, emails)
- **Validaciones:**
  - Solo administradores pueden modificar configuraciones críticas
  - Versionado de cambios de configuración
- **Impacto:** Cambios deben aplicarse sin reinicio del sistema

### RF-23: Manejo de Divisas y Tipos de Cambio
> **Historia:** "Como **Analista Financiero**, quiero **fijar el tipo de cambio en el momento correcto** para que **los márgenes no se vean afectados por fluctuaciones cambiarias**."

- **Funcionalidad:**
  - Política configurable de momento de fijación (cotización vs. orden vs. pago)
  - Registro de tipo de cambio histórico por transacción
  - Cálculo de variación cambiaria vs. tasa fijada
  - Integración con proveedores de tipos de cambio en tiempo real
- **Divisas soportadas:** USD (principal), CLP (local), EUR (ocasional)
- **Contratos Forward (FEC):**
  - Registro de contratos FEC
  - Seguimiento de liquidación
  - Cálculo de efectividad de cobertura

### RF-24: Calidad y Control de Procesos
> **Historia:** "Como **Líder de Equipo**, quiero **checkpoints automáticos de calidad** para que **no se envíen documentos o información incompleta a los clientes**."

- **Funcionalidad:**
  - Checkpoints obligatorios antes de transiciones críticas
  - Validación de completitud de información antes de envío
  - Métricas de calidad por analista y por proceso
  - Alertas por anomalías (ej. muchos rechazos de un analista)
- **Checkpoints clave:**
  - Antes de enviar cotización: validar márgenes, especificaciones
  - Antes de orden a proveedor: validar términos, especificaciones
  - Antes de crear ISR: validar documentación completa
- **Métricas:**
  - Tasa de rechazo por analista
  - Tiempo promedio por fase
  - Proyectos que requieren re-trabajo

### RF-25: Gestión de Variantes de Diseño
> **Historia:** "Como **Analista de Ventas**, quiero **gestionar productos con múltiples diseños** para que **pueda manejar órdenes de gran volumen con diferentes artes aplicados eficientemente**."

- **Funcionalidad:**
  - Crear ProductItem con una o múltiples variantes de diseño
  - Gestión de archivos de arte por variante
  - Distribución de cantidades por diseño
  - Aprobación independiente de maquetas virtuales por variante
- **Flujo unificado:**
  - Sourcing y costos a nivel de ProductItem (cantidad total)
  - Aprobación de muestras físicas a nivel de ProductItem
  - Producción como lote único con múltiples artes
- **Validaciones:**
  - Suma de cantidades de variantes debe igualar cantidad total
  - Cada variante debe tener archivo de arte válido
  - Todas las variantes deben aprobar MV antes de avanzar

### RF-26: Gestión de Kits y Paquetes
> **Historia:** "Como **Analista de Ventas**, quiero **crear paquetes de productos diferentes** para que **pueda ofrecer kits comerciales atractivos manteniendo control operativo independiente**."

- **Funcionalidad:**
  - Crear ProductBundle que agrupa múltiples ProductItem
  - Asignar precio de venta único al paquete
  - Gestión independiente de componentes (sourcing, producción)
  - Cálculo automático de rentabilidad del kit
- **Operaciones:**
  - Cotización unificada del bundle al cliente
  - Sourcing separado por cada componente
  - Seguimiento independiente de estados por ProductItem
  - Coordinación logística para entrega conjunta
- **Validaciones:**
  - Bundle debe tener al menos 2 componentes
  - Precio de venta debe ser positivo
  - Todos los componentes deben estar en estado compatible
- **Reportes:**
  - Análisis de rentabilidad bundle vs. componentes individuales
  - Eficiencia de kits más vendidos

### RF-13: Búsqueda Global Unificada
> **Historia:** "Como **usuario**, quiero **introducir cualquier identificador (nombre de proyecto, ID de ítem, PO) en una única barra de búsqueda** para **encontrar toda la información relacionada instantáneamente, sin tener que navegar por distintos menús**."

- **Funcionalidad:**
  - Una barra de búsqueda prominente y accesible desde cualquier parte de la aplicación.
  - La búsqueda debe indexar y devolver resultados de las entidades principales: `Project`, `ProductItem`, `Customer`, `Supplier`, `ImportShipmentRecord`, `ProductCategory`, `ProductSubcategory`, y artefactos como cotizaciones y órdenes de compra.
  - Los resultados se deben presentar de forma categorizada para una fácil identificación.
  - Búsqueda por especificaciones de productos (ej. buscar "polera" debe encontrar todos los productos de la subcategoría garments).
- **Rendimiento:** Los resultados iniciales deben mostrarse en < 2 segundos.

### RF-14: Colaboración en Contexto
> **Historia:** "Como **Analista de Importaciones**, quiero **dejar un comentario en un `ProductItem` y mencionar al `@AnalistaDeVentas`** para que **la conversación quede registrada y él reciba una notificación**."

- **Funcionalidad:**
  - Un sistema de comentarios o un feed de actividad en las páginas de detalle de `Project` y `ProductItem`.
  - Soporte para menciones de usuarios (`@mención`) que disparen una notificación al usuario mencionado.
  - Capacidad para editar y eliminar los propios comentarios.
  - El historial de comentarios debe ser visible para todos los usuarios con acceso al ítem/proyecto.

### RF-15: Reportes Analíticos y de Inteligencia de Negocio
> **Historia:** "Como **Gerente**, quiero **ver un dashboard con el rendimiento histórico de mis proveedores** para que **pueda tomar decisiones informadas en futuras negociaciones**."

- **Funcionalidad:**
  - Una sección dedicada de "Analítica" en el sistema.
  - Dashboards pre-configurados para analizar tendencias y rendimiento histórico.
- **Dashboards Clave:**
  - **Análisis de Proveedores:** Métricas de entregas a tiempo (OTD), tasas de rechazo de muestras (PPS), evolución de costos por producto.
  - **Análisis de Rentabilidad:** Identificación de los productos, categorías o clientes más y menos rentables a lo largo del tiempo.
  - **Análisis de Eficiencia Operativa:** Medición de tiempos de ciclo promedio por fase del proyecto para identificar cuellos de botella.
- **Características:** Capacidad de filtrar por rangos de fecha, cliente, proveedor, etc.

### RF-16: Operaciones de Datos en Lote
> **Historia:** "Como **Analista de Ventas**, quiero **subir un archivo Excel con 30 ítems de producto para un nuevo proyecto** para que **se creen todos automáticamente, ahorrándome horas de trabajo**."

- **Funcionalidad:**
  - **Importación:** Una interfaz para la carga de archivos (CSV, XLSX) para la creación masiva de `ProductItem` en un proyecto. El sistema debe permitir mapear las columnas del archivo a los campos del sistema y validar los datos antes de la importación final.
  - **Exportación:** Un botón para "Exportar a Excel/CSV" en todas las vistas de datos tabulares principales (ej. lista de ítems de un proyecto, lista de proyectos).

### RF-17: Gestión de Plantillas de Documentos
> **Historia:** "Como **Administrador**, quiero **subir una nueva versión de nuestra plantilla de cotización en formato Word** para que **todos los analistas usen el formato más reciente sin intervención de desarrollo**."

- **Funcionalidad:**
  - Una interfaz en el panel de administración para la gestión de las plantillas de documentos mencionadas en `RF-06.1`.
- **Características:**
  - Subir y descargar archivos de plantilla (ej. `.docx`).
  - Mapear visualmente los placeholders de la plantilla (ej. `{{project_name}}`) a los campos de datos disponibles en el sistema.
  - Permitir el versionado de plantillas y la selección de la plantilla activa por defecto.

### RF-18: Visualizador de Hitos de Negocio
> **Historia:** "Como **Líder de Equipo**, quiero **ver la secuencia cronológica de hitos para un ítem de producto** para que **pueda entender rápidamente su historia y resolver cuellos de botella**."

- **Funcionalidad:**
  - Una interfaz dedicada para visualizar el **Log de Hitos de Negocio** de una entidad (principalmente `ProductItem`).
  - La vista principal debe ser un log cronológico de los hitos registrados.
  - Cada hito en el log debe detallar: Fecha/Hora, Usuario implicado, Hito de Negocio (ej. 'Maqueta Aprobada', 'Rechazo de Cliente'), y un resumen con el contexto o datos relevantes (ej. "Razón del rechazo: Precio elevado").
- **Características:**
  - **Filtrado:** Permitir filtrar el log por tipo de hito.
  - **Visibilidad:** El log debe ser fácilmente accesible desde la página de detalle del `ProductItem`.
- **Control de Acceso:** El acceso a esta interfaz está disponible para todos los roles con acceso al proyecto.

---

## Requisitos de Integración

### RI-01: Sistema Contable
- **Funcionalidad:** Integración con el sistema contable existente.
- **Sincronización de datos:** Transferencia automática de transacciones financieras.
- **Mapeo:** Mapeo del plan de cuentas para una categorización adecuada.
- **Tiempo:** Procesamiento en tiempo real o por lotes diarios.

### RI-02: Sistema de Correo Electrónico
- **Funcionalidad:** Integración SMTP para comunicaciones automatizadas.
- **Plantillas:** Plantillas de email para diferentes escenarios de negocio.
- **Seguimiento:** Confirmaciones de entrega y lectura de correos electrónicos.
- **Seguridad:** Protocolos de correo electrónico seguros (TLS, autenticación).

### RI-03: Cambio de Divisas
- **Funcionalidad:** Integración de tasas de cambio en tiempo real.
- **Proveedores:** Múltiples proveedores de tasas para redundancia.
- **Histórico:** Almacenamiento de tasas históricas para informes precisos.
- **Alertas:** Notificaciones de cambios significativos en las tasas.

### RI-04: APIs de Agentes de Carga
- **Funcionalidad:** Integración con proveedores de logística cuando esté disponible.
- **Seguimiento:** Actualizaciones del estado del envío en tiempo real.
- **Documentación:** Intercambio electrónico de documentos.
- **Facturación:** Conciliación de costos automatizada.

---

## Requisitos de Interfaz de Usuario

### RIU-01: Panel Principal
- **Funcionalidad:** Panel unificado con una visión general de todos los proyectos activos.
- **Widgets:** Widgets configurables por rol de usuario.
- **Actualizaciones en tiempo real:** Actualizaciones de estado y notificaciones en vivo.
- **Diseño responsivo:** Soporte para dispositivos móviles y tabletas.

### RIU-02: Interfaz de Gestión de Proyectos
- **Funcionalidad:** Pantallas completas de gestión de proyectos.
- **Vistas:** Opciones de vista de lista, tablero Kanban, diagrama de Gantt.
- **Filtrado:** Filtrado avanzado por estado, cliente, analista, fechas.
- **Operaciones masivas:** Operaciones de selección múltiple cuando sea apropiado.

### RIU-03: Reportes Financieros
- **Funcionalidad:** Informes y paneles financieros interactivos.
- **Gráficos:** Representaciones visuales de rentabilidad, costos, tendencias.
- **Exportación:** Exportar a Excel, PDF para compartir externamente.
- **Desglose:** Capacidad de desglosar desde el resumen hasta el detalle.

### RIU-04: Soporte Móvil
- **Funcionalidad:** Interfaz responsiva para operaciones clave.
- **Funciones principales:** Actualizaciones de estado, aprobaciones, notificaciones.
- **Capacidad sin conexión:** Funcionalidad sin conexión limitada para rutas críticas.
- **Aplicación nativa:** Considerar una aplicación móvil nativa para fases futuras.

---

## Requisitos No Funcionales

### RNF-01: Rendimiento

#### RNF-01.1: Tiempo de Respuesta
- **Carga del panel:** ≤3 segundos para la carga inicial.
- **Navegación de página:** ≤2 segundos para cargas de página estándar.
- **Generación de informes:** ≤10 segundos para informes estándar.
- **Informes complejos:** ≤30 segundos para informes financieros complejos.
- **Carga de archivos:** Soporte para archivos de hasta 50MB con indicadores de progreso.
- **Impacto en el negocio:** Un sistema rápido permite a nuestros analistas trabajar de forma más eficiente, generar cotizaciones más rápido y atender a más clientes. Un sistema lento frustra al equipo, retrasa los procesos y puede impactar negativamente en la satisfacción del cliente.

#### RNF-01.2: Rendimiento (Throughput)
- **Usuarios concurrentes:** Soportar hasta 50 usuarios concurrentes.
- **Carga máxima:** Manejar 2x la carga normal durante los períodos de máxima actividad.
- **Consultas a la base de datos:** ≤500ms para el 95% de las consultas.
- **Respuestas de API:** ≤1 segundo para el 99% de las llamadas a la API.

#### RNF-01.3: Escalabilidad
- **Crecimiento de usuarios:** La arquitectura debe soportar un crecimiento de hasta 200 usuarios.
- **Volumen de datos:** Manejar hasta 10,000 proyectos y 100,000 `ProductItem`s.
- **Almacenamiento:** Plan para más de 1TB de almacenamiento de documentos.
- **Base de datos:** Indexación optimizada para el rendimiento a escala.

### RNF-02: Disponibilidad

#### RNF-02.1: Tiempo de Actividad (Uptime)
- **Disponibilidad objetivo:** 99.5% de tiempo de actividad (≤36 horas de inactividad/año).
- **Horario comercial:** 99.9% de disponibilidad durante 8 AM - 6 PM, hora de Chile.
- **Mantenimiento planificado:** ≤4 horas de ventana de mantenimiento mensual.
- **Tiempo de recuperación:** ≤1 hora de tiempo de recuperación para fallas del sistema.

#### RNF-02.2: Copia de Seguridad y Recuperación
- **Copia de seguridad de la base de datos:** Copias de seguridad diarias automatizadas con retención de 30 días.
- **Copia de seguridad de archivos:** Copias de seguridad incrementales diarias de archivos.
- **Recuperación ante desastres:** Capacidad de espera activa (hot standby) en 4 horas.
- **Integridad de los datos:** Verificación regular de copias de seguridad y pruebas de restauración.

### RNF-03: Seguridad

#### RNF-03.1: Autenticación y Autorización
- **Autenticación de usuarios:** Inicio de sesión seguro con requisitos de complejidad de contraseña.
- **Gestión de sesiones:** Cierre de sesión automático después de 30 minutos de inactividad.
- **Acceso basado en roles:** Permisos granulares por rol de usuario.
- **Autenticación multifactor:** 2FA opcional para administradores.

#### RNF-03.2: Protección de Datos
- **Cifrado de datos:** TLS 1.3 para datos en tránsito.
- **Cifrado de base de datos:** Cifrado en reposo para datos sensibles.
- **Cumplimiento de PCI:** Manejo seguro de la información relacionada con los pagos.
- **Registro de acceso:** Rastro de auditoría completo de las acciones del usuario.

#### RNF-03.3: Seguridad de Red
- **Protección de firewall:** Segmentación de red adecuada.
- **Acceso VPN:** Capacidades de acceso remoto seguro.
- **Actualizaciones regulares:** Gestión automatizada de parches de seguridad.
- **Escaneo de vulnerabilidades:** Evaluaciones de seguridad regulares.

### RNF-04: Usabilidad

#### RNF-04.1: Experiencia de Usuario
- **Curva de aprendizaje:** Nuevos usuarios productivos en 2 horas de formación.
- **Consistencia de la interfaz:** Patrones de interfaz de usuario consistentes en todas las pantallas.
- **Manejo de errores:** Mensajes de error claros y accionables.
- **Sistema de ayuda:** Ayuda contextual y documentación.

#### RNF-04.2: Accesibilidad
- **Navegación por teclado:** Soporte completo para la navegación por teclado.
- **Lectores de pantalla:** Compatible con lectores de pantalla estándar.
- **Contraste de color:** Cumplir con los estándares WCAG 2.1 AA.
- **Tamaño de fuente:** Opciones de tamaño de texto configurables.

### RNF-05: Compatibilidad

#### RNF-05.1: Soporte de Navegadores
- **Navegadores principales:** Chrome, Firefox, Safari (últimas 2 versiones).
- **Internet Explorer:** Soporte mínimo para IE 11.
- **Navegadores móviles:** iOS Safari, Android Chrome.
- **JavaScript:** Funciones de ES6+ con polyfills apropiados.

#### RNF-05.2: Sistema Operativo
- **SO del servidor:** Basado en Linux (se prefiere Ubuntu 20.04+).
- **SO del cliente:** Windows 10+, macOS 10.15+, iOS 13+, Android 9+.
- **Base de datos:** PostgreSQL 12+.
- **Servidor web:** Apache 2.4+ o Nginx 1.18+.

### RNF-06: Mantenibilidad

#### RNF-06.1: Calidad del Código
- **Documentación:** Documentación completa del código.
- **Pruebas:** Más del 80% de cobertura de código con pruebas automatizadas.
- **Estándares:** Seguir los estándares de codificación establecidos.
- **Control de versiones:** Control de versiones basado en Git con una ramificación adecuada.

#### RNF-06.2: Despliegue
- **Despliegue automatizado:** Pipeline de CI/CD para despliegues consistentes.
- **Gestión de entornos:** Entornos de desarrollo, staging y producción separados.
- **Configuración:** Gestión de la configuración específica del entorno.
- **Monitoreo:** Monitoreo del rendimiento de la aplicación (APM).

---

## Criterios de Aceptación y Pruebas

### CA-01: Pruebas Funcionales

#### CA-01.1: Procesos de Negocio Centrales
- **Ciclo de vida del proyecto:** Proyecto completo de extremo a extremo, desde la creación hasta la entrega.
- **Generación de cotizaciones:** Generar cotizaciones precisas para los clientes con precios adecuados.
- **Seguimiento de estado:** Verificar que todas las transiciones de estado funcionen correctamente.
- **Seguimiento financiero:** Cálculos precisos de costos y rentabilidad.

#### CA-01.2: Pruebas de Integración
- **Consistencia de datos:** Verificar la integridad de los datos en todos los módulos.
- **Funcionalidad de la API:** Probar todas las integraciones de API internas y externas.
- **Manejo de archivos:** Carga, almacenamiento y recuperación de todos los tipos de archivos.
- **Integración de correo electrónico:** Envío y seguimiento automatizado de correos electrónicos.

### CA-02: Pruebas de Rendimiento

#### CA-02.1: Pruebas de Carga
- **Carga normal:** Probar el sistema en condiciones normales de funcionamiento.
- **Carga máxima:** Probar escenarios con el doble de la carga normal.
- **Pruebas de estrés:** Determinar los puntos de quiebre del sistema.
- **Rendimiento de la base de datos:** Rendimiento de las consultas bajo carga.

#### CA-02.2: Pruebas de Experiencia de Usuario
- **Tiempos de respuesta:** Verificar que se cumplan todos los requisitos de tiempo de respuesta.
- **Flujo de trabajo del usuario:** Probar flujos de trabajo completos del usuario para cada rol.
- **Manejo de errores:** Verificar el manejo elegante de errores y la recuperación.
- **Experiencia móvil:** Probar la funcionalidad de la interfaz móvil.

### CA-03: Pruebas de Seguridad

#### CA-03.1: Control de Acceso
- **Autenticación:** Probar el inicio de sesión y la gestión de sesiones.
- **Autorización:** Verificar los controles de acceso basados en roles.
- **Protección de datos:** Probar el cifrado y el manejo seguro de datos.
- **Rastros de auditoría:** Verificar el registro completo de auditoría.

#### CA-03.2: Pruebas de Vulnerabilidad
- **Pruebas de penetración:** Evaluación de seguridad profesional.
- **Inyección de SQL:** Probar las medidas de seguridad de la base de datos.
- **Protección XSS:** Pruebas de vulnerabilidad de Cross-Site Scripting.
- **Cumplimiento de OWASP:** Abordar las 10 principales vulnerabilidades de OWASP.

### CA-04: Pruebas de Aceptación del Usuario

#### CA-04.1: Pruebas Basadas en Roles
- **Sales Analyst:** Probar el flujo de trabajo de ventas completo.
- **Procurement Analyst:** Probar los flujos de trabajo de sourcing y adquisiciones.
- **Import Analyst:** Probar los procesos de logística e importación.
- **Finance Analyst:** Probar el seguimiento financiero y los informes.

#### CA-04.2: Pruebas de Escenarios de Negocio
- **Proyectos estándar:** Probar escenarios típicos de proyectos.
- **Manejo de excepciones:** Probar escenarios de error y recuperación.
- **Proyectos complejos:** Probar proyectos con múltiples productos y problemas.
- **Reportes:** Probar todos los informes estándar y personalizados.

---

## Métricas de Éxito

### Métricas Técnicas
- **Tiempo de actividad del sistema:** ≥99.5%
- **Tiempos de carga de la página:** ≤3 segundos de promedio.
- **Tasas de error:** ≤0.1% de errores de aplicación.
- **Satisfacción del usuario:** ≥4.0/5.0 en encuestas de usuarios.

### Métricas de Negocio
- **Adopción de usuarios:** ≥90% de los usuarios objetivo utilizando activamente el sistema.
- **Eficiencia del proceso:** ≥30% de reducción en el tiempo de procesamiento manual.
- **Precisión de los datos:** ≥99% de precisión en el seguimiento financiero.
- **Tiempo de formación:** ≤2 horas para la productividad de un nuevo usuario.

---

## Referencias

- **Fuente original**: Requisitos inferidos de procesos documentados y best practices de la industria
- **Documentos relacionados**: 
  - [`03_Modelo_de_Negocio.md`](./03_Modelo_de_Negocio.md)
  - [`02_Proceso_y_Actores.md`](./02_Proceso_y_Actores.md)
  - [`../2_architecture/12_glosario.md`](../2_architecture/12_glosario.md)
