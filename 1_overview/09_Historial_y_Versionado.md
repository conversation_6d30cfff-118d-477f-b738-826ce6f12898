# 9. Historial y Versionado: La Memoria del Sistema

> **Propósito:** Este documento ofrece una descripción exhaustiva de los dos mecanismos complementarios que conforman la "memoria" del sistema. Define qué información se registra en el **historial de eventos** (la narrativa) y qué estados de entidades se guardan como **versiones** inmutables (la evidencia), estableciendo un enfoque selectivo que captura valor de negocio real.

---

## 1. Filosofía: Registro Selectivo con Propósito

El sistema no registra cada cambio. La filosofía es el **registro selectivo**, diseñado para responder dos preguntas de negocio fundamentales de la manera más eficiente posible:

1.  **¿Qué ha pasado?** -> La respuesta la tiene el **Historial de Eventos**.
2.  **¿Cómo era esto exactamente en un momento clave?** -> La respuesta la tiene el **Versionado de Estados**.

Este enfoque dual evita la complejidad y los costos de un registro total, enfocándose en la información que tiene valor demostrable para la operación, la resolución de disputas y la mejora de procesos.

---

## 2. Mecanismo 1: Historial de Eventos (La Narrativa)

Este mecanismo es el "diario de vida" o la bitácora de cada entidad importante. Su objetivo es contar la historia de un proyecto o producto de una manera legible para los humanos.

#### Concepto
Se implementa como un **log de actividad** (`Activity Log`) que registra acciones discretas y significativas. Cada entrada en el historial es un evento inmutable que sigue una estructura clara:

> `[Actor]` realizó la acción `[Verbo]` sobre el `[Objeto]` con `[Contexto Adicional]` en `[Fecha]´.

#### ¿Qué se Registra en el Historial?
Se registran únicamente las acciones que alteran el estado o añaden contexto relevante:

*   **Cambios de Estado:** Transiciones en el ciclo de vida (ej. de `Sourcing` a `Cotizado`).
*   **Aprobaciones y Rechazos:** Decisiones formales de negocio (ej. `Maqueta Aprobada`, `Sourcing Rechazado`).
*   **Comunicaciones Clave:** Comentarios y notas importantes dejadas por los usuarios.
*   **Acciones Excepcionales:** Correcciones manuales de estado por parte de un administrador.
*   **Hitos de Versionado:** Se registra el evento de que una nueva versión fue creada (ej. `Versión 2 de Cotización Cliente generada`).

#### Estructura de un Evento del Historial

| Componente | Ejemplo | Descripción |
| :--- | :--- | :--- |
| **Actor** | `Roberto (Analista de Ventas)` | El usuario o sistema que inicia la acción. |
| **Verbo** | `Rechazó` | La acción específica que se realizó. |
| **Objeto** | `Sourcing de Proveedor para 'Hieleras'` | La entidad sobre la que se actuó. |
| **Contexto** | `Motivo: "Precio excede presupuesto en 15%"` | Datos adicionales que dan sentido a la acción. |
| **Fecha** | `21-08-2025 10:45` | El timestamp exacto del evento. |

#### Caso de Uso Detallado
*   **Pregunta de Negocio:** "Este proyecto de poleras parece simple, ¿por qué tardó 3 semanas en pasar a producción?"
*   **Respuesta con el Historial:** Un líder de equipo revisa el historial del `ProductItem` y ve:
    1.  `10/08 - 15:20`: Luisa (Diseño) subió `Maqueta Virtual v1`.
    2.  `11/08 - 09:30`: Roberto (Ventas) registró `Rechazo de Cliente` con motivo: "El logo es muy pequeño".
    3.  `12/08 - 11:00`: Luisa (Diseño) subió `Maqueta Virtual v2`.
    4.  `17/08 - 16:00`: Daniel (Importaciones) registró `Muestra Física Recibida`.
    5.  `18/08 - 12:30`: Roberto (Ventas) registró `Rechazo de Cliente` con motivo: "El color de la muestra no coincide con el pantone solicitado".
    *   El historial cuenta una historia clara de iteraciones y rechazos que justifica la duración del proceso.

---

## 3. Mecanismo 2: Versionado de Estados (La Evidencia)

Este mecanismo es la "cámara fotográfica" del sistema. Su objetivo es tomar una instantánea completa e inmutable de una entidad en un momento de importancia contractual o financiera.

#### Concepto
Se implementa mediante **snapshots JSON** que congelan el estado completo de un objeto. Una versión no es un registro de un cambio, sino una copia de todos los datos del objeto en ese instante.

#### Criterios para el Versionado
No se crea una versión con cada cambio. El versionado se activa solo en **puntos de no retorno** o **hitos de compromiso**, tales como:
*   Cuando se presenta una oferta formal a un cliente.
*   Cuando se confirma un acuerdo y se establece una línea base.
*   Cuando se aprueban las especificaciones finales para producción.

#### Entidades y Momentos Clave para el Versionado

| Entidad | Trigger de Versionado | Valor de Negocio | Retención |
|:---|:---|:---|:---|
| **Proyecto** | Al crear **línea base** (Hito de Compromiso) | Control financiero, análisis de rentabilidad | 7 años |
| **Producto** | Cambios de estado críticos (ver abajo) | Trazabilidad operativa, disputas | 3 años |
| **Cotización Cliente** | Cada versión enviada al cliente | Historial comercial, disputas | 5 años |
| **Orden de Compra** | Creación y modificaciones mayores | Compliance, relación con proveedores | 7 años |

#### Caso de Uso Detallado
*   **Pregunta de Negocio:** "El cliente alega que en la primera cotización le ofrecimos llaveros de metal, no de plástico. ¿Es correcto?"
*   **Respuesta con el Versionado:** El analista abre la `Cotización Cliente v1`. El sistema no reconstruye nada, simplemente carga y muestra la **versión #1** del `ProductItem` (el snapshot JSON) que se guardó en ese momento. En sus datos se lee `"material": "plástico"`. El snapshot es la evidencia inmutable que resuelve la disputa al instante.

---

## 4. Sinergia e Interacción entre Historial y Versionado

Los dos sistemas trabajan en conjunto para ofrecer una trazabilidad completa. Se conectan de una manera muy simple:

> Cuando se crea una **Versión**, se registra un evento en el **Historial**.

**Ejemplo del Flujo:**
1.  Roberto (Ventas) presiona el botón "Enviar Cotización al Cliente".
2.  **Acción de Versionado:** El sistema crea un snapshot JSON de la `CustomerQuotation` y sus `ProductItems`, guardándola como **Versión 2**.
3.  **Acción de Historial:** Inmediatamente después, el sistema añade una entrada al historial del proyecto: `"Roberto (Ventas) generó la Versión 2 de la Cotización al Cliente"`. Esta entrada en el historial puede incluir una referencia directa al ID de la versión creada.

De esta forma, el **Historial** cuenta la historia completa, incluyendo los momentos en que se tomaron las "fotografías" (las **Versiones**).

---

## 5. Gobernanza y Casos de Uso Avanzados

Estos mecanismos también son fundamentales para la gobernanza del sistema y la continuidad del negocio.

#### Auditoría y Cumplimiento Normativo
*   **Versionado Bajo Demanda:** Un `Administrador` puede generar una **Versión (snapshot)** de cualquier entidad en cualquier momento para "congelar" un estado antes de una auditoría.
*   **Exportación de Historial:** El **Historial de Eventos** de una entidad debe ser exportable (ej. a CSV) para proveer una narrativa clara de las acciones a un auditor.
*   **Retención Legal (Legal Hold):** El sistema permitirá marcar entidades para que ignoren las políticas de retención automáticas si están bajo investigación.

#### Recuperación y Continuidad del Negocio
*   **Restauración desde una Versión:** En caso de error humano o corrupción de datos, un `Administrador` puede navegar por las versiones pasadas de un objeto, seleccionar una confiable y restaurar el estado actual a partir de ese snapshot.
*   **Trazabilidad de la Restauración:** Toda acción de restauración debe ser, a su vez, un evento registrado en el **Historial**, asegurando total transparencia.