# 9. Historial y Versionado

> **Propósito:** Define qué información se registra en el **historial** de eventos y qué estados de entidades se guardan como **versiones** inmutables, estableciendo un enfoque selectivo que captura valor de negocio real sin generar complejidad técnica innecesaria.

---

## Filosofía: Registro Selectivo con Propósito

**<PERSON><PERSON><PERSON><PERSON>:** *"Solo registrar lo que tiene valor de negocio demostrable"*

El sistema implementa un registro selectivo que se enfoca en dos mecanismos distintos:
1.  **Historial de Eventos:** Un log cronológico de las acciones importantes para entender la narrativa de un proyecto.
2.  **Versionado de Estados:** "Fotografías" completas e inmutables de una entidad en momentos críticos para preservar el estado exacto.

### Por Qué NO Implementar un Registro Total

- ❌ **Overhead de almacenamiento**: Crecimiento exponencial de datos sin valor.
- ❌ **Complejidad técnica**: Siste<PERSON> de versionado o historiales genéricos son difíciles de mantener.
- ❌ **Performance**: Impacto negativo en los tiempos de respuesta del sistema.
- ❌ **Mantenimiento**: Altos costos de gestión de datos históricos.

---

## Mecanismo 1: Historial de Eventos

Responde a la pregunta: **"¿Qué ha pasado?"**. Es un log legible que cuenta la historia de una entidad.

*   **Implementación:** Un `Activity Log` que registra acciones clave.
*   **Ejemplos de Eventos Registrados en el Historial:**
    *   `Proyecto Creado` por @usuario en fecha.
    *   `Estado de ProductItem cambiado` de `Sourcing` a `Cotizado` por @usuario.
    *   `Maqueta Virtual Rechazada` por @cliente con motivo: "..."
    *   `Comentario añadido` por @usuario: "..."

## Mecanismo 2: Versionado de Estados

Responde a la pregunta: **"¿Cómo era esto exactamente en ese momento?"**. Es una fotografía completa de los datos de una entidad.

*   **Implementación:** Creación de un **snapshot JSON** inmutable en puntos clave del ciclo de vida.

### Entidades y Momentos Clave para el Versionado

| Entidad | Trigger de Versionado | Valor de Negocio | Retención |
|:---|:---|:---|:---|
| **Proyecto** | Al crear **línea base** (Hito de Compromiso) | Control financiero, análisis de rentabilidad | 7 años |
| **Producto** | Cambios de estado críticos (ver abajo) | Trazabilidad operativa, disputas | 3 años |
| **Cotización Cliente** | Cada versión enviada al cliente | Historial comercial, disputas | 5 años |
| **Orden de Compra** | Creación y modificaciones mayores | Compliance, relación con proveedores | 7 años |

### Estados Críticos que Disparan el Versionado de un `ProductItem`

```
✅ QUOTED_TO_CUSTOMER → Se crea una versión al cotizar al cliente.
✅ PENDING_VM_APPROVAL → Se crea una versión al confirmar el proyecto.
✅ READY_FOR_PRODUCTION → Se crea una versión al aprobar para producción.
✅ DELIVERED → Se crea la versión final al entregar.
```

--- 

## Estructura de una Versión (Snapshot JSON)

Se utiliza un enfoque de snapshot JSON almacenado directamente en la entidad principal o en una tabla relacionada.

```json
{
  "entity_type": "ProductItem",
  "entity_id": 123,
  "version": 3,
  "reason": "READY_FOR_PRODUCTION",
  "created_at": "2024-01-15T10:30:00Z",
  "created_by": 45,
  "data": {
    "name": "Hielera Personalizada BeerCO",
    "status": "ready_for_production",
    "specifications": { /* estado completo de las especificaciones */ },
    "financial_data": { /* costos y márgenes en este punto */ }
  }
}
```

### Ventajas del Enfoque Snapshot
- ✅ **Simplicidad**: No requiere tablas de historial complejas.
- ✅ **Atomicidad**: El estado completo se captura en un solo registro.
- ✅ **Performance**: La recuperación de una versión específica es muy rápida.
- ✅ **Flexibilidad**: La estructura JSON se adapta a cambios futuros en el modelo.

---

## Casos de Uso para Cada Mecanismo

### Uso del Historial (El "Qué Pasó")

*   **Pregunta:** "¿Por qué este producto está atrasado?"
*   **Respuesta:** El analista revisa el **historial de eventos** y ve una secuencia como: "Maqueta Rechazada" -> "Comentario: Ajustar logo" -> "Nueva Maqueta Subida". Esto cuenta la historia y explica la demora.

### Uso del Versionado (El "Cómo Era")

*   **Pregunta:** "El cliente dice que el precio que le facturamos no es el que aprobó en la cotización inicial. ¿Qué le enviamos?"
*   **Respuesta:** El sistema recupera la **versión** de la `CustomerQuotation` con `version: 1` y muestra la "fotografía" exacta de los precios y especificaciones de ese momento, resolviendo la disputa.

---

## Política de Retención de Datos

La política de retención se aplica de forma diferenciada según el tipo de dato.

| Categoría | Período | Acción Post-Retención |
|:---|:---|:---|
| **Datos Financieros (Versiones)** | 7 años | Archivo en almacenamiento frío |
| **Datos Operativos (Versiones)** | 3 años | Eliminación completa |
| **Historial de Eventos** | 5 años | Archivo comprimido |
| **Logs de Acceso al Sistema** | 1 año | Eliminación automática |

Este enfoque dual de **Historial y Versionado** balancea la necesidad de una narrativa operativa clara con la de una evidencia inmutable, optimizando tanto la usabilidad como la robustez del sistema.
