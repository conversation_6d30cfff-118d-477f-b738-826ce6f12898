# 11. Especificación Detallada - Máquina de Estados ImportShipmentRecord

> **Propósito:** Especificación técnica detallada de la máquina de estados del Registro de Envío de Importación para validación completa por parte del negocio y desarrollo.

---

## Contenido

1. [Tabla de Transiciones Válidas](#tabla-de-transiciones-válidas)
2. [Matriz de Transiciones Prohibidas](#matriz-de-transiciones-prohibidas)
3. [Eventos de Negocio y Efectos Secundarios](#eventos-de-negocio-y-efectos-secundarios)
4. [Reglas de Negocio por Estado](#reglas-de-negocio-por-estado)
5. [Casos de Uso para Validación](#casos-de-uso-para-validación)
6. [Flujos de Excepción](#flujos-de-excepción)
7. [Sincronización con ProductItems](#sincronización-con-productitems)
8. [Validación de Consistencia](#validación-de-consistencia)

---

## Tabla de Transiciones Válidas

### Transiciones Principales del Flujo Logístico

| Estado Origen | Estado Destino | Disparador | Precondiciones | Actor Responsable | Validaciones del Sistema |
|---|---|---|---|---|---|
| **Planning** | PickupScheduled | Manual | • Al menos 1 ProductItem asignado<br>• Fechas estimadas definidas<br>• Proveedor confirmado | Import Analyst | • Validar ProductItems en estado InProduction<br>• Verificar capacidad de envío<br>• Confirmar datos del proveedor |
| **PickupScheduled** | InTransit | Manual | • Proveedor confirma recolección<br>• Documentos de embarque generados<br>• Tracking number asignado | Import Analyst | • Tracking number válido<br>• Documentos de embarque presentes<br>• Fecha de departure_date registrada |
| **InTransit** | ArrivedPort | Automático | • Envío llega a puerto destino<br>• Notificación de llegada recibida | Sistema/Import Analyst | • Actualización automática vía tracking<br>• Fecha de arrival_date registrada<br>• Documentos de llegada validados |
| **ArrivedPort** | CustomsClearance | Manual | • Documentación aduanera iniciada<br>• Agente aduanero asignado<br>• Valoración aduanera confirmada | Import Analyst | • Documentos aduaneros completos<br>• Valoración dentro de rangos normales<br>• Agente aduanero válido |
| **CustomsClearance** | Cleared | Manual | • Aranceles pagados<br>• Despacho aduanero completado<br>• Autorización de liberación obtenida | Import Analyst | • Comprobante de pago de aranceles<br>• Autorización aduanera válida<br>• Costos reales registrados |
| **Cleared** | DomesticDelivery | Manual | • Transporte doméstico coordinado<br>• Destino final confirmado<br>• Documentos de entrega preparados | Import Analyst | • Transportista asignado<br>• Dirección de entrega validada<br>• Documentos completos |
| **DomesticDelivery** | Delivered | Manual | • Entrega confirmada<br>• Productos recibidos por cliente<br>• Documentación firmada | Import Analyst | • Confirmación de entrega<br>• Documentos firmados<br>• Sincronización con ProductItems |

### Transiciones de Excepción y Retroceso

| Estado Origen | Estado Destino | Disparador | Motivo | Actor Responsable | Validaciones |
|---|---|---|---|---|---|
| **PickupScheduled** | Planning | Manual | Proveedor cancela o retrasa recolección | Import Analyst | Registrar motivo del retraso |
| **InTransit** | ArrivedPort | Manual | Actualización manual por falta de tracking automático | Import Analyst | Verificar documentos de llegada |
| **CustomsClearance** | ArrivedPort | Manual | Problemas aduaneros requieren re-documentación | Import Analyst | Documentar problemas específicos |
| **DomesticDelivery** | Cleared | Manual | Problemas con transporte doméstico | Import Analyst | Registrar incidencia de transporte |

---

## Matriz de Transiciones Prohibidas

### Transiciones Absolutamente Prohibidas

| Desde → Hacia | Razón de Prohibición | Excepción Posible |
|---|---|---|
| **Planning** → **InTransit** | Saltar coordinación y recolección | Solo Admin con justificación |
| **Planning** → **Delivered** | Imposible entregar sin proceso logístico | Ninguna |
| **ArrivedPort** → **InTransit** | Retroceso logísticamente imposible | Ninguna |
| **Delivered** → **cualquier estado** | Proceso completado, productos entregados | Solo corrección de errores de sistema |
| **Cleared** → **CustomsClearance** | Despacho ya completado | Solo en caso de inspección posterior |
| **DomesticDelivery** → **InTransit** | Productos ya en territorio nacional | Ninguna |

### Transiciones Restringidas (Requieren Aprobación Especial)

| Desde → Hacia | Restricción | Aprobación Requerida | Casos Válidos |
|---|---|---|---|
| **InTransit** → **Planning** | Retroceso mayor con impacto de costos | Team Leader + Finance Analyst | Cancelación de envío, error crítico |
| **CustomsClearance** → **Planning** | Retroceso extremo con implicaciones legales | Team Leader + Legal | Productos prohibidos, documentación falsa |
| **Delivered** → **CustomsClearance** | Re-inspección post-entrega | Autoridades Aduaneras | Inspección gubernamental posterior |

---

## Eventos de Negocio y Efectos Secundarios

### Eventos Críticos del Sistema

| Transición | Evento de Negocio | Efectos Secundarios Automáticos | Notificaciones |
|---|---|---|---|
| **Planning** → **PickupScheduled** | **Consolidación Finalizada** | • Bloquear modificaciones de ProductItems del envío<br>• Calcular costos logísticos estimados<br>• Generar documentos de embarque | • Email a proveedor (recolección programada)<br>• Email a Team Leader (envío consolidado)<br>• Actualización en dashboard |
| **PickupScheduled** → **InTransit** | **Inicio de Transporte** | • **Sincronizar todos los ProductItems a InternationalTransit**<br>• Activar seguimiento automático<br>• Calcular ETA estimado | • SMS/Email a clientes (productos en camino)<br>• Email a Import Analyst (seguimiento activo)<br>• Actualización automática de dashboard |
| **InTransit** → **ArrivedPort** | **Llegada a Destino** | • **Sincronizar ProductItems a CustomsClearance (preparación)**<br>• Iniciar proceso aduanero<br>• Calcular costos de almacenaje portuario | • Email a agente aduanero<br>• Email a Import Analyst (iniciar aduanas)<br>• Alerta de costos de demora |
| **ArrivedPort** → **CustomsClearance** | **Inicio Proceso Aduanero** | • Generar declaración aduanera<br>• Calcular aranceles estimados<br>• Iniciar cronómetro de almacenaje | • Email a Finance (pago de aranceles)<br>• Email a clientes (proceso aduanero)<br>• Dashboard update de costos |
| **CustomsClearance** → **Cleared** | **Despacho Completado** | • **Sincronizar ProductItems a CustomsClearance (finalizado)**<br>• Registrar costos reales de aduanas<br>• Liberar productos para entrega doméstica | • Email a transportista doméstico<br>• Email a clientes (productos liberados)<br>• Actualización de costos reales |
| **Cleared** → **DomesticDelivery** | **Inicio Entrega Doméstica** | • Coordinar múltiples entregas por proyecto<br>• Generar guías de despacho<br>• Activar seguimiento doméstico | • Email/SMS a clientes (entrega programada)<br>• Email a Import Analyst (coordinación)<br>• Tracking doméstico activo |
| **DomesticDelivery** → **Delivered** | **Entrega Completada** | • **Sincronizar todos los ProductItems a Delivered**<br>• Cerrar proceso logístico<br>• Calcular costos finales y varianzas | • Email a clientes (confirmación entrega)<br>• Email a Finance (cierre de costos)<br>• Reporte final de entrega |

### Eventos de Rollback y Excepción

| Transición de Retroceso | Evento de Negocio | Efectos Secundarios | Notificaciones |
|---|---|---|---|
| **PickupScheduled** → **Planning** | Cancelación de Recolección | • Reabrir consolidación para cambios<br>• Recalcular fechas estimadas<br>• Notificar retraso a clientes | • Email a Team Leader (retraso)<br>• Email a clientes afectados |
| **CustomsClearance** → **ArrivedPort** | Problema Aduanero | • Re-documentar productos<br>• Registrar costos adicionales<br>• Escalar problema | • Email a Team Leader (escalación)<br>• Email a Legal (si es necesario) |

---

## Reglas de Negocio por Estado

### Permisos y Restricciones por Estado

| Estado | Campos Editables | Campos Bloqueados | Acciones Permitidas | Reglas Especiales |
|---|---|---|---|---|
| **Planning** | • ProductItems asignados<br>• Fechas estimadas<br>• Puerto de origen<br>• Notas de consolidación | Ninguno | • Agregar/quitar ProductItems<br>• Modificar fechas<br>• Cambiar proveedor<br>• Cancelar envío | Único estado donde se pueden modificar ProductItems |
| **PickupScheduled** | • Fechas confirmadas<br>• Documentos de embarque<br>• Tracking number<br>• Notas de recolección | • ProductItems consolidados<br>• Puerto origen<br>• Proveedor | • Actualizar documentos<br>• Modificar fechas (±2 días)<br>• Confirmar recolección | ProductItems bloqueados para cambios |
| **InTransit** | • Ubicación actual<br>• ETA estimado<br>• Documentos de tránsito<br>• Incidencias de viaje | • Datos de recolección<br>• ProductItems<br>• Puertos definidos | • Seguimiento automático<br>• Actualizar ubicación<br>• Registrar incidencias | Seguimiento principalmente automático |
| **ArrivedPort** | • Fecha de llegada real<br>• Documentos de arribo<br>• Estado de descarga<br>• Preparación aduanera | • Datos de tránsito<br>• Tracking de origen<br>• ProductItems | • Verificar llegada<br>• Preparar documentos aduaneros<br>• Coordinar inspección | Estado de transición crítico |
| **CustomsClearance** | • Documentos aduaneros<br>• Valoración oficial<br>• Aranceles calculados<br>• Estado de tramitación | • ProductItems finales<br>• Datos de envío<br>• Puertos | • Pagar aranceles<br>• Gestionar inspecciones<br>• Corregir documentos | Estado crítico para costos |
| **Cleared** | • Autorización de liberación<br>• Costos finales aduaneros<br>• Documentos oficiales<br>• Coordinación entrega | • Valoración aduanera<br>• Aranceles pagados<br>• ProductItems | • Coordinar transporte doméstico<br>• Programar entregas<br>• Generar guías | Productos liberados oficialmente |
| **DomesticDelivery** | • Programación entregas<br>• Transportistas asignados<br>• Rutas de entrega<br>• Estado de entregas | • Documentos aduaneros<br>• Costos internacionales<br>• ProductItems | • Coordinar múltiples entregas<br>• Actualizar programación<br>• Gestionar incidencias domésticas | Coordinación compleja de entregas |
| **Delivered** | • Confirmaciones entrega<br>• Documentos firmados<br>• Evaluación post-entrega<br>• Cierre de proceso | Todos los datos históricos | • Confirmar entregas finales<br>• Cerrar documentación<br>• Análisis de performance | Estado final - solo lectura histórica |

---

## Casos de Uso para Validación

### Estados Especiales - Decisión de Diseño

**IMPORTANTE:** El ImportShipmentRecord utiliza **8 estados principales** optimizados para el flujo logístico real, eliminando estados intermedios innecesarios:

| Etapa Logística | Estados del Sistema | Justificación |
|---|---|---|
| **Planificación** | Planning | Consolidación y preparación |
| **Recolección** | PickupScheduled | Coordinación con proveedor |
| **Transporte Internacional** | InTransit | Seguimiento oceánico/aéreo |
| **Llegada** | ArrivedPort | Transición crítica puerto/aeropuerto |
| **Proceso Aduanero** | CustomsClearance | Gestión de aranceles y permisos |
| **Liberación** | Cleared | Autorización para entrega doméstica |
| **Entrega Doméstica** | DomesticDelivery | Distribución local |
| **Completado** | Delivered | Cierre de proceso |

**Ventaja:** Cada estado representa una fase operativa real con actor responsable y acciones específicas.

### Caso de Uso 1: Envío Consolidado - Productos Textiles

**Contexto:** Consolidación de 3 proyectos (BeerCO camisas, FoodCorp delantales, TechCorp mochilas) desde proveedor en China

**Flujo Esperado:**
1. **Planning → PickupScheduled**
   - *ProductItems incluidos:* 500 camisas BeerCO, 200 delantales FoodCorp, 300 mochilas TechCorp
   - *Validación negocio:* ¿El sistema valida que todos los ProductItems estén en InProduction antes de consolidar?
   - *Sistema debe calcular:* Peso total, volumen total, costos estimados de transporte

2. **PickupScheduled → InTransit**
   - *Proveedor confirma:* Recolección programada para fecha X, tracking number asignado
   - *Validación negocio:* ¿Todos los ProductItems de los 3 proyectos cambian automáticamente a InternationalTransit?
   - *Sistema debe activar:* Seguimiento automático, notificaciones a 3 clientes diferentes

**Puntos Críticos de Validación:**
- **Sincronización masiva:** Cuando el envío pasa a InTransit, ¿se actualizan automáticamente los 1000 ProductItems?
- **Notificaciones diferenciadas:** ¿Se notifica por separado a cada cliente sobre sus productos específicos?

### Caso de Uso 2: Problema Aduanero - Documentación Incorrecta

**Contexto:** Envío con productos promocionales llega a puerto pero valoración aduanera es cuestionada

**Flujo con Complicaciones:**
1. **InTransit → ArrivedPort** ✓
2. **ArrivedPort → CustomsClearance** ✓
3. **CustomsClearance → ArrivedPort** ⚠️ (Autoridades requieren re-documentación)
4. **ArrivedPort → CustomsClearance** (Con documentos corregidos)
5. **CustomsClearance → Cleared** ✓

**Validaciones Específicas:**
- ¿El sistema registra los costos adicionales de almacenaje portuario durante la re-documentación?
- ¿Los ProductItems mantienen su sincronización durante el retroceso temporal?
- ¿Se notifica automáticamente a Finance sobre los sobrecostos?

### Caso de Uso 3: Entrega Múltiple - Un Envío, Varios Destinos

**Contexto:** Envío consolidado que debe entregarse en 5 ciudades diferentes

**Flujo de Distribución:**
1. **Cleared → DomesticDelivery** (Inicio coordinación)
2. **Gestión simultánea:** 5 transportistas diferentes, 5 ciudades, fechas escalonadas
3. **DomesticDelivery → Delivered** (Cuando la ÚLTIMA entrega se completa)

**Validación de Negocio:** ¿El sistema puede manejar entregas parciales sin cambiar el estado del ImportShipmentRecord hasta que todo esté entregado?

---

## Flujos de Excepción

### Excepciones por Problemas Logísticos

| Situación | Estado Actual | Problema | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Cancelación de Proveedor** | PickupScheduled | Proveedor no puede recolectar en fecha | Planning | • Reabrir consolidación<br>• Buscar proveedor alternativo<br>• Recalcular fechas |
| **Pérdida de Carga** | InTransit | Contenedor perdido o dañado | Planning | • Activar seguro de carga<br>• Re-planificar producción<br>• Comunicar emergencia a clientes |
| **Retraso Mayor** | InTransit | Demora >7 días vs fecha estimada | InTransit | • Actualizar ETAs automáticamente<br>• Notificar clientes<br>• Calcular costos de demora |

### Excepciones por Problemas Aduaneros

| Situación | Estado Actual | Problema Aduanero | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Valoración Cuestionada** | CustomsClearance | Autoridades cuestionan valor declarado | ArrivedPort | • Contratar agente especialista<br>• Re-documentar productos<br>• Registrar sobrecostos |
| **Productos Prohibidos** | CustomsClearance | Productos no permitidos detectados | Planning | • Activar proceso legal<br>• Separar productos afectados<br>• Gestionar devolución |
| **Inspección Física** | CustomsClearance | Inspección exhaustiva requerida | CustomsClearance | • Coordinar inspección<br>• Calcular costos adicionales<br>• Gestionar tiempos extra |

### Excepciones por Problemas de Entrega

| Situación | Estado Actual | Problema de Entrega | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Cliente No Disponible** | DomesticDelivery | Cliente no puede recibir en fecha programada | DomesticDelivery | • Re-programar entrega<br>• Calcular costos de almacenaje<br>• Actualizar cronograma |
| **Dirección Incorrecta** | DomesticDelivery | Dirección de entrega errónea | DomesticDelivery | • Corregir dirección<br>• Coordinar nueva entrega<br>• Aplicar costo adicional |
| **Productos Dañados** | DomesticDelivery | Daños detectados al momento de entrega | DomesticDelivery | • Activar seguro<br>• Documentar daños<br>• Gestionar reclamo |

### Excepciones Internas del Sistema

| Situación | Disparador | Acción Automática del Sistema | Notificación |
|---|---|---|---|
| **Seguimiento Perdido** | Sin actualizaciones >48h en InTransit | Solicitar actualización manual | Email a Import Analyst |
| **Sobrecosto Crítico** | Costos reales >150% estimados | Escalación automática | Email a Team Leader + Finance |
| **Retraso Crítico** | ETA vs real >10 días | Alerta de emergencia | SMS a Team Leader + Client notification |
| **ProductItems Huérfanos** | ProductItems sin ImportShipmentRecord >7 días | Identificar para consolidación | Email a Import Analyst |

---

## Sincronización con ProductItems

### Reglas de Sincronización Automática

**CRÍTICO:** El ImportShipmentRecord actúa como **maestro** para los estados logísticos de todos los ProductItems que transporta.

| Estado ImportShipmentRecord | Estado Automático ProductItems | Momento de Sincronización | Excepciones |
|---|---|---|---|
| **Planning** | Sin cambio | N/A | ProductItems deben estar en InProduction |
| **PickupScheduled** | Sin cambio | N/A | Se bloquean modificaciones de ProductItems |
| **InTransit** | **InternationalTransit** | Inmediatamente al cambio de estado | Ninguna |
| **ArrivedPort** | Sin cambio | N/A | Preparación para siguiente fase |
| **CustomsClearance** | **CustomsClearance** | Inmediatamente al cambio de estado | Ninguna |
| **Cleared** | Sin cambio | N/A | Productos liberados pero aún no entregados |
| **DomesticDelivery** | Sin cambio | N/A | Entrega en proceso |
| **Delivered** | **Delivered** | Inmediatamente al cambio de estado | Ninguna |

### Validaciones de Consistencia

| Validación | Descripción | Acción en Caso de Error |
|---|---|---|
| **ProductItems Válidos** | Todos los ProductItems deben existir y estar en estado válido | Bloquear transición |
| **Estados Coherentes** | ProductItems no pueden estar en estados posteriores al ImportShipmentRecord | Sincronizar automáticamente |
| **Proyectos Activos** | Todos los ProductItems deben pertenecer a proyectos no cancelados | Alertar pero permitir |
| **Capacidad de Envío** | Peso y volumen total dentro de límites del contenedor | Alertar sobre sobrecarga |

---

## Validación de Consistencia

### Checklist de Completitud de la Especificación

**✅ Estados Documentados (8 estados principales):**
- Planning, PickupScheduled, InTransit, ArrivedPort
- CustomsClearance, Cleared, DomesticDelivery, Delivered

**✅ Transiciones Válidas (7 principales + 4 retrocesos):**
- Flujo logístico completo desde consolidación hasta entrega
- Retrocesos documentados para puntos críticos
- Actores responsables definidos (Import Analyst principal)

**✅ Sincronización con ProductItems:**
- Reglas automáticas de sincronización especificadas
- Estados maestro-esclavo claramente definidos
- Validaciones de consistencia implementadas

**✅ Eventos Críticos:**
- Consolidación finalizada y efectos en ProductItems
- Sincronización masiva en transiciones clave
- Notificaciones diferenciadas por proyecto/cliente

**✅ Casos de Excepción:**
- Problemas logísticos (retrasos, pérdidas, cancelaciones)
- Problemas aduaneros (valoración, prohibiciones, inspecciones)
- Problemas de entrega (disponibilidad, direcciones, daños)

### Puntos de Validación para el Negocio

**Pregunta Clave 1:** ¿Esta especificación permite rastrear eficientemente múltiples productos de diferentes proyectos en un solo envío?
**Respuesta:** ✅ SÍ - La consolidación y sincronización automática están completamente especificadas.

**Pregunta Clave 2:** ¿Se pueden manejar los problemas logísticos y aduaneros reales sin romper la integridad del sistema?
**Respuesta:** ✅ SÍ - Retrocesos, excepciones y escalaciones están cubiertos con efectos secundarios documentados.

**Pregunta Clave 3:** ¿Los clientes reciben la información correcta sobre sus productos específicos durante todo el proceso?
**Respuesta:** ✅ SÍ - Notificaciones diferenciadas por proyecto y seguimiento granular especificado.

**Pregunta Clave 4:** ¿El sistema previene inconsistencias entre el estado del envío y los productos que transporta?
**Respuesta:** ✅ SÍ - Reglas de sincronización automática y validaciones de consistencia implementadas.

---

## Referencias

- **Documentos relacionados:**
  - [03_Modelo_de_Negocio.md](03_Modelo_de_Negocio.md) → Conceptos y flujos de negocio
  - [08_Diccionario_Negocio_Sistema.md](08_Diccionario_Negocio_Sistema.md) → Mapeo de terminología
  - [09_Especificacion_Maquina_Estados_ProductItem.md](09_Especificacion_Maquina_Estados_ProductItem.md) → Sincronización con ProductItems
  - [05_Requisitos_del_Sistema.md](05_Requisitos_del_Sistema.md) → Requisitos funcionales RF-04
- **Implementación técnica:** Ver `2_architecture/05_bloques_construccion.md#59-dominio-de-logística-importshipmentrecord`
- **Patrones de implementación:** Ver `3_tech_design/patterns/` para ejemplos de código