# 9. Especificación Detallada - Máquina de Estados ProductItem

> **Propósito:** Especificación técnica detallada de la máquina de estados del ProductItem para validación completa por parte del negocio y desarrollo.

---

## Contenido

1. [Tabla de Transiciones Válidas](#tabla-de-transiciones-válidas)
2. [Matriz de Transiciones Prohibidas](#matriz-de-transiciones-prohibidas)
3. [Eventos de Negocio y Efectos Secundarios](#eventos-de-negocio-y-efectos-secundarios)
4. [Reglas de Negocio por Estado](#reglas-de-negocio-por-estado)
5. [Casos de Uso para Validación](#casos-de-uso-para-validación)
6. [Flujos de Excepción](#flujos-de-excepción)
7. [Validación de Consistencia](#validación-de-consistencia)

---

## Tabla de Transiciones Válidas

### Transiciones Principales del Flujo Normal

| Estado Origen | Estado Destino | Disparador | Precondiciones | Actor Responsable | Validaciones del Sistema |
|---|---|---|---|---|---|
| **Draft** | ReadyForSourcing | Manual | • Especificaciones completas según categoría<br>• Cliente válido asignado<br>• Cantidad > 0 | Sales Analyst | • Validar campos obligatorios por ProductSubcategory<br>• Verificar cliente existe<br>• Cantidad entre 1 y 100,000 |
| **ReadyForSourcing** | SourcingInProgress | Manual | • Especificaciones bloqueadas para sourcing<br>• Analista de Adquisiciones asignado | Sales Analyst | • Validar especificaciones completas<br>• Asignar Procurement Analyst responsable |
| **SourcingInProgress** | InternalReviewPending | Manual | • Al menos 1 cotización de proveedor<br>• Precio unitario > 0<br>• Lead time definido | Procurement Analyst | • Validar cotización completa<br>• Verificar datos comerciales |
| **InternalReviewPending** | QuotedToCustomer | Manual | • Aprobación del Team Leader<br>• Margen mínimo cumplido (≥15%)<br>• Cotización generada | Team Leader | • Validar margen calculado<br>• PDF de cotización generado<br>• Fecha de vigencia establecida |
| **QuotedToCustomer** | PendingVmApproval | Manual | • Cliente acepta cotización (CPO registrado)<br>• **HITO DE COMPROMISO** | Sales Analyst | • CPO válido registrado<br>• Crear línea base del proyecto<br>• Bloquear modificaciones críticas |
| **PendingVmApproval** | PendingPpsApproval | Manual | • Cliente aprueba maqueta virtual<br>• Virtual Mockup adjunto | Design Analyst | • Archivo de maqueta presente<br>• Aprobación cliente registrada |
| **PendingPpsApproval** | ReadyForProduction | Manual | • Cliente aprueba muestra física<br>• Pre-Production Sample evaluado | Import Analyst | • Muestra evaluada y aprobada<br>• Especificaciones finales confirmadas |
| **ReadyForProduction** | InProduction | Manual | • Orden de compra enviada al proveedor<br>• Términos comerciales acordados | Procurement Analyst | • SupplierPurchaseOrder generada<br>• Fechas de producción confirmadas |
| **InProduction** | InternationalTransit | Automático | • Proveedor confirma envío<br>• ProductItem asignado a ImportShipmentRecord<br>• ImportShipmentRecord pasa a InTransit | Import Analyst | • Tracking number válido<br>• Sincronización con ImportShipmentRecord<br>• Ver [Especificación ImportShipmentRecord](11_Especificacion_Maquina_Estados_ImportShipmentRecord.md#sincronización-con-productitems) |
| **InternationalTransit** | CustomsClearance | Automático | • Envío llega a puerto destino<br>• ImportShipmentRecord pasa a CustomsClearance<br>• Documentación aduanera iniciada | Import Analyst | • Sincronización automática con ImportShipmentRecord<br>• Documentos aduaneros presentes |
| **CustomsClearance** | Delivered | Automático | • ImportShipmentRecord pasa a Delivered<br>• Entrega confirmada del envío completo | Import Analyst | • Sincronización automática con ImportShipmentRecord<br>• Documentos de entrega del envío |

### Transiciones de Excepción y Retroceso

| Estado Origen | Estado Destino | Disparador | Motivo | Actor Responsable | Validaciones |
|---|---|---|---|---|---|
| **InternalReviewPending** | SourcingInProgress | Manual | Cotización insuficiente o incorrecta | Team Leader | Registrar motivo del retroceso |
| **QuotedToCustomer** | ReadyForSourcing | Manual | Cliente solicita cambios significativos | Sales Analyst | Registrar cambios solicitados |
| **PendingVmApproval** | ReadyForSourcing | Manual | Cliente rechaza maqueta virtual | Sales Analyst | Registrar feedback del cliente |
| **PendingPpsApproval** | ReadyForSourcing | Manual | Cliente rechaza muestra física | Sales Analyst | Registrar razones de rechazo |
| **InProduction** | ReadyForProduction | Manual | Problemas de fabricación | Procurement Analyst | Registrar incidencia de producción |
| **SourcingInProgress** | ReadyForSourcing | Manual | Necesidad de cambiar especificaciones o proveedor | Procurement Analyst | Documentar razón del cambio |

---

## Matriz de Transiciones Prohibidas

### Transiciones Absolutamente Prohibidas

| Desde → Hacia | Razón de Prohibición | Excepción Posible |
|---|---|---|
| **Draft** → **InProduction** | Saltar validaciones críticas de sourcing y aprobaciones | Solo Admin con justificación documentada |
| **Draft** → **Delivered** | Imposible entregar sin pasar por proceso completo | Ninguna |
| **Delivered** → **cualquier estado** | Proceso ya completado, producto entregado | Solo corrección de errores de sistema |
| **CustomsClearance** → **InProduction** | Retroceso logísticamente imposible | Ninguna |
| **InternationalTransit** → **InProduction** | Retroceso logísticamente imposible | Ninguna |
| **PendingVmApproval** → **InProduction** | Saltar aprobación de muestra física obligatoria | Ninguna |
| **ReadyForSourcing** → **PendingVmApproval** | Saltar proceso de sourcing completo | Ninguna |
| **Draft** → **QuotedToCustomer** | Saltar sourcing y validaciones | Ninguna |

### Transiciones Restringidas (Requieren Aprobación Especial)

| Desde → Hacia | Restricción | Aprobación Requerida | Casos Válidos |
|---|---|---|---|
| **InProduction** → **Draft** | Retroceso mayor con impacto financiero | Team Leader + Finance Analyst | Cancelación de proyecto, error crítico |
| **PendingPpsApproval** → **InProduction** | Saltar aprobación de muestra | Team Leader | Cliente acepta riesgo por escrito |
| **Cualquier estado** → **Draft** | Reset completo del proceso | Admin | Reconfiguración mayor del producto |

---

## Eventos de Negocio y Efectos Secundarios

### Eventos Críticos del Sistema

| Transición | Evento de Negocio | Efectos Secundarios Automáticos | Notificaciones |
|---|---|---|---|
| **QuotedToCustomer** → **PendingVmApproval** | **Hito de Compromiso** | • Crear ProjectBaseline (línea base)<br>• Bloquear especificaciones críticas<br>• Activar seguimiento de costos reales | • Email a Design Team (nueva tarea VM)<br>• Email a Finance (proyecto confirmado)<br>• Alerta Team Leader (seguimiento activo) |
| **PendingVmApproval** → **PendingPpsApproval** | Aprobación de Maqueta Virtual | • Crear tarea para solicitud de muestra<br>• Actualizar cronograma estimado | • Email a Procurement (solicitar muestra)<br>• Email a cliente (muestra en proceso) |
| **ReadyForProduction** → **InProduction** | Inicio de Fabricación | • Activar seguimiento de producción<br>• Crear calendario de seguimiento | • Email a Import Analyst (preparar logística)<br>• Email a cliente (producción iniciada) |
| **InProduction** → **InternationalTransit** | Envío Confirmado | • Actualizar ImportShipmentRecord<br>• Sincronizar estados de todos los productos del envío<br>• Calcular fecha estimada de llegada | • Email a Import Analyst (seguimiento)<br>• SMS/Email a cliente (producto en camino)<br>• Actualización automática de dashboard |

### Eventos de Rollback y Excepción

| Transición de Retroceso | Evento de Negocio | Efectos Secundarios | Notificaciones |
|---|---|---|---|
| **PendingVmApproval** → **ReadyForSourcing** | Rechazo de Maqueta | • Crear tarea de rediseño<br>• Registrar feedback del cliente<br>• Pausar cronograma | • Email a Design Team (correcciones)<br>• Email a Team Leader (retraso) |
| **PendingPpsApproval** → **ReadyForSourcing** | Rechazo de Muestra | • Crear tarea de re-sourcing<br>• Registrar problemas de calidad<br>• Re-evaluar proveedor | • Email a Procurement (buscar alternativas)<br>• Email a Team Leader (incidencia) |

---

## Reglas de Negocio por Estado

### Permisos y Restricciones por Estado

| Estado | Campos Editables | Campos Bloqueados | Acciones Permitidas | Reglas Especiales |
|---|---|---|---|---|
| **Draft** | • Todas las especificaciones<br>• Cantidad<br>• Descripción | Ninguno | • Editar libremente<br>• Eliminar ProductItem<br>• Cambiar categoría | Único estado donde se puede eliminar |
| **ReadyForSourcing** | • Notas internas<br>• Proveedores sugeridos | • Especificaciones<br>• Cantidad (cambio >10% prohibido) | • Iniciar sourcing<br>• Retroceder a Draft | Cambios de especificaciones requieren aprobación |
| **SourcingInProgress** | • Cotizaciones de proveedores<br>• Análisis comparativo<br>• Notas de sourcing | • Especificaciones core<br>• Cliente<br>• Categoría | • Agregar cotizaciones<br>• Comparar proveedores<br>• Avanzar o retroceder | Al menos 1 cotización para avanzar |
| **InternalReviewPending** | • Margen aplicado<br>• Condiciones comerciales<br>• Notas de aprobación | • Costos base<br>• Especificaciones<br>• Cliente | • Aprobar/rechazar<br>• Ajustar márgenes | Solo Team Leader puede aprobar |
| **QuotedToCustomer** | • Estado de seguimiento<br>• Notas de cliente<br>• Vigencia de cotización | • Precios<br>• Especificaciones<br>• Condiciones | • Registrar CPO<br>• Extender vigencia<br>• Manejar cambios menores | Estado crítico para Hito de Compromiso |
| **PendingVmApproval** | • Virtual Mockup<br>• Comentarios de diseño<br>• Iteraciones de diseño | • Especificaciones técnicas<br>• Precios comprometidos<br>• Cantidades finales | • Subir/actualizar VM<br>• Registrar aprobación cliente<br>• Manejar rechazos | Requiere Virtual Mockup adjunto y línea base activa |
| **PendingPpsApproval** | • Pre-Production Sample<br>• Evaluación de calidad<br>• Notas de inspección | • Especificaciones finales<br>• Precios comprometidos<br>• Cronograma base | • Evaluar muestra<br>• Aprobar/rechazar<br>• Solicitar ajustes | Evaluación obligatoria de calidad |
| **ReadyForProduction** | • Orden de compra<br>• Términos con proveedor<br>• Fechas de producción | • Especificaciones<br>• Cantidades finales<br>• Precios acordados | • Enviar orden de compra<br>• Negociar términos finales<br>• Iniciar producción | Estado pre-producción crítico |
| **InProduction** | • Estado de producción<br>• Comunicaciones con proveedor<br>• Fechas estimadas | • Especificaciones<br>• Cantidades<br>• Términos comerciales | • Seguimiento de progreso<br>• Actualizar cronograma<br>• Manejar incidencias | Solo Procurement puede actualizar |
| **InternationalTransit** | • Tracking logístico<br>• Documentos de embarque<br>• ETAs | • Datos de producción<br>• Especificaciones<br>• Costos de fabricación | • Seguimiento automático<br>• Actualización de documentos<br>• Preparar aduanas | Sincronización automática con ImportShipmentRecord |
| **CustomsClearance** | • Documentación aduanera<br>• Valuación<br>• Pagos de impuestos | • Datos del producto<br>• Documentos de origen<br>• Cantidades declaradas | • Gestionar trámites<br>• Pagar aranceles<br>• Coordinar liberación | Estado manejado por Import Analyst |
| **Delivered** | • Confirmación de entrega<br>• Evaluación post-entrega<br>• Cierre de proyecto | Todos los datos históricos | • Confirmar entrega<br>• Cerrar documentación<br>• Análisis final | Estado final - solo lectura histórica |

---

## Casos de Uso para Validación

### Estados Especiales - Decisión de Diseño

**IMPORTANTE:** Los siguientes estados documentados en el Diccionario Negocio-Sistema se implementan como **transiciones directas** en lugar de estados independientes:

| Estado del Diccionario | Implementación en Máquina de Estados | Justificación |
|---|---|---|
| **RevisionRequested** | Transición directa al estado anterior | Evita estados intermedios innecesarios |
| **CustomerRevisionRequested** | QuotedToCustomer → ReadyForSourcing | El estado origen indica quién debe actuar |
| **VmRejected** | PendingVmApproval → ReadyForSourcing | El rechazo es una acción, no un estado |
| **PpsRejected** | PendingPpsApproval → ReadyForSourcing | El rechazo es una acción, no un estado |

**Ventaja:** Simplifica el flujo eliminando estados "temporales" que no agregan valor operativo.

### Caso de Uso 1: Producto Textil - Camisa Promocional

**Contexto:** Cliente BeerCO solicita 500 camisas polo con bordado del logo

**Flujo Esperado:**
1. **Draft → ReadyForSourcing**
   - *Especificaciones requeridas:* Material (Piqué 180 GSM), tallas (S,M,L,XL), colores (azul corporativo), bordado (logo pecho izquierdo 8x6cm)
   - *Validación negocio:* ¿El sistema detecta si falta definir el GSM o la posición del bordado?
   - *Sistema debe validar:* Todos los campos de TextileSpecifications completos

2. **SourcingInProgress → InternalReviewPending**
   - *Cotizaciones recibidas:* Proveedor A ($12.50/unidad, 15 días), Proveedor B ($11.80/unidad, 20 días)
   - *Validación negocio:* ¿El sistema puede comparar automáticamente tiempo vs costo?
   - *Sistema debe calcular:* Costo total, margen esperado, fecha de entrega estimada

**Puntos Críticos de Validación:**
- **Hito de Compromiso:** Cuando BeerCO envía CPO, ¿se bloquean las especificaciones automáticamente?
- **Rechazo de VM:** Si BeerCO rechaza la maqueta por color incorrecto, ¿regresa a ReadyForSourcing automáticamente?

### Caso de Uso 2: Material POP - Display de Cartón

**Contexto:** Cliente FoodCorp necesita 200 displays de cartón para supermercados

**Flujo con Complicaciones:**
1. **Draft → ReadyForSourcing** ✓
2. **ReadyForSourcing → SourcingInProgress** ✓
3. **SourcingInProgress → InternalReviewPending** ✓
4. **InternalReviewPending → QuotedToCustomer** ✓
5. **QuotedToCustomer → PendingVmApproval** ✓ (CPO recibido)
6. **PendingVmApproval → ReadyForSourcing** ⚠️ (Cliente rechaza - estructura inestable)
7. **ReadyForSourcing → SourcingInProgress** (Re-sourcing con nuevas especificaciones)

**Validaciones Específicas:**
- ¿El sistema mantiene historial de por qué se rechazó la maqueta?
- ¿Se notifica automáticamente al equipo de diseño sobre los cambios estructurales requeridos?
- ¿La línea base creada en el paso 5 se mantiene inmutable? (DEBE mantenerse según reglas de negocio)

### Caso de Uso 3: Producto con Múltiples Revisiones

**Contexto:** Llavero metálico con grabado láser - proceso con múltiples iteraciones

**Flujo de Excepciones:**
1. **Draft → ReadyForSourcing** ✓
2. **ReadyForSourcing → SourcingInProgress** ✓
3. **SourcingInProgress → ReadyForSourcing** ⚠️ (Cotizaciones muy altas - buscar alternativas)
4. **ReadyForSourcing → SourcingInProgress** (Nuevo sourcing con proveedores alternativos)
5. **SourcingInProgress → InternalReviewPending** ✓
6. **InternalReviewPending → SourcingInProgress** ⚠️ (Margen insuficiente - renegociar)
7. **SourcingInProgress → InternalReviewPending** ✓
8. **Continúa flujo normal...**

**Validación de Negocio:** ¿El sistema puede manejar múltiples ciclos de retroceso sin perder información?

---

## Flujos de Excepción

### Excepciones por Rechazo del Cliente

| Situación | Estado Actual | Acción del Cliente | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Rechazo de Cotización** | QuotedToCustomer | Cliente rechaza precio/condiciones | ReadyForSourcing | • Registrar motivo<br>• Crear tarea de re-sourcing<br>• Notificar a Procurement |
| **Rechazo de Maqueta Virtual** | PendingVmApproval | Cliente no aprueba diseño | ReadyForSourcing | • Registrar feedback<br>• Crear tarea de rediseño<br>• Pausar cronograma |
| **Rechazo de Muestra Física** | PendingPpsApproval | Cliente rechaza calidad/especificaciones | ReadyForSourcing | • Documentar problemas<br>• Re-evaluar proveedor<br>• Buscar alternativas |

### Excepciones por Problemas de Proveedor

| Situación | Estado Actual | Problema del Proveedor | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Incapacidad de Producción** | ReadyForProduction | Proveedor no puede fabricar | ReadyForSourcing | • Blacklist temporal del proveedor<br>• Activar sourcing de emergencia<br>• Notificar a cliente sobre retraso |
| **Problemas de Calidad en Producción** | InProduction | Fallas detectadas durante fabricación | ReadyForProduction | • Detener producción<br>• Evaluar alternativas<br>• Registrar incidencia de proveedor |
| **Retraso en Envío** | InProduction | Proveedor no cumple fecha de envío | InProduction | • Actualizar cronograma<br>• Notificar a cliente<br>• Aplicar penalidades contractuales |
| **Problema en Tránsito** | InternationalTransit | Incidencia durante transporte | InternationalTransit | • Gestionar seguro<br>• Buscar soluciones logísticas<br>• Comunicar al cliente |
| **Problema Aduanero** | CustomsClearance | Documentos rechazados o faltantes | CustomsClearance | • Corregir documentación<br>• Pagar aranceles adicionales<br>• Gestionar tiempos extra |

### Excepciones Internas del Sistema

| Situación | Disparador | Acción Automática del Sistema | Notificación |
|---|---|---|---|
| **Cotización Vencida** | Fecha de vigencia cumplida | Marcar cotización como vencida, requerir renovación | Email a Sales Analyst |
| **Producto Huérfano** | ProductItem sin actividad >30 días | Marcar para revisión | Email a Team Leader |
| **Desfase de Cronograma** | Fecha estimada vs real >7 días | Escalación automática | Email a Team Leader + Finance |
| **Línea Base Comprometida** | Intentar cambio crítico post-CPO | Bloquear cambio, requerir aprobación especial | Email a Team Leader + Finance |
| **Sincronización ImportShipment** | Estado de envío cambia | Actualizar automáticamente todos los ProductItems del envío | Dashboard update automático |

---

## Validación de Consistencia

### Checklist de Completitud de la Especificación

**✅ Estados Documentados (12 estados principales):**
- Draft, ReadyForSourcing, SourcingInProgress, InternalReviewPending
- QuotedToCustomer, PendingVmApproval, PendingPpsApproval, ReadyForProduction  
- InProduction, InternationalTransit, CustomsClearance, Delivered

**✅ Transiciones Válidas (11 principales + 6 retrocesos):**
- Flujo lineal completo sin saltos prohibidos
- Retrocesos documentados para cada punto crítico
- Actores responsables definidos para cada transición

**✅ Actores y Responsabilidades:**
- Consistencia con Diccionario Negocio-Sistema
- Permisos alineados con roles del sistema
- Escalaciones y aprobaciones especiales definidas

**✅ Eventos Críticos:**
- Hito de Compromiso completamente especificado
- Efectos secundarios automáticos documentados
- Notificaciones y sincronizaciones incluidas

**✅ Casos de Excepción:**
- Rechazos del cliente cubiertos
- Problemas de proveedor documentados
- Excepciones internas del sistema incluidas

### Puntos de Validación para el Negocio

**Pregunta Clave 1:** ¿Esta especificación permite identificar exactamente qué persona debe hacer qué acción en cada momento?
**Respuesta:** ✅ SÍ - Cada estado tiene un actor responsable único y acciones específicas.

**Pregunta Clave 2:** ¿Se pueden prevenir los errores operativos más comunes con estas reglas?
**Respuesta:** ✅ SÍ - Transiciones prohibidas evitan saltos peligrosos, validaciones previenen datos incompletos.

**Pregunta Clave 3:** ¿El sistema puede manejar los casos de excepción reales del negocio?
**Respuesta:** ✅ SÍ - Rechazos, problemas de proveedor y cambios de especificación están cubiertos.

---

## Referencias

- **Documentos relacionados:**
  - [03_Modelo_de_Negocio.md](03_Modelo_de_Negocio.md) → Conceptos y flujos de negocio
  - [08_Diccionario_Negocio_Sistema.md](08_Diccionario_Negocio_Sistema.md) → Mapeo de terminología
  - [05_Requisitos_del_Sistema.md](05_Requisitos_del_Sistema.md) → Requisitos funcionales
- **Implementación técnica:** Ver `2_architecture/13_domain_to_code_mapping.md`
- **Patrones de implementación:** Ver `3_tech_design/patterns/` para ejemplos de código