# 9. Especificación Detallada - Máquina de Estados ProductItem

> **Propósito:** Especificación técnica detallada de la máquina de estados del ProductItem para validación completa por parte del negocio y desarrollo.

---

## Contenido

1. [Tabla de Transiciones Válidas](#tabla-de-transiciones-válidas)
2. [Matriz de Transiciones Prohibidas](#matriz-de-transiciones-prohibidas)
3. [Eventos de Negocio y Efectos Secundarios](#eventos-de-negocio-y-efectos-secundarios)
4. [Reglas de Negocio por Estado](#reglas-de-negocio-por-estado)
5. [Casos de Uso para Validación](#casos-de-uso-para-validación)
6. [Flujos de Excepción](#flujos-de-excepción)

---

## Tabla de Transiciones Válidas

### Transiciones Principales del Flujo Normal

| Estado Origen | Estado Destino | Disparador | Precondiciones | Actor Responsable | Validaciones del Sistema |
|---|---|---|---|---|---|
| **Draft** | ReadyForSourcing | Manual | • Especificaciones completas según categoría<br>• Cliente válido asignado<br>• Cantidad > 0 | Sales Analyst | • Validar campos obligatorios por ProductSubcategory<br>• Verificar cliente existe<br>• Cantidad entre 1 y 100,000 |
| **ReadyForSourcing** | SourcingInProgress | Manual | • Al menos 1 proveedor identificado<br>• Analista de Adquisiciones asignado | Procurement Analyst | • Verificar proveedor activo en BD<br>• Validar asignación de analista |
| **SourcingInProgress** | InternalReviewPending | Manual | • Al menos 1 cotización de proveedor<br>• Precio unitario > 0<br>• Lead time definido | Procurement Analyst | • Validar cotización completa<br>• Verificar datos comerciales |
| **InternalReviewPending** | QuotedToCustomer | Manual | • Aprobación del Team Leader<br>• Margen mínimo cumplido (≥15%)<br>• Cotización generada | Team Leader | • Validar margen calculado<br>• PDF de cotización generado |
| **QuotedToCustomer** | PendingVmApproval | Automático | • Cliente acepta cotización (CPO registrado)<br>• **HITO DE COMPROMISO** | Sales Analyst | • CPO válido registrado<br>• Crear línea base del proyecto<br>• Bloquear modificaciones críticas |
| **PendingVmApproval** | PendingPpsApproval | Manual | • Cliente aprueba maqueta virtual<br>• Virtual Mockup adjunto | Design Analyst | • Archivo de maqueta presente<br>• Aprobación cliente registrada |
| **PendingPpsApproval** | ReadyForProduction | Manual | • Cliente aprueba muestra física<br>• Pre-Production Sample evaluado | Import Analyst | • Muestra evaluada y aprobada<br>• Especificaciones finales confirmadas |
| **ReadyForProduction** | InProduction | Manual | • Orden de compra enviada al proveedor<br>• Términos comerciales acordados | Procurement Analyst | • SupplierPurchaseOrder generada<br>• Fechas de producción confirmadas |
| **InProduction** | InternationalTransit | Automático | • Proveedor confirma envío<br>• ProductItem asignado a ImportShipmentRecord | Import Analyst | • Tracking number válido<br>• Sincronización con envío |
| **InternationalTransit** | CustomsClearance | Automático | • Envío llega a puerto destino<br>• Documentación aduanera iniciada | Import Analyst | • Sincronización con ImportShipmentRecord<br>• Documentos aduaneros presentes |
| **CustomsClearance** | Delivered | Manual | • Despacho aduanero completado<br>• Entrega confirmada al cliente | Import Analyst | • Documentos de entrega<br>• Confirmación cliente (opcional) |

### Transiciones de Excepción y Retroceso

| Estado Origen | Estado Destino | Disparador | Motivo | Actor Responsable | Validaciones |
|---|---|---|---|---|---|
| **InternalReviewPending** | SourcingInProgress | Manual | Cotización insuficiente o incorrecta | Team Leader | Registrar motivo del retroceso |
| **QuotedToCustomer** | ReadyForSourcing | Manual | Cliente solicita cambios significativos | Sales Analyst | Registrar cambios solicitados |
| **PendingVmApproval** | ReadyForSourcing | Manual | Cliente rechaza maqueta virtual | Design Analyst | Registrar feedback del cliente |
| **PendingPpsApproval** | ReadyForSourcing | Manual | Cliente rechaza muestra física | Import Analyst | Registrar razones de rechazo |
| **InProduction** | ReadyForProduction | Manual | Problemas de fabricación | Procurement Analyst | Registrar incidencia de producción |

---

## Matriz de Transiciones Prohibidas

### Transiciones Absolutamente Prohibidas

| Desde → Hacia | Razón de Prohibición | Excepción Posible |
|---|---|---|
| **Draft** → **InProduction** | Saltar validaciones críticas de sourcing y aprobaciones | Solo Admin con justificación documentada |
| **Draft** → **Delivered** | Imposible entregar sin pasar por proceso completo | Ninguna |
| **Delivered** → **cualquier estado** | Proceso ya completado, producto entregado | Solo corrección de errores de sistema |
| **CustomsClearance** → **InProduction** | Retroceso logísticamente imposible | Ninguna |
| **PendingVmApproval** → **InProduction** | Saltar aprobación de muestra física obligatoria | Ninguna |

### Transiciones Restringidas (Requieren Aprobación Especial)

| Desde → Hacia | Restricción | Aprobación Requerida | Casos Válidos |
|---|---|---|---|
| **InProduction** → **Draft** | Retroceso mayor con impacto financiero | Team Leader + Finance Analyst | Cancelación de proyecto, error crítico |
| **PendingPpsApproval** → **InProduction** | Saltar aprobación de muestra | Team Leader | Cliente acepta riesgo por escrito |
| **Cualquier estado** → **Draft** | Reset completo del proceso | Admin | Reconfiguración mayor del producto |

---

## Eventos de Negocio y Efectos Secundarios

### Eventos Críticos del Sistema

| Transición | Evento de Negocio | Efectos Secundarios Automáticos | Notificaciones |
|---|---|---|---|
| **QuotedToCustomer** → **PendingVmApproval** | **Hito de Compromiso** | • Crear ProjectBaseline (línea base)<br>• Bloquear especificaciones críticas<br>• Activar seguimiento de costos reales | • Email a Design Team (nueva tarea VM)<br>• Email a Finance (proyecto confirmado)<br>• Alerta Team Leader (seguimiento activo) |
| **PendingVmApproval** → **PendingPpsApproval** | Aprobación de Maqueta Virtual | • Crear tarea para solicitud de muestra<br>• Actualizar cronograma estimado | • Email a Procurement (solicitar muestra)<br>• Email a cliente (muestra en proceso) |
| **ReadyForProduction** → **InProduction** | Inicio de Fabricación | • Activar seguimiento de producción<br>• Crear calendario de seguimiento | • Email a Import Analyst (preparar logística)<br>• Email a cliente (producción iniciada) |
| **InProduction** → **InternationalTransit** | Envío Confirmado | • Actualizar ImportShipmentRecord<br>• Sincronizar estados de todos los productos del envío<br>• Calcular fecha estimada de llegada | • Email a Import Analyst (seguimiento)<br>• SMS/Email a cliente (producto en camino)<br>• Actualización automática de dashboard |

### Eventos de Rollback y Excepción

| Transición de Retroceso | Evento de Negocio | Efectos Secundarios | Notificaciones |
|---|---|---|---|
| **PendingVmApproval** → **ReadyForSourcing** | Rechazo de Maqueta | • Crear tarea de rediseño<br>• Registrar feedback del cliente<br>• Pausar cronograma | • Email a Design Team (correcciones)<br>• Email a Team Leader (retraso) |
| **PendingPpsApproval** → **ReadyForSourcing** | Rechazo de Muestra | • Crear tarea de re-sourcing<br>• Registrar problemas de calidad<br>• Re-evaluar proveedor | • Email a Procurement (buscar alternativas)<br>• Email a Team Leader (incidencia) |

---

## Reglas de Negocio por Estado

### Permisos y Restricciones por Estado

| Estado | Campos Editables | Campos Bloqueados | Acciones Permitidas | Reglas Especiales |
|---|---|---|---|---|
| **Draft** | • Todas las especificaciones<br>• Cantidad<br>• Descripción | Ninguno | • Editar libremente<br>• Eliminar ProductItem<br>• Cambiar categoría | Único estado donde se puede eliminar |
| **ReadyForSourcing** | • Notas internas<br>• Proveedores sugeridos | • Especificaciones<br>• Cantidad (cambio >10% prohibido) | • Iniciar sourcing<br>• Retroceder a Draft | Cambios de especificaciones requieren aprobación |
| **SourcingInProgress** | • Cotizaciones de proveedores<br>• Análisis comparativo<br>• Notas de sourcing | • Especificaciones core<br>• Cliente<br>• Categoría | • Agregar cotizaciones<br>• Comparar proveedores<br>• Avanzar o retroceder | Al menos 1 cotización para avanzar |
| **InternalReviewPending** | • Margen aplicado<br>• Condiciones comerciales<br>• Notas de aprobación | • Costos base<br>• Especificaciones<br>• Cliente | • Aprobar/rechazar<br>• Ajustar márgenes | Solo Team Leader puede aprobar |
| **QuotedToCustomer** | • Estado de seguimiento<br>• Notas de cliente<br>• Vigencia de cotización | • Precios<br>• Especificaciones<br>• Condiciones | • Registrar CPO<br>• Extender vigencia<br>• Manejar cambios menores | Estado crítico para Hito de Compromiso |
| **PendingVmApproval** | • Virtual Mockup<br>• Comentarios de diseño<br>• Iteraciones de diseño | • Especificaciones técnicas<br>• Precios<br>• Cantidades | • Subir/actualizar VM<br>• Registrar aprobación<br>• Manejar rechazos | Requiere Virtual Mockup adjunto |
| **PendingPpsApproval** | • Pre-Production Sample<br>• Evaluación de calidad<br>• Notas de inspección | • Especificaciones finales<br>• Precios comprometidos<br>• Cronograma base | • Evaluar muestra<br>• Aprobar/rechazar<br>• Solicitar ajustes | Evaluación obligatoria de calidad |
| **ReadyForProduction** | • Orden de compra<br>• Términos con proveedor<br>• Fechas de producción | • Especificaciones<br>• Cantidades finales<br>• Precios acordados | • Enviar orden de compra<br>• Negociar términos finales<br>• Iniciar producción | Estado pre-producción crítico |
| **InProduction** | • Estado de producción<br>• Comunicaciones con proveedor<br>• Fechas estimadas | • Especificaciones<br>• Cantidades<br>• Términos comerciales | • Seguimiento de progreso<br>• Actualizar cronograma<br>• Manejar incidencias | Solo Procurement puede actualizar |
| **InternationalTransit** | • Tracking logístico<br>• Documentos de embarque<br>• ETAs | • Datos de producción<br>• Especificaciones<br>• Costos de fabricación | • Seguimiento automático<br>• Actualización de documentos<br>• Preparar aduanas | Sincronización automática con ImportShipmentRecord |
| **CustomsClearance** | • Documentación aduanera<br>• Valuación<br>• Pagos de impuestos | • Datos del producto<br>• Documentos de origen<br>• Cantidades declaradas | • Gestionar trámites<br>• Pagar aranceles<br>• Coordinar liberación | Estado manejado por Import Analyst |
| **Delivered** | • Confirmación de entrega<br>• Evaluación post-entrega<br>• Cierre de proyecto | Todos los datos históricos | • Confirmar entrega<br>• Cerrar documentación<br>• Análisis final | Estado final - solo lectura histórica |

---

## Casos de Uso para Validación

### Caso de Uso 1: Producto Textil - Camisa Promocional

**Contexto:** Cliente BeerCO solicita 500 camisas polo con bordado del logo

**Flujo Esperado:**
1. **Draft → ReadyForSourcing**
   - *Especificaciones requeridas:* Material (Piqué 180 GSM), tallas (S,M,L,XL), colores (azul corporativo), bordado (logo pecho izquierdo 8x6cm)
   - *Validación negocio:* ¿El sistema detecta si falta definir el GSM o la posición del bordado?
   - *Sistema debe validar:* Todos los campos de TextileSpecifications completos

2. **SourcingInProgress → InternalReviewPending**
   - *Cotizaciones recibidas:* Proveedor A ($12.50/unidad, 15 días), Proveedor B ($11.80/unidad, 20 días)
   - *Validación negocio:* ¿El sistema puede comparar automáticamente tiempo vs costo?
   - *Sistema debe calcular:* Costo total, margen esperado, fecha de entrega estimada

**Puntos Críticos de Validación:**
- **Hito de Compromiso:** Cuando BeerCO envía CPO, ¿se bloquean las especificaciones automáticamente?
- **Rechazo de VM:** Si BeerCO rechaza la maqueta por color incorrecto, ¿regresa a ReadyForSourcing automáticamente?

### Caso de Uso 2: Material POP - Display de Cartón

**Contexto:** Cliente FoodCorp necesita 200 displays de cartón para supermercados

**Flujo con Complicaciones:**
1. **Draft → ReadyForSourcing** ✓
2. **ReadyForSourcing → SourcingInProgress** ✓
3. **SourcingInProgress → InternalReviewPending** ✓
4. **InternalReviewPending → QuotedToCustomer** ✓
5. **QuotedToCustomer → PendingVmApproval** ✓ (CPO recibido)
6. **PendingVmApproval → ReadyForSourcing** ⚠️ (Cliente rechaza - estructura inestable)
7. **ReadyForSourcing → SourcingInProgress** (Re-sourcing con nuevas especificaciones)

**Validaciones Específicas:**
- ¿El sistema mantiene historial de por qué se rechazó la maqueta?
- ¿Se notifica automáticamente al equipo de diseño sobre los cambios estructurales requeridos?
- ¿La línea base creada en el paso 5 se mantiene o se actualiza?

### Caso de Uso 3: Producto con Múltiples Revisiones

**Contexto:** Llavero metálico con grabado láser - proceso con múltiples iteraciones

**Flujo de Excepciones:**
1. **Draft → ReadyForSourcing** ✓
2. **ReadyForSourcing → SourcingInProgress** ✓
3. **SourcingInProgress → ReadyForSourcing** ⚠️ (Cotizaciones muy altas - buscar alternativas)
4. **ReadyForSourcing → SourcingInProgress** (Nuevo sourcing con proveedores alternativos)
5. **SourcingInProgress → InternalReviewPending** ✓
6. **InternalReviewPending → SourcingInProgress** ⚠️ (Margen insuficiente - renegociar)
7. **SourcingInProgress → InternalReviewPending** ✓
8. **Continúa flujo normal...**

**Validación de Negocio:** ¿El sistema puede manejar múltiples ciclos de retroceso sin perder información?

---

## Flujos de Excepción

### Excepciones por Rechazo del Cliente

| Situación | Estado Actual | Acción del Cliente | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Rechazo de Cotización** | QuotedToCustomer | Cliente rechaza precio/condiciones | ReadyForSourcing | • Registrar motivo<br>• Crear tarea de re-sourcing<br>• Notificar a Procurement |
| **Rechazo de Maqueta Virtual** | PendingVmApproval | Cliente no aprueba diseño | ReadyForSourcing | • Registrar feedback<br>• Crear tarea de rediseño<br>• Pausar cronograma |
| **Rechazo de Muestra Física** | PendingPpsApproval | Cliente rechaza calidad/especificaciones | ReadyForSourcing | • Documentar problemas<br>• Re-evaluar proveedor<br>• Buscar alternativas |

### Excepciones por Problemas de Proveedor

| Situación | Estado Actual | Problema del Proveedor | Estado Resultante | Efectos del Sistema |
|---|---|---|---|---|
| **Incapacidad de Producción** | ReadyForProduction | Proveedor no puede fabricar | ReadyForSourcing | • Blacklist temporal del proveedor<br>• Activar sourcing de emergencia<br>• Notificar a cliente sobre retraso |
| **Problemas de Calidad en Producción** | InProduction | Fallas detectadas durante fabricación | ReadyForProduction | • Detener producción<br>• Evaluar alternativas<br>• Registrar incidencia de proveedor |
| **Retraso en Envío** | InProduction | Proveedor no cumple fecha de envío | InProduction | • Actualizar cronograma<br>• Notificar a cliente<br>• Aplicar penalidades contractuales |

### Excepciones Internas del Sistema

| Situación | Disparador | Acción Automática del Sistema | Notificación |
|---|---|---|---|
| **Cotización Vencida** | Fecha de vigencia cumplida | Cambiar estado a "Requiere Actualización" | Email a Sales Analyst |
| **Producto Huérfano** | ProductItem sin actividad >30 días | Marcar para revisión | Email a Team Leader |
| **Desfase de Cronograma** | Fecha estimada vs real >7 días | Escalación automática | Email a Team Leader + Finance |

---

## Referencias

- **Documentos relacionados:**
  - [03_Modelo_de_Negocio.md](03_Modelo_de_Negocio.md) → Conceptos y flujos de negocio
  - [08_Diccionario_Negocio_Sistema.md](08_Diccionario_Negocio_Sistema.md) → Mapeo de terminología
  - [05_Requisitos_del_Sistema.md](05_Requisitos_del_Sistema.md) → Requisitos funcionales
- **Implementación técnica:** Ver `2_architecture/13_domain_to_code_mapping.md`
- **Patrones de implementación:** Ver `3_tech_design/patterns/` para ejemplos de código