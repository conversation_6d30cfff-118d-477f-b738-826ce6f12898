# Modelo de Negocio y Ciclos de Vida

> **Propósito:** Define las principales "piezas de información" (entidades) que maneja el sistema y cómo se comportan y cambian a lo largo del tiempo (sus estados y ciclos de vida).

---

## 1. Las Piezas Clave del Negocio: Entidades Principales

Todo el sistema se articula en torno a un conjunto de entidades de negocio que representan conceptos del mundo real. Para garantizar la trazabilidad, algunas aprobaciones clave como las Maquetas Virtuales y las Muestras Físicas se registran como entidades separadas.

### Cómo se Organizan las Piezas del Negocio

**Los Protagonistas Principales:**

- **Cliente**: Empresas que solicitan productos promocionales. Cada cliente puede tener múltiples proyectos a lo largo del tiempo.

- **Proyecto**: Es como una "carpeta" que contiene todo lo relacionado con un pedido específico de un cliente. Por ejemplo, "Campaña Verano 2024 - BeerCO".

- **Producto**: Cada artículo individual dentro de un proyecto. Un proyecto puede tener varios productos (ej: hieleras, llaveros, camisetas).

- **Proveedor**: Fabricantes que producen los productos, generalmente ubicados en el extranjero.

**Los Elementos de Trabajo:**

- **Maqueta Virtual**: Diseño digital que se muestra al cliente para aprobar el concepto visual antes de producir.

- **Muestra de Preproducción**: Prototipo físico que se envía al cliente para validar calidad y especificaciones.

- **Registro de Envío de Importación**: Documento que agrupa productos (de uno o varios proyectos) que viajan juntos desde el proveedor hasta Chile.

- **Costos Reales**: Registro de todos los gastos reales del proyecto (fabricación, flete, aduanas, etc.) para calcular la rentabilidad final.

**Cómo se Relacionan:**

- Un **cliente** puede tener varios **proyectos** activos o históricos
- Cada **proyecto** contiene uno o más **productos** específicos
- Cada **producto** necesita una **maqueta virtual** y una **muestra física** para aprobación
- Los **productos** se agrupan en **registros de envío de importación** para optimizar la logística
- Todos los gastos se registran como **costos reales** para medir la rentabilidad del **proyecto**

### Detalles Importantes de las Piezas Principales

**Proyecto:**
- Funciona como una "carpeta maestra" que contiene todo lo relacionado con un pedido
- Su estado general (ej. "En Cotización", "Confirmado") se calcula automáticamente según el progreso de sus productos
- Mantiene un "snapshot" financiero inicial (línea base) que se fija al momento del compromiso.
- Mantiene un "snapshot" de planificaciǫn de hitos inicial (línea base) que se fija al momento del compromiso


**Producto:**
- Es el elemento central que impulsa todo el proceso operativo
- Su estado específico (ej. "En Diseño", "En Producción") determina qué equipo debe trabajar y qué tarea realizar
- Cada producto tiene especificaciones detalladas y sigue su propio ciclo de vida independiente
- Se le imputa cada costo **directo** incurrido durante su ciclo de vida
- Se registra la fecha real de ejecución para el listado de hitos definidos

**Registro de Envío de Importación:**
- Optimiza costos al agrupar productos de diferentes proyectos en un solo embarque internacional
- Su estado logístico actualiza automáticamente el estado de todos los productos que transporta
- Facilita el seguimiento centralizado de múltiples productos en tránsito

**Costos Reales:**
- Captura todos los gastos reales (fabricación, transporte, aduanas, etc.)
- Permite comparar el costo real vs. el presupuesto inicial para medir rentabilidad
- Es fundamental para el aprendizaje y mejora de estimaciones futuras

---

## 2. El Motor del Proceso: El Ciclo de Vida del Producto

El **Producto** es la entidad que viaja a través de todo el proceso. Su estado nos dice exactamente en qué fase se encuentra y quién es el responsable.

### Fases del Ciclo de Vida

**Fase 1: Venta y Cotización**
- El producto se define, se buscan proveedores y se cotiza al cliente
- **Estados principales:** Borrador → Sourcing → Cotizado al Cliente

**Fase 2: Preparación de la Producción** 
- Se obtienen las aprobaciones necesarias antes de producir
- **Estados principales:** Aprobación de Maqueta → Aprobación de Muestra → Listo para Producción
- **Hito Clave:** Al completar la cotización se activa el **Hito de Compromiso**

**Fase 3: Cumplimiento y Entrega**
- Producción, logística y entrega del producto
- **Estados principales:** En Producción → En Tránsito → Entregado

> **Referencias Técnicas:** 
> - **Mapeo de estados:** [8. Diccionario Negocio-Sistema](08_Diccionario_Negocio_Sistema.md#estados-del-proceso)
> - **Especificación detallada:** [9. Especificación Máquina de Estados ProductItem](09_Especificacion_Maquina_Estados_ProductItem.md)

### Estados Clave por Fase

| Fase | Estados Principales | Responsable | Propósito |
|:---|:---|:---|:---|
| **Venta** | Borrador → Sourcing → Cotizado | Analista de Ventas + Adquisiciones | Definir el producto y conseguir precio competitivo |
| **Preparación** | Aprobación Maqueta → Aprobación Muestra → Listo | Analista de Diseño + Importaciones | Validar diseño y calidad antes de producir |
| **Cumplimiento** | Producción → Envío → Entregado | Analista de Importaciones + Proveedor | Fabricar y entregar el producto final |

**Puntos de Control Clave:**
- **Hito de Compromiso:** Se activa al recibir la Orden de Compra del cliente
- **Sincronización Automática:** Los estados logísticos se actualizan automáticamente según el Registro de Envío de Importación

---

## 3. La Visión General: El Ciclo de Vida del Proyecto

A diferencia del Ítem de Producto, el ciclo de vida del Proyecto es más simple y representa una visión de alto nivel. Su estado se deriva automáticamente de los estados de los productos que contiene, pero sigue una progresión lógica y predecible.

```mermaid
stateDiagram-v2
    [*] --> DRAFT : Oportunidad Creada
    DRAFT --> SOURCING : Inicia Búsqueda
    SOURCING --> QUOTED : Cotización Enviada
    QUOTED --> CONFIRMED : CPO Recibido (Compromiso)
    CONFIRMED --> IN_PROGRESS : Inicia Preparación
    IN_PROGRESS --> COMPLETED : Todos los ítems entregados
    COMPLETED --> [*]

    note right of CONFIRMED
        El estado "IN_PROGRESS" agrupa
        internamente las fases de Preparación,
        Producción y Logística para una
        visión gerencial simplificada.
    end note
```

- **DRAFT, SOURCING, QUOTED:** Corresponden a la fase de **Venta y Cotización**.
- **CONFIRMED:** Es el estado que se activa con el Hito de Compromiso, marcando el inicio del acuerdo formal.
- **IN_PROGRESS:** Agrupa todo el trabajo operativo post-confirmación.
- **COMPLETED:** El proyecto se completa una vez que todos sus ítems han sido entregados.

---

## 4. Momentos Decisivos: Eventos Críticos del Sistema

### Hito de Compromiso y Creación de Línea Base

Este es el momento más importante del proceso. Se activa con la **Orden de Compra del Cliente (CPO)** y marca la transición de "Venta" a "Ejecución".

1.  **Formaliza el Acuerdo:** El plan del proyecto se convierte en un acuerdo formal.
2.  **Crea la Línea Base (Baseline):** El sistema toma una "fotografía" inmutable del presupuesto, las especificaciones y el cronograma. Esta línea base es la vara con la que se medirá el éxito financiero y operativo del proyecto.
3.  **Inicia el Flujo de Trabajo:** El proyecto avanza y se notifica a los equipos que el trabajo operativo puede comenzar.

### Sincronización Automática de Estados

Además del Hito de Compromiso, otro concepto clave es la **sincronización automática de estados**. Para asegurar la consistencia, el estado logístico de un producto (`INTERNATIONAL_TRANSIT`, `CUSTOMS_CLEARANCE`, etc.) se deriva automáticamente del estado del Registro de Envío de Importación al que está asignado, eliminando la necesidad de actualizaciones manuales y reduciendo errores.

---

## 5. Control de Gestión: Línea Base vs. Realidad

Una vez que se establece el **Hito de Compromiso**, el sistema captura una "fotografía" inmutable del proyecto que sirve como punto de referencia para medir el desempeño. Esta **línea base** se compara continuamente contra la **realidad actual** en dos dimensiones clave: **rentabilidad financiera** y **cumplimiento de cronograma**.

### Las Dos Dimensiones de Medición

#### Dimensión Financiera: Margen Planificado vs. Margen Real
- **Línea Base:** Costos estimados y márgenes planificados al momento del compromiso
- **Realidad Actual:** Costos reales incurridos durante la ejecución
- **Indicador Clave:** Variación de margen (% de desviación respecto al margen planificado)

#### Dimensión Temporal: Cronograma Planificado vs. Avance Real  
- **Línea Base:** Fechas estimadas de hitos clave (aprobaciones, producción, entrega)
- **Realidad Actual:** Fechas reales de completitud de cada hito
- **Indicador Clave:** Variación de cronograma (días de adelanto/atraso por hito)

### Niveles de Análisis: Producto vs. Proyecto

El sistema ofrece dos perspectivas complementarias para el análisis de desempeño:

#### Nivel de Producto (Granular)
**Propósito:** Control operativo detallado y aprendizaje para estimaciones futuras.

**Análisis Financiero por Producto:**
- Cada producto mantiene su propio presupuesto individual en la línea base
- Los costos reales se asignan directamente al producto cuando son específicos (ej: costo de fabricación)
- Los costos compartidos se prorratean usando reglas definidas:
  - **Costos de flete internacional:** Por peso/volumen del producto
  - **Costos de aduanas:** Por valor FOB del producto  
  - **Costos de gestión:** Por complejidad/tiempo invertido

**Análisis de Cronograma por Producto:**
- Cada producto tiene su propio cronograma de hitos independiente
- Permite identificar productos problemáticos dentro de un proyecto
- Facilita el aprendizaje sobre qué tipos de productos son más predecibles

#### Nivel de Proyecto (Consolidado)
**Propósito:** Visión gerencial y toma de decisiones estratégicas.

**Análisis Financiero Consolidado:**
- **Margen Total del Proyecto:** Suma de márgenes individuales de todos los productos
- **Costos Compartidos:** Se distribuyen según las reglas de prorrateo establecidas
- **Rentabilidad Global:** Permite evaluar si el proyecto cumple objetivos financieros

**Análisis de Cronograma Consolidado:**
- **Ruta Crítica del Proyecto:** El producto que determine la fecha de entrega más tardía
- **Estado General:** Se calcula según reglas de consolidación:
  - **"En Cronograma":** Si todos los productos están dentro de ±3 días de su plan
  - **"Riesgo Menor":** Si algún producto tiene 4-7 días de atraso
  - **"Riesgo Mayor":** Si algún producto tiene >7 días de atraso
  - **"Crítico":** Si el producto en ruta crítica tiene atraso

### Reglas de Prorrateo de Costos Compartidos

| Tipo de Costo | Criterio de Prorrateo | Justificación |
|:---|:---|:---|
| **Flete Internacional** | Peso/Volumen (el mayor) | Los costos de transporte dependen del espacio ocupado |
| **Seguro de Carga** | Valor FOB del producto | El seguro se calcula sobre el valor de la mercancía |
| **Gestión Aduanera** | Valor FOB del producto | Las tarifas aduaneras son ad-valorem |
| **Almacenaje 3PL** | Días × (Peso/Volumen) | Combina tiempo de estadía con espacio ocupado |
| **Gestión de Proyecto** | Tiempo invertido por producto | Basado en la complejidad y atención requerida |
| **Muestras de QA** | Costo directo al producto | Cada muestra es específica de un producto |

### Reglas de Consolidación de Estado del Proyecto

| Condición | Estado del Proyecto | Descripción |
|:---|:---|:---|
| Todos los productos "En Cronograma" | **En Cronograma** | Proyecto avanza según lo planificado |
| Al menos un producto "Riesgo Menor" | **Monitoreo** | Requiere atención pero no intervención inmediata |
| Al menos un producto "Riesgo Mayor" | **Intervención** | Requiere acciones correctivas |
| Producto en ruta crítica "Crítico" | **Escalación** | Riesgo real de incumplimiento al cliente |
| Todos los productos entregados | **Completado** | Proyecto cerrado exitosamente |

Esta estructura permite tanto el control granular necesario para la mejora operativa como la visión consolidada requerida para la gestión estratégica del portafolio de proyectos.