# 6. Roles y Permisos del Sistema

> **Propósito:** Define el modelo simplificado de roles y permisos para evitar over-engineering de sistemas RBAC complejos, estableciendo un enfoque pragmático que satisface las necesidades operativas sin complejidad innecesaria.

---

## Filosofía de Diseño: Simplicidad Operativa

**Prin<PERSON>pio Rector:** *"Un usuario, un rol, responsabilidades claras"*

El sistema PromoSmart adopta un **modelo de roles simplificado** basado en la realidad operativa de la empresa, donde cada persona tiene una función principal claramente definida. Esto evita la complejidad de sistemas de permisos granulares que serían excesivos para el contexto de negocio.

### Por Qué NO Implementar RBAC Complejo

- ❌ **Múltiples roles por usuario**: Cada analista tiene una especialización clara
- ❌ **Permisos granulares**: Las responsabilidades están bien delimitadas por proceso
- ❌ **Herencia de roles**: La estructura organizacional es plana y funcional
- ❌ **Roles dinámicos**: Las funciones son estables y no cambian frecuentemente

---

## Roles Definidos del Sistema

### Roles Operativos

| Rol | Código | Descripción | Cantidad Típica |
|:---|:---|:---|:---|
| **Analista de Ventas** | `sales_analyst` | Gestiona relación con clientes y cotizaciones | 2-3 usuarios |
| **Analista de Adquisiciones** | `procurement_analyst` | Sourcing y gestión de proveedores | 1-2 usuarios |
| **Analista de Importaciones** | `import_analyst` | Logística internacional y entrega | 1-2 usuarios |
| **Analista de Diseño** | `design_analyst` | Creación de maquetas virtuales | 1 usuario |
| **Analista Financiero** | `financial_analyst` | Control de costos y rentabilidad | 1 usuario |

### Roles de Supervisión

| Rol | Código | Descripción | Cantidad Típica |
|:---|:---|:---|:---|
| **Líder de Equipo** | `team_leader` | Supervisión operativa y escalaciones | 1 usuario |
| **Administrador** | `admin` | Configuración del sistema y usuarios | 1 usuario |

---

## Matriz de Permisos por Rol

### Permisos de Lectura (Ver)

| Funcionalidad | Ventas | Adquisiciones | Importaciones | Diseño | Financiero | Líder | Admin |
|:---|:---:|:---:|:---:|:---:|:---:|:---:|:---:|
| **Todos los Proyectos** | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |
| **Proyectos Asignados** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **Datos de Clientes** | ✅ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |
| **Datos de Proveedores** | ❌ | ✅ | ✅ | ❌ | ✅ | ✅ | ✅ |
| **Información Financiera** | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |

### Permisos de Escritura (Modificar)

| Funcionalidad | Ventas | Adquisiciones | Importaciones | Diseño | Financiero | Líder | Admin |
|:---|:---:|:---:|:---:|:---:|:---:|:---:|:---:|
| **Crear Proyectos** | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| **Cotizar a Clientes** | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| **Gestionar Sourcing** | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ | ✅ |
| **Estados Logísticos** | ❌ | ❌ | ✅ | ❌ | ❌ | ✅ | ✅ |
| **Maquetas Virtuales** | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ✅ |
| **Registrar Costos Reales** | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |

---

## Reglas de Negocio por Rol

### Analista de Ventas
**Puede:**
- Crear y modificar proyectos en fase de cotización
- Aprobar/rechazar opciones de sourcing
- Generar cotizaciones para clientes
- Registrar órdenes de compra de clientes

**No Puede:**
- Modificar proyectos después del Hito de Compromiso
- Eliminar productos con sourcing completado
- Acceder a información financiera detallada de otros proyectos

### Analista de Adquisiciones
**Puede:**
- Gestionar información de proveedores
- Crear y enviar órdenes de compra a proveedores
- Actualizar estados de producción
- Registrar cotizaciones de proveedores

**No Puede:**
- Modificar precios en cotizaciones al cliente
- Cambiar especificaciones de productos post-compromiso
- Eliminar registros de sourcing histórico

### Analista de Importaciones
**Puede:**
- Crear y gestionar Registros de Envío de Importación
- Actualizar estados logísticos de productos
- Coordinar con agentes de carga y 3PL
- Gestionar documentación de importación

**No Puede:**
- Modificar especificaciones de productos
- Cambiar términos comerciales con proveedores
- Eliminar registros de envío completados

### Líder de Equipo
**Puede:**
- Vista completa de todos los proyectos y métricas
- Resolver escalaciones y conflictos
- Reasignar proyectos entre analistas
- Aprobar excepciones a procesos estándar

**No Puede:**
- Eliminar datos históricos o de auditoría
- Modificar configuraciones del sistema
- Cambiar roles de usuarios

---

## Reglas de Asignación y Acceso

### Asignación de Proyectos
- **Automática:** Los proyectos se asignan al analista de ventas que los crea
- **Manual:** El líder de equipo puede reasignar proyectos
- **Visibilidad:** Cada analista ve solo los proyectos donde tiene un rol activo

### Control de Acceso Temporal
- **Proyectos Activos:** Acceso completo según permisos del rol
- **Proyectos Cerrados:** Solo lectura para todos los roles (excepto Admin)
- **Datos Históricos:** Retención según políticas de auditoría

### Excepciones y Escalaciones
- **Casos Especiales:** El líder de equipo puede otorgar acceso temporal
- **Auditoría:** Todas las excepciones se registran para revisión
- **Reversión:** Permisos especiales expiran automáticamente en 7 días

---

## Implementación Técnica Simplificada

### Estructura de Datos
```
users:
  - id
  - name
  - email  
  - role (enum: sales_analyst, procurement_analyst, etc.)
  - active (boolean)
```

### Validación de Permisos
- **Middleware simple** basado en el rol del usuario
- **Sin tablas de permisos** ni relaciones complejas
- **Lógica en código** para validaciones específicas

### Principios de Implementación
1. **Un usuario = un rol** (sin múltiples asignaciones)
2. **Permisos implícitos** definidos por el rol
3. **Validación a nivel de controlador** (no granular)
4. **Excepciones mínimas** y bien documentadas

---

## Casos Especiales y Consideraciones

### Usuarios Temporales
- **Consultores externos:** Rol de "lectura limitada" por tiempo definido
- **Reemplazos:** Activación temporal de roles para cubrir ausencias
- **Capacitación:** Acceso de "observador" para nuevos empleados

### Crecimiento del Equipo
- **Escalabilidad:** El modelo soporta múltiples usuarios por rol
- **Nuevos roles:** Se pueden agregar fácilmente sin reestructurar permisos
- **Especialización:** Subdivision de roles existentes si es necesario

### Historial de Acceso
- **Log simple:** Registro de quién accedió a qué proyecto y cuándo
- **Sin tracking granular:** No se registra cada clic o vista
- **Retención:** 1 año de logs de acceso para auditoría básica

Este modelo de roles y permisos balancea la **seguridad operativa** con la **simplicidad de implementación**, evitando la complejidad innecesaria que podría ralentizar tanto el desarrollo como el uso diario del sistema.