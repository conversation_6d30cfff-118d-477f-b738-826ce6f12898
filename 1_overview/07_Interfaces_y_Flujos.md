# 7. Interfaces y Flujos de Usuario

> **Propósito:** Describe las pantallas principales del sistema y cómo los usuarios navegan entre ellas para completar sus tareas, sirviendo como un puente visual entre el proceso de negocio y la interfaz de usuario.

---

## 5.1. El Dashboard Principal (Pantalla de Inicio)

-   **Usuario:** Todos los roles.
-   **Descripción:** Es la primera pantalla que ve un usuario al iniciar sesión. Actúa como un centro de comando personalizado que resume las tareas y proyectos más urgentes y relevantes para ese usuario específico.
-   **Componentes Clave:**
    -   **Widget "Mis Tareas Pendientes":** Una lista clara y accionable de ítems que requieren la atención del usuario. Cada elemento indica el proyecto, el ítem y la acción requerida (ej. "Aprobar Maqueta para Hieleras BeerCo", "Revisar Sourcing para Llaveros BeerCo").
    -   **Widget "Proyectos Activos":** Una serie de tarjetas o una tabla resumen de los proyectos asignados al usuario. Cada tarjeta muestra el nombre del proyecto, el cliente, el estado general (ej. `En Progreso`) y un indicador visual de la rentabilidad actual vs. la presupuestada.
    -   **Barra de Búsqueda Global (`RF-13`):** Un campo de búsqueda prominente que permite encontrar cualquier entidad del sistema (proyecto, ítem, cliente, PO) desde un único lugar.

## 5.2. La Vista de Proyecto (Project View)

-   **Usuario:** Todos los roles, con distintos niveles de permisos.
-   **Descripción:** Es la vista de 360 grados de un proyecto. Consolida toda la información relacionada con un pedido de un cliente en un solo lugar.
-   **Componentes Clave:**
    -   **Cabecera:** Muestra la información maestra del proyecto: nombre, cliente, estado general calculado (ej. `Confirmado`), y métricas financieras clave como el margen total presupuestado y el real.
    -   **Pestaña "Ítems":** Una tabla con todos los `ProductItem` del proyecto. Cada fila es un resumen que muestra el nombre del ítem, su estado específico (ej. `En Producción`), el analista responsable y un link para navegar a la "Vista de Ítem de Producto".
    -   **Pestaña "Finanzas":** Un desglose detallado de los costos presupuestados versus los costos reales registrados, mostrando la varianza. Aquí se gestionarían también los Contratos de Cambio a Plazo (FEC).
    -   **Pestaña "Documentos":** Un repositorio central con todos los archivos y artefactos generados o subidos para el proyecto (cotizaciones, órdenes de compra, facturas, etc.).
    -   **Pestaña "Log de Hitos":** Muestra el historial cronológico de los hitos de negocio importantes para el proyecto en su conjunto.

## 5.3. La Vista de Ítem de Producto (ProductItem View)

-   **Usuario:** Principalmente los Analistas (Ventas, Adquisiciones, Importaciones, Diseño).
-   **Descripción:** El corazón operativo del sistema. Es una vista detallada donde se gestiona el ciclo de vida completo de un único producto, desde la idea hasta la entrega.
-   **Componentes Clave:**
    -   **Cabecera:** Muestra el nombre del ítem, su estado actual de forma prominente (ej. "Pendiente Aprobación de Muestra Física") y el analista responsable de la etapa actual.
    -   **Panel de Especificaciones:** Un formulario detallado (posiblemente deshabilitado después del Hito de Compromiso) con todos los atributos técnicos del producto.
    -   **Panel de Flujo de Aprobaciones:** Un check-list visual e interactivo que muestra el estado de las aprobaciones requeridas (Maqueta Virtual, Muestra de Pre-Producción, Factura Proforma).

> **Referencia:** Ver [3. Modelo de Negocio - Ciclo de Vida del Producto](03_Modelo_de_Negocio.md#2-el-motor-del-proceso-el-ciclo-de-vida-del-producto) para entender los estados y transiciones.
    -   **Panel de Sourcing:** Una tabla comparativa donde el Analista de Adquisiciones registra y compara las cotizaciones de diferentes proveedores.
    -   **Feed de Actividad y Comentarios (`RF-14`):** Un historial de todos los hitos, cambios de estado y comentarios dejados por los usuarios para este ítem específico, permitiendo la colaboración en contexto.
