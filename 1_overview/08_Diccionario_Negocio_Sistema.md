# 8. Diccionario Negocio-Sistema

> **Propósito:** Diccionario de traducción entre el lenguaje de negocio de PromoSmart y los conceptos del sistema, facilitando la comunicación entre equipos de negocio y desarrollo.

---

## Contenido

1. [Conceptos Fundamentales](#conceptos-fundamentales)
2. [Estados del Proceso](#estados-del-proceso)
3. [Documentos y Artefactos](#documentos-y-artefactos)
4. [Roles y Responsabilidades](#roles-y-responsabilidades)
5. [Reglas de Negocio Principales](#reglas-de-negocio-principales)

---

## Conceptos Fundamentales

### Entidades Principales

| Término de Negocio | Concepto en el Sistema | Descripción |
|---|---|---|
| **Proyecto** | Project | Oportunidad de negocio completa con uno o más productos |
| **Ítem de Producto** | ProductItem | Un producto específico dentro de un proyecto |
| **Registro de Envío** | ImportShipmentRecord | Agrupación de productos para importación |
| **Cliente** | Customer | Empresa o persona que solicita productos |
| **Proveedor** | Supplier | Empresa que fabrica o suministra productos |
| **Analista** | User (con rol específico) | Miembro del equipo PromoSmart |

### Conceptos Arquitectónicos

| Término de Negocio | Concepto del Sistema | Descripción |
|---|---|---|
| **Línea Base** | Project Baseline | Fotografía financiera del proyecto al momento del compromiso |
| **Hito de Compromiso** | Commitment Milestone | Punto donde el proyecto se confirma y no puede modificarse libremente |
| **Especificaciones** | Product Specifications | Detalles técnicos y comerciales de cada producto |
| **Cotización** | Customer Quotation | Propuesta comercial formal enviada al cliente |
| **Orden de Compra** | Purchase Order | Documento de compra (del cliente o hacia proveedor) |

---

## Estados del Proceso

### Ciclo de Vida del Producto

| Estado de Negocio | Estado del Sistema | Responsable | Descripción |
|---|---|---|---|
| **Borrador** | Draft | Analista de Ventas | Producto en creación, no listo para sourcing |
| **Listo para Sourcing** | ReadyForSourcing | Analista de Ventas | Especificaciones completas, puede iniciar sourcing |
| **Sourcing en Progreso** | SourcingInProgress | Analista de Adquisiciones | Buscando proveedores y cotizaciones |
| **Revisión Interna** | InternalReviewPending | Líder de Equipo | Validación interna antes de enviar al cliente |
| **Cotizado al Cliente** | QuotedToCustomer | Analista de Ventas | Cotización enviada, esperando respuesta |
| **Aprobación Maqueta** | PendingVmApproval | Analista de Diseño | Cliente debe aprobar diseño virtual |
| **Aprobación Muestra** | PendingPpsApproval | Analista de Importaciones | Cliente debe aprobar muestra física |
| **Listo para Producción** | ReadyForProduction | Analista de Adquisiciones | Aprobado para inicio de fabricación |
| **En Producción** | InProduction | Analista de Adquisiciones | Producto siendo fabricado |
| **En Tránsito** | InternationalTransit | Analista de Importaciones | Producto en camino desde fábrica |
| **Despacho Aduanas** | CustomsClearance | Analista de Importaciones | Trámites aduaneros en curso |
| **Entregado** | Delivered | Analista de Importaciones | Producto entregado al cliente |

### Estados Especiales

| Estado de Negocio | Estado del Sistema | Descripción |
|---|---|---|
| **Requiere Revisión** | RevisionRequested | El producto debe volver a etapa anterior para correcciones |
| **Rechazado por Cliente** | CustomerRevisionRequested | Cliente solicita cambios en la propuesta |
| **Maqueta Rechazada** | VmRejected | Cliente no aprueba el diseño virtual |
| **Muestra Rechazada** | PpsRejected | Cliente no aprueba la muestra física |

---

## Documentos y Artefactos

### Documentos Principales

| Documento de Negocio | Artefacto del Sistema | Generado por | Descripción |
|---|---|---|---|
| **Cotización Cliente** | Customer Quotation | Analista de Ventas | Propuesta comercial formal |
| **Orden Cliente** | Customer Purchase Order | Cliente (registrada por Analista) | Confirmación de compra del cliente |
| **Orden Proveedor** | Supplier Purchase Order | Analista de Adquisiciones | Orden de compra al fabricante |
| **Maqueta Virtual** | Virtual Mockup | Analista de Diseño | Representación visual del producto |
| **Muestra Física** | Pre-Production Sample | Proveedor (gestionada por Analista) | Prototipo físico para aprobación |
| **Factura Proforma** | Proforma Invoice | Analista de Adquisiciones | Documento previo a producción |

### Información de Productos

| Concepto de Negocio | Campo del Sistema | Descripción |
|---|---|---|
| **Nombre Comercial** | Product Name | Nombre para mostrar al cliente |
| **Especificaciones Técnicas** | Product Specifications | Detalles técnicos completos |
| **Cantidad** | Quantity | Unidades solicitadas |
| **Precio Unitario** | Unit Price | Costo por unidad |
| **Tiempo de Entrega** | Lead Time | Días para fabricación |
| **MOQ** | Minimum Order Quantity | Cantidad mínima por pedido |

---

## Roles y Responsabilidades

### Roles del Sistema

| Rol de Negocio | Rol del Sistema | Responsabilidades Principales |
|---|---|---|
| **Analista de Ventas** | Sales Analyst | Crear proyectos, gestionar clientes, enviar cotizaciones |
| **Analista de Adquisiciones** | Procurement Analyst | Sourcing, negociar con proveedores, órdenes de compra |
| **Analista de Importaciones** | Import Analyst | Logística, aduanas, seguimiento de envíos |
| **Analista de Diseño** | Design Analyst | Crear maquetas virtuales, arte final |
| **Analista Financiero** | Finance Analyst | Control de costos, análisis de rentabilidad |
| **Líder de Equipo** | Team Leader | Supervisión, aprobaciones, gestión de equipo |
| **Administrador** | Admin | Configuración del sistema, gestión de usuarios |

### Permisos por Rol

| Acción | Sales | Procurement | Import | Design | Finance | Leader | Admin |
|---|---|---|---|---|---|---|---|
| Crear proyectos | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| Sourcing productos | ❌ | ✅ | ❌ | ❌ | ❌ | ✅ | ✅ |
| Gestionar envíos | ❌ | ❌ | ✅ | ❌ | ❌ | ✅ | ✅ |
| Crear maquetas | ❌ | ❌ | ❌ | ✅ | ❌ | ✅ | ✅ |
| Ver análisis financiero | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ |
| Gestionar equipo | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ |
| Configurar sistema | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ |

---

## Reglas de Negocio Principales

### Transiciones de Estado

**Regla:** Los productos solo pueden avanzar al siguiente estado si cumplen condiciones específicas:

- **Draft → ReadyForSourcing:** Especificaciones completas y validadas
- **SourcingInProgress → InternalReviewPending:** Al menos una cotización de proveedor
- **QuotedToCustomer → PendingVmApproval:** Cliente acepta cotización
- **ReadyForProduction → InProduction:** Orden de compra enviada al proveedor
- **InProduction → InternationalTransit:** Proveedor confirma envío

### Línea Base del Proyecto

**Regla:** Una vez alcanzado el Hito de Compromiso:
- Se crea una fotografía inmutable del proyecto (línea base)
- Los cambios posteriores requieren aprobación especial
- Se pueden comparar costos reales vs. planificados
- Base para cálculo de rentabilidad final

### Validación de Especificaciones

**Regla:** Cada producto debe tener especificaciones completas según su categoría:
- **Merchandising:** Capacidad, materiales, certificaciones
- **Material PDV:** Dimensiones, ensamblaje, montaje
- **Textiles:** GSM, tallas, cuidados, confección

### Control de Acceso

**Regla:** Las acciones están restringidas por rol y estado:
- Solo el responsable del estado actual puede hacer transiciones
- Líderes pueden intervenir en cualquier momento
- Cambios críticos requieren doble aprobación
- Historial completo de todas las acciones

### Gestión Financiera

**Regla:** Control estricto de costos y márgenes:
- Todos los montos se manejan con divisa específica
- Tipos de cambio fijos al momento de cotización
- Alertas automáticas por degradación de márgenes
- Comparación automática real vs. presupuestado

---

## Flujo de Información

### De Negocio a Sistema
1. **Oportunidad de negocio** → Se crea **Proyecto**
2. **Lista de productos** → Se crean **ProductItems**
3. **Búsqueda de proveedores** → **Sourcing en proceso**
4. **Cotización al cliente** → **Estado QuotedToCustomer**
5. **Aprobación cliente** → **Transición a producción**
6. **Seguimiento logístico** → **Estados de tránsito**

### De Sistema a Negocio
1. **Alertas automáticas** → Notificaciones por email/sistema
2. **Reportes de estado** → Dashboards por rol
3. **Análisis financiero** → Reportes de rentabilidad
4. **Métricas operativas** → KPIs de proceso
5. **Documentos generados** → PDFs para clientes/proveedores

---

## Referencias

- **Documentos relacionados:**
  - [02_Proceso_y_Actores.md](02_Proceso_y_Actores.md) → Actores y responsabilidades
  - [03_Modelo_de_Negocio.md](03_Modelo_de_Negocio.md) → Conceptos y flujos de negocio
  - [06_Roles_y_Permisos.md](06_Roles_y_Permisos.md) → Definición detallada de roles
- **Implementación técnica:** Ver `2_architecture/13_domain_to_code_mapping.md`