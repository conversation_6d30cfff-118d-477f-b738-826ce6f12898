# 4. Estrategia de Solución (Solution Strategy)

> **Propósito:** Resume las decisiones arquitectónicas fundamentales, el stack tecnológico y el enfoque general para resolver el problema de negocio.

---

## 4.1. Arquitectura General del Sistema

> 👔 **Para el Negocio:** Hemos decidido construir nuestro sistema como una **"casa todo en uno" (un monolito)** en lugar de varias "cabañas separadas" (microservicios). Este enfoque hace que la construcción inicial y el mantenimiento sean más sencillos y rápidos. Usamos tecnologías modernas y probadas que nos permiten crear una experiencia de usuario ágil y dinámica sin la complejidad de tener dos equipos (uno para el "motor" y otro para la "carrocería") trabajando por separado.

La aplicación sigue una arquitectura monolítica full-stack, donde la lógica de negocio y la interfaz de usuario son gestionadas dentro del mismo ecosistema de Laravel.

```mermaid
graph TB
    subgraph "Usuario"
        USER[Navegador Web]
    end

    subgraph "Servidor Web"
        NGINX[Nginx]
    end

    subgraph "Aplicación Monolítica (Laravel 12)"
        APP[PHP-FPM]
        
        subgraph "Capa de Presentación (Filament 4)"
            PANELS[Paneles de Administración]
            FORMS[Formularios y Tablas]
            LIVEWIRE[Componentes Reactivos Livewire]
            TAILWIND[Estilos con Tailwind CSS]
        end

        subgraph "Lógica de Negocio y Dominio"
            ROUTING[Rutas (web.php)]
            SERVICES[Servicios de Dominio]
            EVENTS[Eventos de Dominio]
            JOBS[Procesos en Background]
        end

        subgraph "Capa de Acceso a Datos"
            ORM[Eloquent ORM]
        end
    end

    subgraph "Capa de Persistencia y Servicios"
        DB[(PostgreSQL Database)]
        CACHE[Redis Cache]
        QUEUE[Redis Queue]
        FILES[Almacenamiento de Archivos (S3)]
    end

    USER --> NGINX
    NGINX --> APP
    
    APP --> DB
    APP --> CACHE
    APP --> QUEUE
    APP --> FILES
```

## 4.2. Stack Tecnológico

### Backend Framework: Laravel 12

- **Justificación:** Ecosistema robusto, desarrollo rápido, ORM potente (Eloquent), y funcionalidades de seguridad y colas de trabajo integradas.

### Frontend y UI: Filament 4, Livewire y Tailwind CSS

- **Justificación:** Se opta por la pila TALL (Tailwind, Alpine.js, Laravel, Livewire) para unificar el desarrollo en un solo lenguaje (PHP) y ecosistema. Filament permite construir interfaces de administración complejas de forma declarativa y rápida, mientras que Livewire proporciona la reactividad de una SPA sin la complejidad de un framework de JavaScript separado.

### Base de Datos: PostgreSQL 12+

- **Justificación:** Se elige por su soporte superior para tipos de datos complejos como JSONB (crítico para las especificaciones de producto), su robustez y su estricta adherencia a los estándares SQL (ver ADR-005).

### Caching y Colas: Redis

- **Justificación:** Alto rendimiento para sesiones, caché de aplicación y gestión de trabajos en segundo plano.

### Almacenamiento de Archivos: AWS S3 Compatible

- **Justificación:** Almacenamiento de objetos escalable, duradero y seguro, con integración nativa en Laravel.

---

## 4.3. Estrategia de Procesamiento Asíncrono

Para garantizar una experiencia de usuario fluida y tiempos de respuesta rápidos, el sistema adoptará una estrategia de procesamiento asíncrono. Todas las tareas que puedan ser lentas o que no necesiten ser completadas de forma inmediata (ej. envío de correos, generación de PDFs, webhooks) se descargarán a un **sistema de colas (Queues)** que las procesará en segundo plano. Esto asegura que el usuario nunca tenga que esperar por una operación pesada.