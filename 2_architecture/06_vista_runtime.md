# 6. Vista de Tiempo de Ejecución (Runtime View)

> **Propósito:** Describe el comportamiento del sistema en tiempo de ejecución, mostrando los flujos principales de la arquitectura CRUD + Version Tracking durante la ejecución de casos de uso críticos.

---

## 6.1 Escenarios de Tiempo de Ejecución

### Escenario RT-01: Creación de ProductItem
**Actor:** Sales Analyst  
**Disparador:** Usuario crea nuevo ítem de producto en proyecto

```mermaid
sequenceDiagram
    participant User as Sales Analyst
    participant UI as Filament Admin
    participant Action as CreateProductItemAction
    participant DTO as CreateProductItemData
    participant Model as ProductItem
    participant Version as ProductItemVersionService
    participant DB as PostgreSQL

    User->>UI: Completa formulario nuevo ProductItem
    UI->>DTO: Valida datos entrada
    DTO->>Action: execute(CreateProductItemData)
    Action->>Model: ProductItem::create()
    Model->>DB: INSERT INTO product_items
    DB-->>Model: ProductItem creado
    Action->>Version: createVersion('creation')
    Version->>DB: INSERT INTO product_item_versions
    DB-->>Version: Version registrada
    Action-->>UI: ProductItem creado + versión inicial
    UI-->>User: "Item creado exitosamente"
    
    Note over Version: Hito inicial registrado para RF-18
```

### Escenario RT-02: Re-cotización de ProductItem (Flujo Crítico)
**Actor:** Procurement Analyst  
**Disparador:** Cambios en especificaciones o precios del proveedor

```mermaid
sequenceDiagram
    participant User as Procurement Analyst
    participant UI as Filament Admin
    participant Action as RequoteProductItemAction
    participant DTO as RequoteProductItemData
    participant Model as ProductItem
    participant Version as ProductItemVersionService
    participant Enum as ProductItemStatus
    participant DB as PostgreSQL

    User->>UI: "Re-cotizar con nuevas specs/precios"
    UI->>DTO: Captura datos cambio
    Note over DTO: previous_price, new_price, reason, etc.
    
    DTO->>Action: execute(RequoteProductItemData)
    Action->>Model: Obtener estado actual
    Model-->>Action: Estado y datos previos
    
    Action->>Enum: Validar transición estado
    Enum-->>Action: Transición válida
    
    Action->>Model: update(specifications, status)
    Model->>DB: UPDATE product_items
    DB-->>Model: Actualización confirmada
    
    Action->>Version: createVersion('requote', previous, new)
    Version->>Version: generateRequoteSummary()
    Version->>DB: INSERT INTO product_item_versions
    DB-->>Version: Versión creada
    
    Action-->>UI: ProductItem actualizado
    UI-->>User: "Re-cotización procesada exitosamente"
    
    Note over Action,Version: Core del business milestone logging
```

### Escenario RT-03: Cálculo de Estado de Proyecto
**Actor:** Sistema (automático)  
**Disparador:** Cambio en cualquier ProductItem del proyecto

```mermaid
sequenceDiagram
    participant Trigger as State Change Event
    participant Service as ProjectStatusCalculatorService
    participant Project as Project Model
    participant Items as ProductItem Collection
    participant Enum as ProjectStatus Enum
    participant DB as PostgreSQL

    Trigger->>Service: calculateProjectStatus(project_id)
    Service->>Project: with('productItems')
    Project->>DB: SELECT project + related items
    DB-->>Project: Project + ProductItems
    
    Project-->>Service: Project con items
    Service->>Items: Obtener todos los estados
    Items-->>Service: Collection de estados
    
    Service->>Service: Aplicar lógica precedencia
    Note over Service: Estado más crítico/menos avanzado
    
    Service->>Enum: Determinar estado proyecto
    Enum-->>Service: Estado calculado
    
    Service->>Project: update(computed_status)
    Project->>DB: UPDATE projects
    DB-->>Project: Estado actualizado
    
    Service-->>Trigger: Estado proyecto actualizado
    
    Note over Service: Lógica simple sin Event Sourcing
```

---

## 6.2 Casos de Uso Críticos

### CU-01: Flujo de Aprobación VM (Virtual Mockup)
**Complejidad:** Media  
**Frequency:** Alta (múltiples por día)

```mermaid
flowchart TD
    A[VM Created] --> B{Cliente Revisa}
    B -->|Aprueba| C[VM Approved]
    B -->|Rechaza| D[Customer Revision Requested]
    B -->|Solicita Cambios| E[Spec Update Required]
    
    C --> F[ProductItem.status = VM_APPROVED]
    D --> G[ProductItem.status = CUSTOMER_REVISION_REQUESTED]
    E --> H[ProductItem.status = SPEC_UPDATE_REQUIRED]
    
    F --> I[Version: 'vm_approval' + approval_result]
    G --> J[Version: 'customer_rejection' + reason]
    H --> K[Version: 'spec_update' + changes]
    
    I --> L[Notification to PPS team]
    J --> M[Notification to Sales Analyst]  
    K --> N[Notification to Procurement]
    
    style C fill:#90EE90
    style D fill:#FFB6C1
    style E fill:#FFE4B5
```

### CU-02: Seguimiento Financiero Integrado
**Complejidad:** Alta  
**Frequency:** Continua

```mermaid
flowchart LR
    A[ProductItem Changes] --> B[Cost Recalculation]
    B --> C[Margin Analysis]
    C --> D[Project Profitability]
    D --> E[Financial Reporting]
    
    B --> F[(Version History)]
    F --> G[Cost Evolution Tracking]
    
    C --> H[Real-time Alerts]
    H --> I{Margin Below Threshold?}
    I -->|Yes| J[Alert Finance Team]
    I -->|No| K[Continue Monitoring]
    
    style J fill:#FF6B6B
    style K fill:#90EE90
```

### CU-03: Gestión de Excepciones y Rechazos
**Complejidad:** Alta  
**Frequency:** Variable (1-5 por semana)

```mermaid
sequenceDiagram
    participant Client as Cliente
    participant Sales as Sales Analyst
    participant UI as Sistema
    participant Action as HandleRejectionAction
    participant Version as VersionService
    participant Notify as NotificationService

    Client->>Sales: Rechaza propuesta
    Sales->>UI: Registra rechazo formal
    UI->>Action: execute(rejection_data)
    
    Action->>Action: Determinar acciones requeridas
    Note over Action: ¿Re-sourcing? ¿Cancelación?
    
    Action->>Version: createVersion('customer_rejection')
    Action->>Notify: Notificar equipos afectados
    
    par Procesos Paralelos
        Notify->>Sales: Actualizar estrategia
    and
        Notify->>Sales: Re-evaluar especificaciones
    and
        Notify->>Sales: Considerar proveedores alternativos
    end
    
    Note over Action,Version: Trazabilidad completa del rechazo
```

---

## 6.3 Flujos de Trabajo Principales

### Workflow WF-01: Ciclo Completo CRUD + Tracking

```mermaid
graph TD
    subgraph "Fase 1: Creación"
        A1[Create Project] --> A2[Add ProductItems]
        A2 --> A3[Initial Specifications]
        A3 --> A4[Version: 'creation']
    end
    
    subgraph "Fase 2: Sourcing"
        B1[Supplier Research] --> B2[RFQ Generation]
        B2 --> B3[Quote Comparison]
        B3 --> B4[Supplier Selection]
        B4 --> B5[Version: 'sourcing_complete']
    end
    
    subgraph "Fase 3: Cliente Quote"
        C1[Price Calculation] --> C2[Quote Generation]
        C2 --> C3[Client Review]
        C3 --> C4{Approval?}
        C4 -->|Yes| C5[Version: 'client_approved']
        C4 -->|Changes| C6[Version: 'requote']
        C4 -->|Reject| C7[Version: 'client_rejected']
    end
    
    subgraph "Fase 4: Production"
        D1[PO Generation] --> D2[VM Creation]
        D2 --> D3[PPS Production]
        D3 --> D4[Quality Control]
        D4 --> D5[Final Delivery]
        D5 --> D6[Version: 'delivered']
    end
    
    A4 --> B1
    B5 --> C1
    C5 --> D1
    C6 --> B1
    C7 --> X[Project Analysis/Archive]
    D6 --> Y[Project Complete]
    
    style C5 fill:#90EE90
    style C7 fill:#FFB6C1
    style Y fill:#87CEEB
```

### Workflow WF-02: Version Tracking Strategy

```mermaid
flowchart TD
    A[Business Event Occurs] --> B{Significant Change?}
    B -->|Yes| C[Create Version Entry]
    B -->|No| D[Standard Update Only]
    
    C --> E[ProductItemVersionService]
    E --> F[Determine Change Type]
    
    F --> G{Change Type}
    G -->|creation| H[Initial baseline]
    G -->|requote| I[Price/spec changes]
    G -->|status_change| J[Workflow progression]
    G -->|vm_approval| K[Client approval tracking]
    G -->|customer_rejection| L[Rejection analysis]
    G -->|manual_correction| M[Admin intervention]
    
    H --> N[(version_history)]
    I --> N
    J --> N  
    K --> N
    L --> N
    M --> N
    
    N --> O[RF-18: Business Milestone Log]
    O --> P[Audit Trail & Analytics]
    
    style C fill:#87CEEB
    style O fill:#90EE90
    style P fill:#FFE4B5
```

### Workflow WF-03: Error Handling & Recovery

```mermaid
flowchart TD
    A[Operation Starts] --> B{Validation Passes?}
    B -->|No| C[Return Validation Errors]
    B -->|Yes| D[Execute Business Logic]
    
    D --> E{Database Transaction OK?}
    E -->|No| F[Rollback + Log Error]
    E -->|Yes| G[Version Service Call]
    
    G --> H{Version Creation OK?}
    H -->|No| I[Log Warning - Continue]
    H -->|Yes| J[Success Response]
    
    F --> K[Error Response + User Notification]
    I --> L[Success with Warning Log]
    
    C --> M[User Corrects Input]
    M --> A
    
    K --> N[Admin Notification]
    L --> O[Background Monitoring]
    
    style F fill:#FFB6C1
    style I fill:#FFE4B5
    style J fill:#90EE90
```

---

## 6.4 Patrones de Rendimiento

### Patrón P-01: Eager Loading Strategy
**Problema:** N+1 queries en listados complejos  
**Solución:** Precarga selectiva basada en contexto

```php
// Ejemplo optimizado para lista de proyectos con estados calculados
Project::with([
    'productItems:id,project_id,status',
    'customer:id,name',
    'latestProductItemVersions' => function($query) {
        $query->latest('version_number');
    }
])->get();
```

### Patrón P-02: Computed Status Caching
**Problema:** Cálculo repetitivo de estados de proyecto  
**Solución:** Cache inteligente con invalidación por eventos

```php
// Cached project status calculation
public function getComputedStatusAttribute()
{
    return Cache::remember(
        key: "project_status_{$this->id}",
        ttl: 3600, // 1 hour
        callback: fn() => $this->calculateStatusFromItems()
    );
}
```

### Patrón P-03: Background Version Processing
**Problema:** Version creation puede ser lenta  
**Solución:** Queue jobs para operaciones no críticas

```php
// Asynchronous version processing for heavy operations
dispatch(new CreateProductItemVersionJob($item, $changeData))
    ->onQueue('versions');
```

---

## 6.5 Consideraciones de Despliegue

### Configuración Runtime
- **Database Connection Pool:** 20 conexiones concurrentes
- **Cache Layer:** Redis para session + application cache
- **Queue Workers:** 3 workers para version processing
- **File Storage:** Local disk + S3 backup para archivos

### Monitoreo Runtime
- **Response Time:** < 2s para 95% operaciones
- **Version Creation:** < 5s para operaciones complejas
- **Database Performance:** < 500ms para 99% queries
- **Memory Usage:** < 512MB per request

### Escalabilidad Runtime
- **Horizontal Scaling:** Load balancer + múltiples app servers
- **Database Scaling:** Read replicas para reportes
- **Cache Scaling:** Redis cluster para alta disponibilidad
- **Storage Scaling:** CDN para archivos estáticos

---

## 6.6 Trazabilidad y Auditoría en Runtime

### Logging Strategy
```mermaid
flowchart LR
    A[User Action] --> B[Application Log]
    A --> C[Audit Trail]
    A --> D[Version History]
    
    B --> E[Error Tracking]
    C --> F[Compliance Reports]
    D --> G[Business Analytics]
    
    E --> H[(ELK Stack)]
    F --> I[(Audit DB)]
    G --> J[(Data Warehouse)]
```

Esta vista de runtime proporciona la visión detallada del comportamiento del sistema durante la ejecución, complementando la arquitectura estática definida en los documentos anteriores y cumpliendo con los requisitos de trazabilidad (RF-18) y notificaciones (RF-09) identificados en la revisión.