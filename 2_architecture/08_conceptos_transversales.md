# 8. Conceptos Transversales (Crosscutting Concepts)

> **Propósito:** Explica las regulaciones, patrones y convenciones que afectan a múltiples partes del sistema, asegurando consistencia y calidad en todo el código.

---

## 8.1. Patrones Arquitectónicos y de Diseño

Para mantener la consistencia y la calidad del código, el sistema se adhiere a un conjunto de patrones de diseño simplificados, como se define en los ADRs correspondientes.

- **Action Pattern:** Encapsula cada operación de negocio en una clase única con responsabilidad clara.
- **Money Value Object Pattern:** Encapsula los valores monetarios en un objeto `Money` para garantizar la seguridad de tipos y centralizar la lógica financiera (ADR-011).
- **State Transition Service Pattern:** La lógica de negocio para la transición entre estados se centraliza en un servicio dedicado (`StateTransitionService`) para mayor robustez y testeabilidad. Los Enums se usan para definir los estados, pero no para validar las transiciones entre ellos.
- **Version Tracking Pattern:** Tracking selectivo de cambios significativos usando tabla adicional.
- **Service Layer Pattern:** Separa lógica compleja cross-domain del CRUD directo.
- **DTO Pattern:** Contratos tipados para validación y transferencia de datos.
- **Componentes de Livewire y Filament:** Para la construcción de la interfaz de usuario de forma declarativa y reactiva.

---

## 8.2. Guías de Implementación Específicas

### Validación con Form Requests
Toda la validación de datos de entrada provenientes de peticiones HTTP debe realizarse en clases `FormRequest` dedicadas. Esto mantiene los controladores y `Actions` limpios de lógica de validación, la hace reutilizable y fácil de probar de forma aislada.

### Enums como Objetos de Valor (ADR-008)
Para representar estados y categorías, se deben utilizar Enums de PHP. Su responsabilidad se centra en definir los estados y encapsular la lógica de UI (etiquetas, colores), pero **no la lógica de transición**, que es manejada por el `StateTransitionService` (ADR-010).

### Data Transfer Objects (DTOs) (ADR-007)
Para pasar datos a las `Actions` o `Services`, se deben usar DTOs (`spatie/laravel-data`) para asegurar un contrato de datos claro y seguro.

### Convenciones para Actions, Enums y DTOs

#### Nomenclatura de Actions
- **Actions**: `{Verb}{Entity}Action` (ej. `CreateProductItemAction`, `RequoteProductItemAction`)
- **Directorio**: `app/Actions/{Domain}/` organizados por dominio de negocio
- **Método principal**: `execute()` con DTO como parámetro

#### Estructura de Enums
- **Valores**: `snake_case` para consistencia con la BD (ej. `ready_for_sourcing`).
- **Métodos UI obligatorios**: `getLabel()`, `getColor()` para consistencia en Filament.
- **Métodos contextuales**: `getPhase()`, `getResponsibleActor()` para información de negocio.
- **Lógica de Transición**: **NO** debe estar en el Enum. Es responsabilidad del `StateTransitionService`.

#### Version Tracking
- **Service**: `ProductItemVersionService` centraliza toda la lógica de versioning
- **Campos requeridos**: `change_type`, `previous_data`, `new_data`, `changed_by`, `changed_at`
- **Tipos de cambios**: `'creation'`, `'requote'`, `'spec_update'`, `'status_change'`

#### DTOs Organizados por Dominio
- **Estructura**: `app/Data/{EntityName}/{ActionName}Data.php`
- **Ejemplos**: `CreateProductItemData`, `TransitionProductItemData`
- **Validación**: Incluir reglas específicas en `rules()` cuando sea necesario

#### Integración Estado → Enum → UI
- Cada `State` debe implementar `status(): EnumCase` 
- El enum proporciona métodos para etiquetas, colores y lógica UI
- Filament consume directamente los métodos del enum para consistencia visual

---

## 8.3. Convenciones de Base de Datos y Rendimiento

### Prevenir Problemas N+1 con Eager Loading

Es **mandatorio** el uso de Eager Loading (`->with('relacion')`) en todas las consultas de Eloquent que recuperen una colección de modelos y que posteriormente vayan a acceder a sus relaciones. Esta es la optimización de rendimiento más importante para prevenir cientos de consultas innecesarias a la base de datos.

**Ejemplo:**
```php
// Malo ❌ (Genera N+1 consultas)
$items = ProductItem::all();
foreach ($items as $item) {
    echo $item->project->name; // Se ejecuta una consulta por cada ítem
}

// Bueno ✅ (Genera solo 2 consultas)
$items = ProductItem::with('project')->get();
foreach ($items as $item) {
    echo $item->project->name; // No hay nuevas consultas
}
```

---

## 8.4. Guías de Testing y Desarrollo

### Seeders y Factories para Entornos de Prueba
El estado de la base de datos para las pruebas automatizadas y para los entornos de desarrollo locales debe ser generado exclusivamente a través de **Factories y Seeders**. Esto garantiza un entorno de pruebas consistente, predecible y fácil de reconstruir para todos los desarrolladores.

### Proceso de Testing
Se exige una cobertura de pruebas superior al 80%. El framework de testing estándar es **Pest 4**, y las pruebas de componentes Livewire se realizan en PHP para simular interacciones del usuario y verificar el comportamiento de la UI.

### Proceso de Desarrollo (CI/CD)
- **Control de Versiones:** Se utiliza Git con un flujo de trabajo basado en ramas (`feature-branches`).
- **Integración Continua (CI):** Se utiliza GitHub Actions para ejecutar la suite de pruebas completa en cada `pull request`.
- **Despliegue Continuo (CD):** Tras una fusión a la rama `main`, un pipeline automatizado despliega la nueva versión a producción.