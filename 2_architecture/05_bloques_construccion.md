# 5. Vista de Bloques de Construcción (Building Block View)

> **Referencia**: [Arquitectura Simplificada](00_arquitectura_integrada.md) - Especificación CRUD + Version Tracking

---

## 5.1 Sistema Completo - Arquitectura Simplificada

PromoSmart implementa una **arquitectura CRUD simplificada** con **version tracking selectivo** para cumplir el requisito real de negocio: **"lista de versiones de productos"** derivados de re-cotizaciones.

```
┌─────────────────────────────────────────────────────────┐
│                    UI Layer (Filament)                  │
│  Admin Panels | Forms | Tables | Charts | Dashboards   │
└─────────────────┬───────────────────────────────────────┘
                  │ HTTP Requests + Form Data
┌─────────────────▼───────────────────────────────────────┐
│             Application Layer (Actions + DTOs)          │
│  ProductItem Actions | Project Actions | User Actions   │
└─────────────────┬───────────────────────────────────────┘
                  │ Validated Business Operations
┌─────────────────▼───────────────────────────────────────┐
│              Domain Layer (Eloquent Models)             │
│  ProductItem + ProductItemVersion + Project + Others    │
└─────────────────┬───────────────────────────────────────┘
                  │ Direct DB Operations + Version Service
┌─────────────────▼───────────────────────────────────────┐
│        Infrastructure Layer (Services + Logging)       │
│  ProductItemVersionService | ProjectStatusCalculator    │
│                Standard Laravel Infrastructure          │
└─────────────────┬───────────────────────────────────────┘
                  │ Database Operations
┌─────────────────▼───────────────────────────────────────┐
│           Database Layer (PostgreSQL)                   │
│  Standard Tables + product_item_versions                │
└─────────────────────────────────────────────────────────┘
```

## 5.2 Nivel 2 - Bloques Organizados por Dominio

La arquitectura simplificada organiza componentes por dominios de negocio, manteniendo patrones útiles sin complejidad innecesaria:

```
app/
├── Actions/            # 🎭 Orquestación de Casos de Uso
│   ├── Project/
│   │   ├── CreateProjectAction.php      # CRUD simple
│   │   ├── UpdateProjectAction.php      # CRUD simple
│   │   ├── CreateProjectBaselineAction.php # Snapshot para análisis financiero
│   │   └── CalculateProjectStatusAction.php # Service call
│   ├── ProductItem/    # CRUD + Version Tracking
│   │   ├── CreateProductItemAction.php  # CRUD simple
│   │   ├── UpdateProductItemAction.php  # CRUD + conditional versioning
│   │   └── RequoteProductItemAction.php # CRUD + mandatory versioning
│   └── User/
│       ├── CreateUserAction.php         # CRUD simple
│       ├── UpdateUserAction.php         # CRUD simple
│       └── ManageUserRolesAction.php    # Role management
├── Casts/              # 🔩 Casts personalizados de Eloquent
│   └── MoneyCast.php
├── Data/               # 📋 DTOs con Validación Tipada
│   ├── Project/
│   │   ├── CreateProjectData.php        # Validación standard
│   │   └── UpdateProjectData.php        # Validación standard
│   ├── ProductItem/
│   │   ├── CreateProductItemData.php    # Validación standard
│   │   ├── UpdateProductItemData.php    # Validación standard
│   │   └── RequoteProductItemData.php   # Validación + change tracking
│   └── User/
│       ├── CreateUserData.php           # Validación standard
│       └── UpdateUserData.php           # Validación standard
├── Services/           # 🔧 Lógica Compleja Cross-Domain
│   ├── ProductItemVersionService.php    # Version management
│   ├── ProjectStatusCalculatorService.php # Status calculation
│   ├── StateTransitionService.php     # Business rules for state changes
│   ├── ProjectProfitabilityService.php  # Cálculos de rentabilidad vs baseline
│   ├── ProductSpecificationValidator.php # Taxonomy-based validation
│   ├── CategorySpecificValidator.php   # Category-specific business rules
│   └── ReportGenerationService.php      # Cross-domain reporting
├── Models/             # 📊 Standard Eloquent Models
│   ├── ProductItem.php                  # Standard model + taxonomy integration
│   ├── ProductItemVersion.php           # Version tracking model
│   ├── Project.php                      # Standard model
│   ├── User.php                         # Standard model
│   ├── Customer.php                     # Standard model
│   ├── ProductCategory.php              # Taxonomy: Main categories (3)
│   ├── ProductSubcategory.php           # Taxonomy: Specific classifications
│   └── ProductSpecificationStructure.php # Taxonomy: Specification templates
├── ValueObjects/       # 💎 Objetos de Valor Inmutables
│   └── Money.php
├── Enums/              # 🎛️ Business Rules + UI Logic
│   ├── ProductItemStatus.php            # Con métodos UI + business logic
│   ├── ProjectStatus.php                # Con métodos de cálculo
│   └── UserRole.php                     # Role management
└── Http/               # 🌐 Standard Laravel
    ├── Controllers/     # Standard controllers
    └── Resources/       # Filament resources
```

### 5.2.1 Application Layer - Actions como Orquestadores

**Actions/**: Cada caso de uso tiene un Action dedicado que orquesta la operación completa. Los Actions para ProductItem incluyen version tracking cuando es apropiado.

**Data/DTOs**: Contratos tipados entre UI y lógica de negocio. Incluyen validación automática y transformación de datos.

### 5.2.2 Domain Layer - Models Eloquent Standard

**Models/**: Standard Eloquent models sin complejidad adicional. `ProductItemVersion` es una tabla adicional simple para tracking de cambios.

**Enums/**: Definen los estados y categorías como objetos de valor. Contienen lógica de UI (etiquetas, colores) y de contexto, pero la lógica de transición de estados se delega a `StateTransitionService`.

### 5.2.3 Infrastructure Layer - Services para Lógica Compleja

**Services/**: Encapsulan lógica compleja que trasciende un solo modelo. El `ProductItemVersionService` maneja todo el version tracking.

---

## 5.3 Nivel 3 - Inventario de Componentes Simplificados

### 5.3.1 Actions por Dominio - Casos de Uso Orquestados

**Project/** - Gestión de Proyectos
- `CreateProjectAction` → CRUD simple con validación DTO
- `UpdateProjectAction` → CRUD simple con logging estándar  
- `CalculateProjectStatusAction` → Service call para estado derivado

**ProductItem/** - Core del Sistema con Version Tracking  
- `CreateProductItemAction` → CRUD simple, no versioning (estado inicial)
- `UpdateProductItemAction` → CRUD + conditional version tracking para cambios significativos
- `RequoteProductItemAction` → CRUD + mandatory version tracking (siempre crea versión)

**User/** - Gestión de Usuarios
- `CreateUserAction` → Standard user creation con role assignment
- `UpdateUserAction` → Profile updates con validación
- `ManageUserRolesAction` → Role management con enum validation

### 5.3.2 DTOs por Dominio - Contratos Tipados

**Project/** - Validación de Proyectos
- `CreateProjectData` - Cliente, nombre, descripción, timeline
- `UpdateProjectData` - Actualizaciones con validación de business rules

**ProductItem/** - Validación con Context de Versioning
- `CreateProductItemData` - Proyecto, especificaciones, estado inicial
- `UpdateProductItemData` - Cambios generales con change reason opcional
- `RequoteProductItemData` - Nuevas especificaciones, precios, razón obligatoria

**User/** - Gestión de Usuarios
- `CreateUserData` - Standard user fields con role validation
- `UpdateUserData` - Profile updates con security validation

### 5.3.3 Services - Lógica Compleja Cross-Domain

**ProductItemVersionService** - Version Management Core
```php
class ProductItemVersionService
{
    public function createVersion(
        ProductItem $item,
        string $changeType,
        array $previousData,
        array $newData,
        ?string $summary = null
    ): ProductItemVersion;
    
    public function getVersionHistory(ProductItem $item): Collection;
    
    public function hasSignificantChanges(array $previous, array $new): bool;
}
```

**ProjectStatusCalculatorService** - Estado Derivado
```php
class ProjectStatusCalculatorService
{
    public function calculate(Project $project): ProjectStatus;
    
    public function getStatusBreakdown(Project $project): array;
}
```

**ReportGenerationService** - Cross-Domain Reporting
```php
class ReportGenerationService  
{
    public function generateProjectSummary(Project $project): array;
    
    public function generateVersionReport(ProductItem $item): array;
}
```

### 5.3.4 Enums - Business Rules Engine Simplificado

**ProductItemStatus** (16 estados + métodos de UI y contexto)
```php
enum ProductItemStatus: string
{
    case DRAFT = 'draft';
    // ... otros estados
    
    // UI Logic
    public function getLabel(): string;
    public function getColor(): string;
    
    // Business Context
    public function getPhase(): string;
}
```

**ProjectStatus** (8 estados derivados)
```php
enum ProjectStatus: string
{
    case DRAFT = 'draft';
    case SOURCING = 'sourcing';
    case QUOTED = 'quoted';
    case COMPLETED = 'completed';
    
    // Calculation Logic
    public static function calculateFromItems(Collection $productItems): self;
    
    // UI Logic  
    public function getLabel(): string;
    public function getProgressPercentage(): int;
}
```

### 5.3.5 Models - Standard Eloquent

**ProductItem** - Core Entity
```php
class ProductItem extends Model
{
    protected $fillable = ['project_id', 'name', 'status', 'specifications'];
    
    protected $casts = [
        'status' => ProductItemStatus::class,
        'specifications' => 'array'
    ];
    
    public function project(): BelongsTo;
    public function versions(): HasMany; // Relationship to ProductItemVersion
}
```

**ProductItemVersion** - Version Tracking
```php
class ProductItemVersion extends Model
{
    protected $fillable = [
        'product_item_id', 'version_number', 'change_type', 
        'changes_summary', 'previous_data', 'new_data', 
        'changed_by', 'changed_at'
    ];
    
    protected $casts = [
        'previous_data' => 'array',
        'new_data' => 'array',
        'changed_at' => 'datetime'
    ];
    
    public function productItem(): BelongsTo;
    public function changedBy(): BelongsTo; // User relationship
}
```

## 5.4 Flujo Integrado - Ejemplo RequoteProductItem

### 5.4.1 Secuencia Completa

```mermaid
sequenceDiagram
    participant UI as Filament UI
    participant Action as RequoteProductItemAction
    participant DTO as RequoteProductItemData
    participant Model as ProductItem Model
    participant Version as ProductItemVersionService
    participant DB as Database

    UI->>Action: execute(formData)
    Action->>DTO: new RequoteProductItemData(validated)
    Action->>Model: Capturar datos previos
    Action->>Model: update(newSpecs, newStatus)
    Model->>DB: UPDATE product_items
    DB-->>Model: OK
    
    Action->>Version: createVersion(item, 'requote', previous, new, reason)
    Version->>DB: INSERT into product_item_versions
    DB-->>Version: version created
    
    Action-->>UI: return updated ProductItem
    
    Note over Version: Lista de versiones disponible para auditoría
```

### 5.4.2 Código Simplificado

```php
// Flujo completo en ~20 líneas
class RequoteProductItemAction
{
    public function execute(RequoteProductItemData $data): ProductItem
    {
        $item = $data->productItem;
        
        // Capturar estado previo
        $previousData = [
            'specifications' => $item->specifications,
            'status' => $item->status->value,
            'price' => $data->previousPrice,
        ];
        
        // CRUD update simple
        $item->update([
            'specifications' => $data->newSpecifications,
            'status' => $data->newStatus,
        ]);
        
        // Version tracking obligatorio para requotes
        $this->versionService->createVersion(
            item: $item,
            changeType: 'requote',
            previousData: $previousData,
            newData: [
                'specifications' => $data->newSpecifications,
                'status' => $data->newStatus->value,
                'price' => $data->newPrice,
            ],
            summary: $data->reason
        );
        
        return $item;
    }
}
```

## 5.5 Beneficios de la Arquitectura Simplificada

### 5.5.1 Development Speed
- ⚡ **10x más rápido** que Event Sourcing
- 🛠️ **~200 líneas** vs ~2000 líneas de código
- 📅 **3-4 días** vs 3-4 semanas de desarrollo

### 5.5.2 Team Productivity  
- 👥 **Cualquier dev Laravel** puede trabajar inmediatamente
- 🔍 **Debugging simple** y directo
- 📚 **Curva de aprendizaje mínima**

### 5.5.3 Satisface Requisitos
- ✅ **Lista de versiones**: Exactamente lo que se necesita
- ✅ **Cambios por re-cotizaciones**: Tracked automáticamente  
- ✅ **Audit trail**: Para cambios significativos
- ✅ **Quién cambió qué y cuándo**: Información completa

---

## 5.6 Patrones Eliminados (No Necesarios)

### ❌ Removidos por Simplicidad

**Event Sourcing Components**
```diff
- Aggregates/         # Business logic encapsulada → Reemplazado por Actions + Enums
- Events/             # Domain events inmutables → No necesario para requisito
- Projectors/         # Vista optimizada automática → CRUD directo es suficiente  
- Reactors/           # Side effects separados → Logging manual en Actions
```

**Spatie Model States**
```diff
- States/ProductItem/ # State classes → Reemplazado por Enum methods
- StateTransitions/   # Transition classes → Validación en Enum.canTransitionTo()
```

### ✅ Mantenidos por Utilidad

**Core Patterns**
- `Actions/` - Orquestación útil de casos de uso
- `Data/DTOs` - Tipado fuerte + validación automática  
- `Services/` - Lógica compleja cross-domain
- `Enums/` con métodos - Business rules + UI logic centralizadas

---

## 5.7 Resumen de Arquitectura Simplificada

La documentación de bloques de construcción refleja una **arquitectura pragmática** que satisface exactamente el requisito de negocio:

- **CRUD + Version Tracking** como patrón central simple
- **ProductItemVersionService** para gestión de versiones
- **Actions + DTOs** para orquestación tipada
- **Enums con métodos** para business rules centralizadas
- **Standard Eloquent** para persistencia optimizada

Esta simplificación proporciona **exactamente lo que el negocio necesita** - lista de versiones de productos - sin la complejidad innecesaria de Event Sourcing para un requisito que no lo justifica.

**Resultado**: Implementación rápida, mantenimiento simple, y team productivity optimizada para satisfacer el verdadero requisito de PromoSmart.

---

## 5.8 Dominio de Taxonomía de Productos - Clasificación y Validación

### 5.8.1 Arquitectura CRUD para Taxonomía

El dominio de taxonomía proporciona clasificación jerárquica de productos y validación de especificaciones usando el patrón CRUD simplificado:

```mermaid
graph TD
    subgraph "Taxonomy Domain"
        A[ProductCategory] --> B[3 Main Categories]
        C[ProductSubcategory] --> D[Specific Classifications]  
        E[ProductSpecificationStructure] --> F[Validation Templates]
        G[ProductItem.specifications] --> H[JSON Validated Data]
    end
    
    subgraph "CRUD Operations"
        I[CreateProductItemAction] --> J[Taxonomy Validation]
        K[UpdateProductItemAction] --> J
        L[ManageProductCategoriesAction] --> A
    end
    
    subgraph "Validation Services"
        M[ProductSpecificationValidator] --> F
        N[CategorySpecificValidator] --> B
        O[SpecificationIntegrityService] --> H
    end
```

### 5.8.2 Implementación de Componentes de Taxonomía

**Models de Taxonomía**
```php
// app/Models/ProductCategory.php
class ProductCategory extends Model
{
    protected $fillable = ['name', 'description', 'sort_order'];
    
    public function subcategories(): HasMany
    {
        return $this->hasMany(ProductSubcategory::class);
    }
    
    // 3 categorías principales: Merchandising, Material PDV, Textiles
    public function scopeMerchandising($query) { return $query->where('name', 'Merchandising'); }
    public function scopeMaterialPDV($query) { return $query->where('name', 'Material PDV'); }
    public function scopeTextiles($query) { return $query->where('name', 'Textiles'); }
}

// app/Models/ProductSubcategory.php  
class ProductSubcategory extends Model
{
    protected $fillable = [
        'product_category_id', 'name', 'slug', 'description', 'sort_order'
    ];
    
    public function productCategory(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class);
    }
    
    public function productSpecificationStructure(): HasOne
    {
        return $this->hasOne(ProductSpecificationStructure::class);
    }
    
    public function productItems(): HasMany
    {
        return $this->hasMany(ProductItem::class);
    }
}

// app/Models/ProductSpecificationStructure.php
class ProductSpecificationStructure extends Model
{
    protected $fillable = [
        'product_subcategory_id',
        'required_attributes',
        'optional_attributes', 
        'validation_rules',
        'category_specific_rules'
    ];
    
    protected $casts = [
        'required_attributes' => 'array',
        'optional_attributes' => 'array',
        'validation_rules' => 'array',
        'category_specific_rules' => 'array',
    ];
    
    public function productSubcategory(): BelongsTo
    {
        return $this->belongsTo(ProductSubcategory::class);
    }
}
```

**Actions con Validación de Taxonomía**
```php
// app/Actions/ProductItem/CreateProductItemAction.php (Enhanced)
class CreateProductItemAction
{
    public function __construct(
        private ProductSpecificationValidator $validator
    ) {}

    public function execute(CreateProductItemData $data): ProductItem
    {
        return DB::transaction(function () use ($data) {
            // Validar especificaciones según taxonomía antes de crear
            $tempItem = new ProductItem([
                'product_subcategory_id' => $data->productSubcategoryId,
                'specifications' => $data->specifications,
            ]);
            $tempItem->setRelation('productSubcategory', 
                ProductSubcategory::with('productSpecificationStructure', 'productCategory')
                    ->find($data->productSubcategoryId)
            );
            
            $validation = $this->validator->validateSpecifications($tempItem);
            if (!$validation->isValid()) {
                throw new ValidationException('Specification validation failed: ' . 
                    implode(', ', $validation->getErrors()));
            }
            
            // Crear el producto si la validación pasa
            $item = ProductItem::create([
                'project_id' => $data->projectId,
                'product_subcategory_id' => $data->productSubcategoryId,
                'name' => $data->name,
                'status' => $data->status ?? ProductItemStatus::DRAFT,
                'specifications' => $data->specifications,
                'quantity' => $data->quantity,
            ]);

            return $item;
        });
    }
}
```

**Services de Validación de Taxonomía**
```php
// app/Services/ProductSpecificationValidator.php
class ProductSpecificationValidator
{
    public function __construct(
        private CategorySpecificValidator $categoryValidator
    ) {}

    public function validateSpecifications(ProductItem $item): ValidationResult
    {
        $errors = [];
        
        // 1. Validación estructural
        $structuralErrors = $this->validateStructure($item);
        $errors = array_merge($errors, $structuralErrors);
        
        // 2. Validación específica por categoría
        $categoryErrors = $this->validateByCategory($item);
        $errors = array_merge($errors, $categoryErrors);
        
        // 3. Validación de integridad
        $integrityErrors = $this->validateDataIntegrity($item);
        $errors = array_merge($errors, $integrityErrors);
        
        return new ValidationResult($errors);
    }
    
    private function validateStructure(ProductItem $item): array
    {
        $errors = [];
        $structure = $item->productSubcategory->productSpecificationStructure;
        
        if (!$structure) {
            return $errors; // No structure defined, allow all
        }
        
        // Verificar campos obligatorios
        foreach ($structure->required_attributes as $required) {
            if (empty($item->specifications[$required])) {
                $errors[] = "Required field '{$required}' is missing";
            }
        }
        
        return $errors;
    }
    
    private function validateByCategory(ProductItem $item): array
    {
        $category = $item->productSubcategory->productCategory->name;
        
        return match($category) {
            'Merchandising' => $this->categoryValidator->validateMerchandising($item->specifications),
            'Material PDV' => $this->categoryValidator->validatePDV($item->specifications),
            'Textiles' => $this->categoryValidator->validateTextiles($item->specifications),
            default => []
        };
    }
}

// app/Services/CategorySpecificValidator.php
class CategorySpecificValidator
{
    public function validateMerchandising(array $specs): array
    {
        $errors = [];
        
        // Validar capacidad de volumen para artículos de beber
        if (isset($specs['capacity_volume']) && $specs['capacity_volume'] <= 0) {
            $errors[] = 'Capacity volume must be greater than 0';
        }
        
        // Validar certificaciones para productos de contacto alimentario
        if (isset($specs['food_contact_product']) && $specs['food_contact_product']) {
            if (empty($specs['food_grade_certification'])) {
                $errors[] = 'Food grade certification required for food contact products';
            }
        }
        
        return $errors;
    }
    
    public function validateTextiles(array $specs): array
    {
        $errors = [];
        
        // Validar GSM para textiles
        if (isset($specs['gsm_value']) && ($specs['gsm_value'] < 80 || $specs['gsm_value'] > 600)) {
            $errors[] = 'GSM value must be between 80 and 600';
        }
        
        // Validar tallas para prendas
        if (isset($specs['garment_construction']) && empty($specs['available_sizes'])) {
            $errors[] = 'Available sizes required for garments';
        }
        
        return $errors;
    }
    
    public function validatePDV(array $specs): array
    {
        $errors = [];
        
        // Validar dimensiones plegadas para displays plegables
        if (isset($specs['structural_design']) && $specs['structural_design'] === 'foldable') {
            if (empty($specs['folded_collapsed_dimensions'])) {
                $errors[] = 'Folded dimensions required for foldable displays';
            }
        }
        
        return $errors;
    }
}
```

### 5.8.3 Integración con ProductItem Model

```php
// Extensions en app/Models/ProductItem.php
class ProductItem extends Model
{
    // ... existing code ...
    
    public function productSubcategory(): BelongsTo
    {
        return $this->belongsTo(ProductSubcategory::class);
    }
    
    /**
     * Validar especificaciones completas según taxonomía.
     */
    public function hasCompleteSpecifications(): bool
    {
        if (!$this->relationLoaded('productSubcategory.productSpecificationStructure')) {
            $this->load('productSubcategory.productSpecificationStructure');
        }
        
        $structure = $this->productSubcategory->productSpecificationStructure;
        if (!$structure || empty($structure->required_attributes)) {
            return true; // No requirements defined
        }

        // Validar campos obligatorios según taxonomía
        foreach ($structure->required_attributes as $attribute) {
            if (empty($this->specifications[$attribute])) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * Validar especificaciones según reglas de negocio.
     */
    public function validateSpecifications(): ValidationResult
    {
        $validator = app(ProductSpecificationValidator::class);
        return $validator->validateSpecifications($this);
    }
    
    /**
     * Obtener plantilla de especificaciones para subcategoría.
     */
    public function getSpecificationTemplate(): array
    {
        $structure = $this->productSubcategory->productSpecificationStructure;
        
        return [
            'required' => $structure->required_attributes ?? [],
            'optional' => $structure->optional_attributes ?? [],
            'rules' => $structure->validation_rules ?? [],
            'category_rules' => $structure->category_specific_rules ?? [],
        ];
    }
}
```

### 5.8.4 DTOs con Validación de Taxonomía

```php
// Extension en app/Data/ProductItem/CreateProductItemData.php
class CreateProductItemData extends Data
{
    // ... existing code ...
    
    /**
     * Validar especificaciones contra estructura de subcategoría.
     */
    public function validateAgainstStructure(ProductSubcategory $subcategory): array
    {
        $structure = $subcategory->productSpecificationStructure;
        $errors = [];
        
        if (!$structure) {
            return $errors; // No structure defined, allow all
        }
        
        // Verificar campos obligatorios
        foreach ($structure->required_attributes as $required) {
            if (empty($this->specifications[$required])) {
                $errors[] = "Required field '{$required}' is missing";
            }
        }
        
        return $errors;
    }
}
```

---

## 5.9 Dominio de Logística - ImportShipmentRecord

### 5.8.1 Arquitectura CRUD para Logística

El dominio de logística maneja la consolidación y seguimiento de envíos internacionales usando el mismo patrón CRUD simplificado:

```mermaid
graph TD
    subgraph "Logística Domain"
        A[ImportShipmentRecord] --> B[ProductItem Consolidation]
        A --> C[Shipment Status Tracking]
        A --> D[Customs Documentation]
        A --> E[Cost Allocation]
    end
    
    subgraph "CRUD Operations"
        F[CreateShipmentAction] --> A
        G[UpdateShipmentStatusAction] --> A  
        H[ConsolidateItemsAction] --> A
        I[ProcessCustomsClearanceAction] --> A
    end
    
    subgraph "Supporting Services"
        J[ShipmentConsolidationService] --> B
        K[CustomsDocumentationService] --> D
        L[LogisticsCostCalculatorService] --> E
    end
```

### 5.8.2 Implementación de Componentes Logísticos

**Models y Relaciones**
```php
// app/Models/ImportShipmentRecord.php
class ImportShipmentRecord extends Model
{
    protected $fillable = [
        'shipment_number',
        'status',
        'origin_port',
        'destination_port',
        'departure_date',
        'arrival_date',
        'customs_reference',
        'total_weight',
        'total_volume',
        'estimated_cost',
        'actual_cost',
    ];

    protected $casts = [
        'departure_date' => 'date',
        'arrival_date' => 'date',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'status' => ImportShipmentStatus::class,
    ];

    // Relación con ProductItems consolidados
    public function productItems(): BelongsToMany
    {
        return $this->belongsToMany(ProductItem::class, 'shipment_items')
                    ->withPivot('quantity', 'weight', 'volume');
    }
}

// app/Enums/ImportShipmentStatus.php
enum ImportShipmentStatus: string
{
    case PLANNING = 'PLANNING';
    case PICKUP_SCHEDULED = 'PICKUP_SCHEDULED';
    case IN_TRANSIT = 'IN_TRANSIT';
    case ARRIVED_PORT = 'ARRIVED_PORT';
    case CUSTOMS_CLEARANCE = 'CUSTOMS_CLEARANCE';
    case CLEARED = 'CLEARED';
    case DOMESTIC_DELIVERY = 'DOMESTIC_DELIVERY';
    case DELIVERED = 'DELIVERED';
}
```

**Actions para Operaciones Logísticas**
```php
// app/Actions/Logistics/ConsolidateItemsAction.php
class ConsolidateItemsAction
{
    public function __construct(
        private ShipmentConsolidationService $consolidationService
    ) {}

    public function execute(ConsolidateItemsData $data): ImportShipmentRecord
    {
        $shipment = ImportShipmentRecord::create([
            'shipment_number' => $this->generateShipmentNumber(),
            'status' => ImportShipmentStatus::PLANNING,
            'origin_port' => $data->originPort,
            'destination_port' => $data->destinationPort,
        ]);

        // Consolidar ProductItems
        foreach ($data->productItemIds as $itemId) {
            $item = ProductItem::find($itemId);
            
            // Validar que item esté listo para envío
            if (!$item->status->canBeShipped()) {
                throw new InvalidShipmentConsolidationException(
                    "Item {$item->id} not ready for shipment"
                );
            }

            $shipment->productItems()->attach($itemId, [
                'quantity' => $item->quantity,
                'weight' => $item->estimated_weight,
                'volume' => $item->estimated_volume,
            ]);

            // Actualizar estado de ProductItem automáticamente
            $item->update(['status' => ProductItemStatus::INTERNATIONAL_TRANSIT]);
        }

        // Calcular totales del envío
        $this->consolidationService->calculateShipmentTotals($shipment);

        Log::info('Shipment consolidated', [
            'shipment_id' => $shipment->id,
            'item_count' => count($data->productItemIds),
            'total_weight' => $shipment->total_weight,
        ]);

        return $shipment;
    }
}
```

**Services de Logística**
```php
// app/Services/ShipmentConsolidationService.php
class ShipmentConsolidationService
{
    public function calculateShipmentTotals(ImportShipmentRecord $shipment): void
    {
        $totals = $shipment->productItems->reduce(function ($carry, $item) {
            $pivot = $item->pivot;
            return [
                'weight' => $carry['weight'] + $pivot->weight,
                'volume' => $carry['volume'] + $pivot->volume,
            ];
        }, ['weight' => 0, 'volume' => 0]);

        $shipment->update([
            'total_weight' => $totals['weight'],
            'total_volume' => $totals['volume'],
            'estimated_cost' => $this->calculateLogisticsCost($totals),
        ]);
    }

    public function suggestOptimalConsolidation(Collection $availableItems): array
    {
        // Algoritmo de optimización simple por peso/volumen
        return $availableItems
            ->filter(fn($item) => $item->status->canBeShipped())
            ->groupBy(fn($item) => $item->project->customer_id) // Agrupar por cliente
            ->map(fn($group) => $this->optimizeGroupConsolidation($group))
            ->flatten(1)
            ->toArray();
    }
}
```

### 5.8.3 Integración con ProductItem Status

El dominio de logística se integra perfectamente con el ciclo de vida de ProductItems:

```php
// Extensión en ProductItemStatus enum
public function canBeShipped(): bool
{
    return in_array($this, [
        self::PENDING_SHIPMENT,
        self::READY_FOR_SHIPMENT, // Si se añade este estado
    ]);
}

public function isInLogistics(): bool
{
    return in_array($this, [
        self::INTERNATIONAL_TRANSIT,
        self::CUSTOMS_CLEARANCE,
        self::DOMESTIC_TRANSIT,
        self::DELIVERED
    ]);
}
```

---

## 5.9 Dominio de Finanzas - Gestión de Costos y FX

### 5.9.1 Arquitectura CRUD para Finanzas

El dominio financiero maneja costos reales, contratos de cambio y análisis de rentabilidad:

```mermaid
graph TD
    subgraph "Finance Domain"
        A[ActualCost] --> B[Cost Tracking]
        C[ForwardExchangeContract] --> D[FX Risk Management]
        E[ProfitabilityAnalysis] --> F[Margin Calculation]
    end
    
    subgraph "CRUD Operations"
        G[RecordActualCostAction] --> A
        H[CreateFECAction] --> C
        I[UpdateCostAllocationAction] --> A
        J[CalculateProfitabilityAction] --> E
    end
    
    subgraph "Supporting Services"
        K[CostAllocationService] --> B
        L[FXRateService] --> D
        M[ProfitabilityCalculatorService] --> F
    end
```

### 5.9.2 Implementación de Componentes Financieros

**Models Financieros**
```php
// app/Models/ActualCost.php
class ActualCost extends Model
{
    protected $fillable = [
        'project_id',
        'product_item_id',
        'cost_category',
        'description',
        'amount',
        'currency',
        'exchange_rate',
        'amount_usd',
        'incurred_date',
        'supplier_invoice_ref',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'exchange_rate' => 'decimal:4',
        'amount_usd' => 'decimal:2',
        'incurred_date' => 'date',
        'cost_category' => CostCategory::class,
    ];

    public function project(): BelongsTo { return $this->belongsTo(Project::class); }
    public function productItem(): BelongsTo { return $this->belongsTo(ProductItem::class); }
}

// app/Models/ForwardExchangeContract.php
class ForwardExchangeContract extends Model
{
    protected $fillable = [
        'contract_reference',
        'currency_pair',
        'contract_rate',
        'notional_amount',
        'settlement_date',
        'bank_reference',
        'status',
        'realized_pnl',
    ];

    protected $casts = [
        'contract_rate' => 'decimal:4',
        'notional_amount' => 'decimal:2',
        'settlement_date' => 'date',
        'realized_pnl' => 'decimal:2',
        'status' => FECStatus::class,
    ];

    public function projects(): BelongsToMany
    {
        return $this->belongsToMany(Project::class, 'project_fec_allocations')
                    ->withPivot('allocated_amount', 'hedge_ratio');
    }
}
```

**Actions Financieras**
```php
// app/Actions/Finance/RecordActualCostAction.php
class RecordActualCostAction
{
    public function __construct(
        private FXRateService $fxRateService,
        private CostAllocationService $allocationService
    ) {}

    public function execute(RecordActualCostData $data): ActualCost
    {
        // Obtener tasa de cambio actual o usar la proporcionada
        $exchangeRate = $data->exchangeRate ?? 
                       $this->fxRateService->getCurrentRate($data->currency, 'USD');

        $cost = ActualCost::create([
            'project_id' => $data->projectId,
            'product_item_id' => $data->productItemId,
            'cost_category' => $data->costCategory,
            'description' => $data->description,
            'amount' => $data->amount,
            'currency' => $data->currency,
            'exchange_rate' => $exchangeRate,
            'amount_usd' => $data->amount * $exchangeRate,
            'incurred_date' => $data->incurredDate,
            'supplier_invoice_ref' => $data->invoiceReference,
        ]);

        // Actualizar análisis de rentabilidad del proyecto
        $this->allocationService->updateProjectProfitability($cost->project);

        // Alertas por desviación de costos
        if ($this->detectsCostDeviation($cost)) {
            event(new CostDeviationDetected($cost));
        }

        Log::info('Actual cost recorded', [
            'cost_id' => $cost->id,
            'project_id' => $cost->project_id,
            'amount_usd' => $cost->amount_usd,
            'category' => $cost->cost_category->value,
        ]);

        return $cost;
    }
}

// app/Actions/Finance/CreateFECAction.php
class CreateFECAction
{
    public function execute(CreateFECData $data): ForwardExchangeContract
    {
        $fec = ForwardExchangeContract::create([
            'contract_reference' => $this->generateFECReference(),
            'currency_pair' => $data->currencyPair,
            'contract_rate' => $data->contractRate,
            'notional_amount' => $data->notionalAmount,
            'settlement_date' => $data->settlementDate,
            'bank_reference' => $data->bankReference,
            'status' => FECStatus::ACTIVE,
        ]);

        // Asignar a proyectos si se especifican
        if (!empty($data->projectAllocations)) {
            foreach ($data->projectAllocations as $allocation) {
                $fec->projects()->attach($allocation['project_id'], [
                    'allocated_amount' => $allocation['amount'],
                    'hedge_ratio' => $allocation['hedge_ratio'],
                ]);
            }
        }

        return $fec;
    }
}
```

**Services Financieros**
```php
// app/Services/ProfitabilityCalculatorService.php
class ProfitabilityCalculatorService
{
    public function calculateProjectMargin(Project $project): array
    {
        // Ingresos del proyecto (cotizaciones confirmadas)
        $totalRevenue = $project->productItems
            ->where('status', '>=', ProductItemStatus::CLIENT_CONFIRMED)
            ->sum('quoted_price_usd');

        // Costos reales incurridos
        $actualCosts = $project->actualCosts->sum('amount_usd');

        // Costos estimados para items no completados
        $estimatedRemainingCosts = $this->calculateEstimatedRemainingCosts($project);

        $totalCosts = $actualCosts + $estimatedRemainingCosts;
        $grossMargin = $totalRevenue - $totalCosts;
        $marginPercentage = $totalRevenue > 0 ? ($grossMargin / $totalRevenue) * 100 : 0;

        return [
            'total_revenue_usd' => $totalRevenue,
            'actual_costs_usd' => $actualCosts,
            'estimated_remaining_costs_usd' => $estimatedRemainingCosts,
            'total_costs_usd' => $totalCosts,
            'gross_margin_usd' => $grossMargin,
            'margin_percentage' => round($marginPercentage, 2),
            'cost_breakdown' => $this->getCostBreakdown($project),
            'fx_exposure' => $this->calculateFXExposure($project),
        ];
    }

    private function getCostBreakdown(Project $project): array
    {
        return $project->actualCosts
            ->groupBy('cost_category')
            ->map(fn($costs) => [
                'total_usd' => $costs->sum('amount_usd'),
                'count' => $costs->count(),
                'percentage' => 0, // Calculado después
            ])
            ->toArray();
    }
}
```

### 5.9.3 Integración con Version Tracking

Los eventos financieros también se registran en el sistema de versiones:

```php
// Extension del ProductItemVersionService
private function generateCostImpactSummary(array $previous, array $new): string
{
    $costChange = $new['total_cost_usd'] - $previous['total_cost_usd'];
    $marginImpact = $new['margin_percentage'] - $previous['margin_percentage'];
    
    return "Impacto en costos: " . 
           ($costChange >= 0 ? "+$" : "-$") . abs($costChange) . 
           " USD, Margen: " . 
           ($marginImpact >= 0 ? "+" : "") . round($marginImpact, 1) . "%";
}
```

### 5.9.4 Dashboards Financieros

```php
// app/Services/FinancialDashboardService.php  
class FinancialDashboardService
{
    public function getExecutiveSummary(): array
    {
        return [
            'active_projects_count' => Project::active()->count(),
            'total_pipeline_value_usd' => $this->calculatePipelineValue(),
            'ytd_margin_percentage' => $this->calculateYTDMargin(),
            'fx_exposure_usd' => $this->calculateTotalFXExposure(),
            'top_cost_categories' => $this->getTopCostCategories(),
            'projects_at_risk' => $this->getProjectsAtRisk(),
        ];
    }
}
```

---

Estas extensiones de dominio mantienen la **filosofía CRUD simplificada** mientras proporcionan la funcionalidad empresarial completa necesaria para logística y finanzas. La integración con el sistema de version tracking asegura que todos los cambios financieros y logísticos importantes sean rastreables para auditoría y análisis.