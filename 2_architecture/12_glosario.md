# ⚠️ DOCUMENTO MOVIDO - Glosario y Nomenclatura

> **🚀 Nueva Ubicación**: Este documento ha sido **consolidado y mejorado** en la nueva estructura de nomenclatura.

---

## 🎯 Acceso Rápido a la Nueva Documentación

### **Documento Principal** (reemplaza este archivo):
**[00_NOMENCLATURA_GUIA_MAESTRA.md](naming/00_NOMENCLATURA_GUIA_MAESTRA.md)**
- ✅ Todo el contenido de este documento + mucho más
- ✅ Ejemplos prácticos con código
- ✅ Convenciones completas por tipo de artefacto
- ✅ FAQ y casos especiales

### **Consulta Diaria** (para desarrolladores):
**[01_guia_rapida_desarrollador.md](naming/01_guia_rapida_desarrollador.md)**
- ⚡ Cheat sheet con patrones más usados
- ⚡ Conversión rápida de conceptos
- ⚡ Templates y ejemplos inmediatos

### **Casos Prácticos**:
**[02_ejemplos_practicos.md](naming/02_ejemplos_practicos.md)**
- 💡 Ejemplos completos con código real
- 💡 Escenarios típicos de desarrollo
- 💡 Patrones identificados

---

## 🔄 Migración Automática

**Para actualizar tus bookmarks y referencias:**

1. **Mapeo de entidades** → `naming/00_NOMENCLATURA_GUIA_MAESTRA.md` (sección "Conversión Rápida")
2. **Estados de ProductItem** → `naming/01_guia_rapida_desarrollador.md` (sección "Estados")
3. **Convenciones de código** → `naming/00_NOMENCLATURA_GUIA_MAESTRA.md` (sección "Convenciones")

---

## 📦 Contenido Original

El contenido original de este documento se mantiene en:
**[naming/legacy/glosario_actual.md](naming/legacy/glosario_actual.md)**

---

## ✨ Beneficios de la Nueva Estructura

- 🎯 **Un solo punto de entrada** vs información dispersa
- ⚡ **Consulta en 30 segundos** vs 5-10 minutos
- 💡 **Ejemplos prácticos** con código real
- 🔧 **Herramientas de validación** automática
- 📚 **Onboarding más rápido** para nuevos desarrolladores

---

**¿Necesitas ayuda con la migración?** Consulta la [guía de migración](naming/04_guia_migracion.md).
