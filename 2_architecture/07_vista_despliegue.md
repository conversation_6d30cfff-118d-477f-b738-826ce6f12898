# 7. Vista de Despliegue (Deployment View)

> **Propósito:** Detalla la infraestructura técnica sobre la cual se ejecuta el sistema y cómo los componentes de software se mapean a los elementos de hardware.

---

## 7.1. Arquitectura de Producción

El despliegue se simplifica a servidores de aplicación monolíticos que sirven tanto la lógica de backend como la interfaz de usuario, detrás de un balanceador de carga para asegurar alta disponibilidad y escalabilidad horizontal.

```mermaid
graph TB
    subgraph "Balanceador de Carga"
        LB[Nginx Load Balancer]
    end
    
    subgraph "Servidores de Aplicación (Auto-escalables)"
        APP1[App Server 1<br/>Nginx + PHP-FPM]
        APP2[App Server 2<br/>Nginx + PHP-FPM]
    end
    
    subgraph "Cluster de Base de Datos (Alta Disponibilidad)"
        DB_MASTER[(PostgreSQL Master)]
        DB_SLAVE[(PostgreSQL Read Replica)]
    end
    
    subgraph "Capa de Caching y Colas"
        REDIS[Redis Cluster]
    end
    
    subgraph "Almacenamiento de Archivos"
        S3[Almacenamiento Compatible con S3]
    end
    
    LB --> APP1
    LB --> APP2
    APP1 --> DB_MASTER
    APP2 --> DB_MASTER
    APP1 --> DB_SLAVE
    APP2 --> DB_SLAVE
    APP1 --> REDIS
    APP2 --> REDIS
    APP1 --> S3
    APP2 --> S3
```

## 7.2. Componentes de la Infraestructura

-   **Balanceador de Carga (Nginx):** Distribuye el tráfico entrante entre los servidores de aplicación para evitar sobrecargas y asegurar que el servicio no se interrumpa si un servidor falla.
-   **Servidores de Aplicación (Nginx + PHP-FPM):** Contienen la aplicación monolítica de Laravel. Pueden ser escalados horizontalmente (añadiendo más servidores) si la carga aumenta.
-   **Cluster de Base de Datos (PostgreSQL):** Un clúster Maestro-Esclavo. Todas las escrituras van al Maestro, mientras que las lecturas pueden distribuirse a las réplicas para mejorar el rendimiento.
-   **Cluster de Redis:** Proporciona un servicio de caching y colas de alta velocidad y disponibilidad para la aplicación.
-   **Almacenamiento S3:** Un servicio de almacenamiento de objetos para todos los archivos subidos por los usuarios y los artefactos generados por el sistema, asegurando durabilidad y escalabilidad.

---

## 7.3. Proceso de Optimización en Despliegue

Como parte de cada despliegue a producción, se deben ejecutar los siguientes comandos para mejorar drásticamente el rendimiento de la aplicación, cacheando en un único archivo los elementos que no cambian entre peticiones:

-   **Cacheo de Rutas:** `php artisan route:cache`
-   **Cacheo de Configuración:** `php artisan config:cache`
-   **Cacheo de Vistas:** `php artisan view:cache`