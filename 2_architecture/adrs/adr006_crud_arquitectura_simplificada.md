# ADR-006: Arquitectura CRUD Simplificada con Version Tracking

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura
- **Supersede:** ADR-006 original (Event Sourcing como Backbone)

## Contexto

PromoSmart inicialmente consideró Event Sourcing debido a lo que parecía ser un requisito de "auditoría perfecta". Sin embargo, después de clarificar con el negocio, el verdadero requisito es mucho más simple:

**Requisito Real Clarificado**: "Solo necesito lista de versiones de productos derivados de re-cotizaciones"

### Análisis del Requisito Real vs Percibido

| Aspecto | Requisito Percibido | Requisito Real |
|---------|--------------------|-----------------| 
| **Auditoría** | Completa + inmutable | Lista de versiones principales |
| **Reconstrucción** | Estado exacto en cualquier momento | NO necesario |
| **Tracking** | Todos los cambios | Solo cambios significativos |
| **Compliance** | Auditoría externa | Tracking interno simple |
| **Complejidad** | Event Sourcing (3-4 semanas) | CRUD + tabla adicional (3-4 días) |

## Decisión

Adoptar **arquitectura CRUD simplificada** con **version tracking selectivo** para satisfacer el verdadero requisito de negocio sin over-engineering.

### Principios de la Decisión

1. **Requisito-Driven**: Implementar exactamente lo que se necesita, nada más
2. **Simplicidad Primero**: CRUD directo con tabla adicional para versioning
3. **Performance Optimizada**: Sin overhead de Event Sourcing
4. **Team Productivity**: Cualquier dev Laravel puede trabajar inmediatamente

### Arquitectura Resultante

```
UI Layer (Filament)
     ↓
Application Layer (Actions + DTOs)
     ↓ 
Domain Layer (Standard Eloquent Models)
     ↓
Infrastructure Layer (ProductItemVersionService)
     ↓
Database (Standard Tables + product_item_versions)
```

**Flujo Simplificado**:
1. **DTO** valida entrada tipada
2. **Action** ejecuta CRUD operation
3. **ProductItemVersionService** crea version si cambio significativo
4. **Standard logging** para operaciones normales

## Implementación

### Schema de Base de Datos

```sql
-- Tabla principal sin cambios
CREATE TABLE product_items (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    specifications JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Nueva tabla para version tracking
CREATE TABLE product_item_versions (
    id BIGINT PRIMARY KEY,
    product_item_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    change_type VARCHAR(50) NOT NULL, -- 'creation', 'requote', 'spec_update'
    changes_summary TEXT,
    previous_data JSON,
    new_data JSON,
    changed_by BIGINT,
    changed_at TIMESTAMP,
    created_at TIMESTAMP
);
```

### RequoteProductItemAction (Ejemplo Core)

```php
class RequoteProductItemAction
{
    public function __construct(
        private ProductItemVersionService $versionService
    ) {}
    
    public function execute(RequoteProductItemData $data): ProductItem
    {
        $item = $data->productItem;
        $previousData = [
            'specifications' => $item->specifications,
            'status' => $item->status->value,
            'price' => $data->previousPrice,
        ];
        
        // CRUD simple update
        $item->update([
            'specifications' => $data->newSpecifications,
            'status' => $data->newStatus,
        ]);
        
        // Version tracking para cambios significativos
        $this->versionService->createVersion(
            item: $item,
            changeType: 'requote',
            previousData: $previousData,
            newData: [
                'specifications' => $data->newSpecifications,
                'status' => $data->newStatus->value,
                'price' => $data->newPrice,
            ],
            summary: $data->reason
        );
        
        return $item;
    }
}
```

### ProductItemVersionService

```php
class ProductItemVersionService
{
    public function createVersion(
        ProductItem $item,
        string $changeType,
        array $previousData,
        array $newData,
        ?string $summary = null
    ): ProductItemVersion {
        return ProductItemVersion::create([
            'product_item_id' => $item->id,
            'version_number' => $this->getNextVersionNumber($item),
            'change_type' => $changeType,
            'changes_summary' => $summary ?? $this->generateSummary($changeType, $previousData, $newData),
            'previous_data' => $previousData,
            'new_data' => $newData,
            'changed_by' => auth()->id(),
            'changed_at' => now(),
        ]);
    }
    
    public function getVersionHistory(ProductItem $item): Collection
    {
        return ProductItemVersion::where('product_item_id', $item->id)
            ->with('changedBy:id,name')
            ->orderBy('version_number', 'desc')
            ->get()
            ->map(fn($version) => [
                'version' => $version->version_number,
                'type' => $version->change_type,
                'summary' => $version->changes_summary,
                'changed_by' => $version->changedBy->name,
                'changed_at' => $version->changed_at->format('d/m/Y H:i'),
            ]);
    }
}
```

## Patrones Mantenidos vs Eliminados

### ✅ Mantenemos (Útiles para CRUD)

**Enums con Métodos** - Muy útil para UI logic (según ADR-008 y ADR-010)
```php
enum ProductItemStatus: string 
{
    case Draft = 'draft';
    case QuotedToCustomer = 'quoted_to_customer';
    
    public function getLabel(): string { /* ... */ }
    public function getColor(): string { /* ... */ }
    // ❌ NO: public function canTransitionTo() - Ver StateTransitionService
}
```

**DTOs Simplificados** - Útil para tipado fuerte + validación
```php
class RequoteProductItemData extends Data
{
    public function __construct(
        public ProductItem $productItem,
        public array $newSpecifications,
        public ProductItemStatus $newStatus,
        public string $reason
    ) {}
}
```

**Actions** - Útil para orquestación de casos de uso (usa StateTransitionService según ADR-010)
```php
class UpdateProductItemAction
{
    public function __construct(
        private StateTransitionService $stateTransitionService
    ) {}
    
    public function execute(UpdateProductItemData $data): ProductItem
    {
        // Validation usando StateTransitionService
        if (!$this->stateTransitionService->isTransitionAllowed($item, $data->status, auth()->user())) {
            throw new ValidationException("Invalid transition");
        }
        
        // CRUD update + version tracking si necesario
        // ...
    }
}
```

### ❌ Eliminamos (Overkill)

- **Spatie Model States** → Reemplazado por Enum UI methods + StateTransitionService (misma funcionalidad, menos complejidad)
- **Event Sourcing completo** → No necesario para requisito real
- **Aggregates/Events/Projectors/Reactors** → Overkill masivo

## Consecuencias

### Positivas
- ✅ **Development Speed**: 10x más rápido que Event Sourcing
- ✅ **Team Productivity**: Cualquier dev Laravel puede trabajar inmediatamente  
- ✅ **Performance**: Sin overhead, queries directos optimizados
- ✅ **Debugging**: Path simple, troubleshooting fácil
- ✅ **Maintenance**: Pocas moving parts, menos superficie para bugs
- ✅ **Satisface Requisito**: Lista de versiones exactamente como se necesita

### Limitaciones (Aceptables dado el requisito)
- ❌ **No reconstrucción histórica**: No puedes "time travel" a estado exacto (no necesario)
- ❌ **No business rules enforcement automático**: Validación manual en Actions (aceptable)
- ❌ **No side effects automáticos**: Notificaciones manuales (aceptable)
- ❌ **Limited audit trail**: Solo cambios "significativos" (suficiente para el requisito)

### Comparación de Complejidad

| Aspecto | Event Sourcing | CRUD + Version Tracking |
|---------|----------------|--------------------------|
| **Código** | ~2000 líneas | ~200 líneas |
| **Conceptos** | Aggregates, Events, Projectors, Reactors | 1 tabla adicional + Service |
| **Learning curve** | Alta (Event Sourcing) | Baja (CRUD + 1 tabla) |
| **Time to market** | 3-4 semanas | 3-4 días |
| **Debugging** | Complejo (async, múltiple components) | Simple (direct SQL) |

## Plan de Implementación

### Fase 1: Fundación (1-2 días)
1. Crear migration para `product_item_versions`
2. Crear `ProductItemVersion` model  
3. Crear `ProductItemVersionService`

### Fase 2: Actions con Version Tracking (2-3 días)
1. Actualizar Actions existentes con version tracking
2. Crear `RequoteProductItemAction` específico
3. Añadir UI components para mostrar versiones

### Fase 3: Testing y Refinement (1 día)
1. Testing de versioning
2. Refinements en UI
3. Documentation

**Total: ~1 semana** vs 3-4 semanas para Event Sourcing

## Referencias

- **[Arquitectura Integrada Simplificada](../00_arquitectura_integrada.md)** - Especificación completa
- **[Recomendación Final](../../recomendacion_final.md)** - Análisis detallado de la decisión
- **[Explicación Técnica](../../explicacion.md)** - Comparación Event Sourcing vs CRUD
- **[Bloques de Construcción Simplificados](../05_bloques_construccion.md)** - Estructura de componentes