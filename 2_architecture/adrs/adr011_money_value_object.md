# ADR-011: Adopción del Patrón Money como Value Object

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura

## Contexto

El sistema maneja múltiples valores monetarios en diferentes divisas. El uso de tipos primitivos (como `float` para el monto y `string` para la moneda) es propenso a errores de precisión, errores de cálculo (ej. sumar accidentalmente USD con CLP) y dispersa la lógica de negocio relacionada con el dinero (formato, conversión) en múltiples lugares.

## Decisión

Adoptar el **patrón de Value Object `Money`** para encapsular todos los valores monetarios. Este enfoque trata al dinero como un concepto de primera clase, combinando el monto y la divisa en un único objeto inmutable.

La implementación se basará en:

1.  **Una clase `Money`:** Un objeto de valor que contiene el monto (como un entero, en la unidad más pequeña, ej. centavos) y un `Enum` de `Currency`.
2.  **Un `MoneyCast` personalizado:** Una clase que implementa la interfaz `CastsAttributes` de Laravel para convertir de forma transparente entre el objeto `Money` en el código y las dos columnas (`_amount`, `_currency`) en la base de datos.

## Consecuencias

**Positivas:**
- (+) **Seguridad de Tipos:** Previene operaciones matemáticas entre monedas diferentes, eliminando una clase entera de errores.
- (+) **Encapsulación:** Centraliza toda la lógica de formato, comparación y aritmética monetaria en un solo lugar.
- (+) **Claridad del Código:** El código se vuelve más explícito y legible (ej. `$price->add($tax)`).
- (+) **Precisión:** Evita los problemas de redondeo de los números de punto flotante al operar con enteros.

**Negativas:**
- (-) **Configuración Inicial:** Requiere la creación de las clases `Money` y `MoneyCast`.
- (-) **Abstracción:** Los desarrolladores deben familiarizarse con el uso del objeto `Money` en lugar de tipos primitivos.
