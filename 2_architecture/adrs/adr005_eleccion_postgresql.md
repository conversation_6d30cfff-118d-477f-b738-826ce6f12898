# ADR-005: Elección de PostgreSQL como Base de Datos

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura

## Contexto
La elección de la base de datos es una de las decisiones de infraestructura más críticas para el sistema. Los requerimientos del dominio de PromoSmart exigen una base de datos relacional que no solo sea robusta y transaccional, sino que también ofrezca un soporte avanzado para tipos de datos semi-estructurados como JSON. Esta capacidad es fundamental para manejar la naturaleza variable y compleja de las especificaciones de los productos. 

Adicionalmente, la arquitectura debe ser previsor y sentar las bases para futuras funcionalidades, como analíticas complejas o capacidades geoespaciales.

## Decisión
Seleccionar **PostgreSQL 12+** como el sistema de gestión de base de datos relacional principal para el proyecto.

## Consecuencias

**Positivas:**
- (+) **Soporte JSON/JSONB Superior:** PostgreSQL ofrece un conjunto de operadores y capacidades de indexación para campos JSONB mucho más potente que otras bases de datos relacionales, lo cual es crítico para las especificaciones de producto.
- (+) **Mayor Adherencia a Estándares SQL:** Es conocido por ser más estricto con el estándar SQL, lo que reduce comportamientos inesperados y asegura la portabilidad del conocimiento.
- (+) **Extensibilidad:** Proporciona un ecosistema de extensiones maduro y robusto (ej. PostGIS), preparando al sistema para futuras funcionalidades sin necesidad de cambiar de tecnología.
- (+) **Rendimiento en Consultas Complejas:** Generalmente muestra un mejor rendimiento para consultas complejas, joins y grandes volúmenes de datos, lo cual es beneficioso para la futura capa de reportería.

**Negativas:**
- (-) **Diferencias de Sintaxis Menores:** El equipo debe estar al tanto de las pequeñas diferencias en la sintaxis de SQL en comparación con otros motores como MySQL.