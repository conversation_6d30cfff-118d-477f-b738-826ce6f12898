# ADR-012: Adopción de Patrones de Interfaz de Usuario con Filament v4

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura

## Contexto

Tras la implementación inicial de la interfaz de administración con Filament, se ha identificado la necesidad de estandarizar el uso de las nuevas características introducidas en la versión 4. El objetivo es establecer un conjunto de patrones de diseño y arquitectura consistentes para todos los `Resources` del sistema. Esto garantizará una experiencia de usuario homogénea, un rendimiento optimizado y una base de código más limpia y mantenible a medida que la aplicación crezca en complejidad.

## Decisión

Se decide adoptar formalmente los siguientes patrones y características de Filament v4 para el desarrollo de la interfaz de usuario del panel de administración:

1.  **Jerarquía con Recursos Anidados (Nested Resources):** Para relaciones de negocio estrictamente jerárquicas (como `Proyecto` -> `ProductItem`), se anidará la navegación del recurso hijo dentro del padre. Esto mejora la claridad contextual para el usuario a través de URLs y breadcrumbs coherentes.

2.  **Optimización de Formularios Reactivos:** Se utilizará la recarga parcial (`partiallyRenderAfterStateUpdated`) en los campos reactivos (`live()`) para minimizar las peticiones al servidor y actualizar solo los componentes necesarios del formulario. Esto mejora drásticamente la fluidez y el rendimiento percibido en formularios dinámicos.

3.  **Uso de Componentes de Formulario Avanzados:** Se estandariza el uso del `RichEditor` para todos los campos de texto largo que requieran formato (como descripciones), en lugar de `Textarea` simples, para mejorar la calidad de los datos ingresados.

4.  **Estandarización de la Interfaz de Tablas:**
    -   Las acciones a nivel de tabla (como "Crear") se ubicarán en la `toolbarActions()`.
    -   Se habilitará la reordenación de columnas (`reorderableColumns()`) por defecto para dar flexibilidad al usuario.

5.  **Refactorización de Resources Complejos:** Para `Resources` con una lógica de formulario o tabla extensa (más de ~150 líneas), la definición del schema se extraerá a clases dedicadas (ej. `ProductItemForm.php`) para mantener la clase del `Resource` limpia y enfocada en la configuración general.

## Consecuencias

**Positivas:**
- (+) **Consistencia en la UI/UX:** Todos los `Resources` seguirán las mismas convenciones, resultando en una experiencia de usuario predecible y coherente.
- (+) **Mejor Rendimiento Percibido:** Los formularios dinámicos se sentirán más rápidos y fluidos al evitar recargas completas de la página.
- (+) **Mantenibilidad del Código:** Las clases de los `Resources` se mantendrán limpias y legibles. La lógica compleja quedará encapsulada en clases específicas, facilitando su mantenimiento.
- (+) **Mejor Experiencia de Usuario:** La navegación será más intuitiva gracias a la anidación, y las tablas serán más flexibles gracias a la personalización de columnas.

**Negativas:**
- (-) **Mayor Abstracción:** Los desarrolladores deberán aprender y aplicar estos patrones específicos en lugar de solo usar las funcionalidades básicas de Filament.
- (-) **Posible Aumento de Archivos:** La refactorización de `Resources` complejos incrementará el número de archivos, lo que podría percibirse como un exceso de boilerplate para casos simples.
