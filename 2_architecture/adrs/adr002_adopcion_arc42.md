# ADR-002: Uso de arc42 como marco y estructura de documentación

- Estado: Aceptada
- Fecha: 2025-08-17
- Deciders: <PERSON>
- Authors: <AUTHORS>

## Contexto
Se requiere organizar la documentación técnica de forma coherente, mantenible y comunicable para múltiples interesados del proyecto PromoSmart. La especificación crece en amplitud (datos, normativa/SNA, seguridad, integraciones, operación, pruebas, performance) y profundidad (guías, políticas, decisiones).

**Fuerzas impulsoras:**
- Claridad y navegabilidad para stakeholders técnicos y de negocio
- Trazabilidad entre decisiones (ADRs), vistas de arquitectura y especificación detallada
- Alineamiento con prácticas reconocidas de documentación de arquitectura
- Necesidad de un enfoque pragmático que responda "Qué" y "Cómo" documentar la arquitectura

**Problema específico:**
Sin un marco estructurado, la documentación tiende a fragmentarse, creando inconsistencias, duplicidades y dificultades para el onboarding de nuevos miembros del equipo.

## Decisión
Adoptar **arc42** como marco integral de documentación de arquitectura, implementando su estructura completa de 12 secciones para organizar todo el conocimiento arquitectónico del sistema PromoSmart.

**Implementación específica:**
- Crear estructura plana de archivos dentro de `2_architecture/` basada en las 12 secciones de arc42


## Alcance y Estructura Detallada

### Marco arc42: 12 Secciones Fundamentales

arc42 es una plantilla pragmática para documentación de arquitectura de software que estructura el conocimiento arquitectónico en 12 secciones interconectadas. Cada sección tiene un propósito específico y responde preguntas clave sobre el sistema:

**Estructura de directorios implementada:**

```
/2_architecture/
├── 01_introduccion_objetivos.md       # Introducción y Objetivos
├── 02_restricciones.md                # Restricciones
├── 03_contexto_alcance.md             # Contexto y Alcance
├── 04_estrategia_solucion.md          # Estrategia de Solución
├── 05_bloques_construccion.md         # Vista de Bloques de Construcción
├── 06_vista_runtime.md                # Vista de Tiempo de Ejecución
├── 07_vista_despliegue.md             # Vista de Despliegue
├── 08_conceptos_transversales.md      # Conceptos Transversales
├── 09_decisiones_arquitectonicas.md   # Decisiones Arquitectónicas
├── 10_requisitos_calidad.md           # Requisitos de Calidad
├── 11_riesgos_deuda_tecnica.md        # Riesgos y Deuda Técnica
├── 12_glosario.md                     # Glosario
├── adrs/
│   ├── adr002_adopcion_arc42.md
│   └── adr003_politica_idioma.md
├── diagramas/
│   ├── contexto_sistema.puml
│   └── arquitectura_general.png
└── README.md
```

### Descripción Detallada de las Secciones arc42

#### 1. Introduction and Goals → Introducción y Objetivos (`01_introduccion_objetivos.md`)
**Propósito:** Establece el fundamento del proyecto y comunica la visión arquitectónica.
- **Contenido:** Descripción de requisitos del sistema PromoSmart, objetivos de calidad principales, stakeholders clave
- **Pregunta clave:** "¿Por qué existe este sistema y qué debe lograr?"
- **Audiencia:** Todos los stakeholders del proyecto

#### 2. Constraints → Restricciones (`02_restricciones.md`)
**Propósito:** Documenta limitaciones de diseño e implementación que afectan las decisiones arquitectónicas.
- **Contenido:** Restricciones organizacionales, técnicas, legales y de tiempo
- **Pregunta clave:** "¿Qué limitaciones debemos considerar en nuestras decisiones?"
- **Ejemplo:** Uso obligatorio de Laravel 12, restricciones de presupuesto, compliance con regulaciones chilenas

#### 3. Context and Scope → Contexto y Alcance (`03_contexto_alcance.md`)
**Propósito:** Define los límites del sistema y especifica interfaces externas.
- **Contenido:** Perspectiva de negocio y técnica del entorno del sistema
- **Pregunta clave:** "¿Qué está dentro y qué está fuera de nuestro sistema?"
- **Enfoque:** Diagramas de contexto mostrando PromoSmart y sus sistemas externos

#### 4. Solution Strategy → Estrategia de Solución (`04_estrategia_solucion.md`)
**Propósito:** Resume las decisiones arquitectónicas fundamentales y el enfoque de solución.
- **Contenido:** Decisiones tecnológicas clave, descomposición de alto nivel del sistema
- **Pregunta clave:** "¿Cuál es nuestro enfoque general para resolver el problema?"
- **Ejemplo:** Arquitectura monolítica modular con Laravel, uso de Filament para UI

#### 5. Building Block View → Vista de Bloques de Construcción (`05_bloques_construccion.md`)
**Propósito:** Proporciona descomposición estática jerárquica del sistema.
- **Contenido:** Estructura modular del código fuente, abstracción en múltiples niveles
- **Pregunta clave:** "¿Cómo está estructurado estáticamente nuestro sistema?"
- **Función especial:** Portal hacia vistas detalladas en `3_especificacion_tecnica/`

#### 6. Runtime View → Vista de Tiempo de Ejecución (`06_vista_runtime.md`)
**Propósito:** Ilustra el comportamiento dinámico del sistema.
- **Contenido:** Casos de uso críticos, interacciones entre componentes, flujos de trabajo
- **Pregunta clave:** "¿Cómo se comporta el sistema durante la ejecución?"
- **Ejemplo:** Flujos de especificación de productos, procesos de quotación

#### 7. Deployment View → Vista de Despliegue (`07_vista_despliegue.md`)
**Propósito:** Detalla la infraestructura técnica y mapeo de componentes.
- **Contenido:** Elementos de infraestructura, mapeo software-hardware
- **Pregunta clave:** "¿Dónde y cómo se ejecuta nuestro sistema?"
- **Ejemplo:** Docker containers, VPS, Traefik como reverse proxy

#### 8. Crosscutting Concepts → Conceptos Transversales (`08_conceptos_transversales.md`)
**Propósito:** Explica regulaciones y patrones que afectan múltiples partes del sistema.
- **Contenido:** Modelos de dominio, patrones arquitectónicos, conceptos de seguridad
- **Pregunta clave:** "¿Qué conceptos aplican a todo el sistema?"
- **Ejemplo:** Manejo de monedas, auditabilidad, gestión de estados

#### 9. Architectural Decisions → Decisiones Arquitectónicas (`09_decisiones_arquitectonicas.md`)
**Propósito:** Documenta decisiones arquitectónicas críticas y riesgosas.
- **Contenido:** ADRs con justificaciones, alternativas consideradas, consecuencias
- **Pregunta clave:** "¿Por qué tomamos estas decisiones arquitectónicas específicas?"
- **Integración:** Referencias al directorio `adrs/` existente

#### 10. Quality Requirements → Requisitos de Calidad (`10_requisitos_calidad.md`)
**Propósito:** Define escenarios de calidad y proporciona árbol de calidad.
- **Contenido:** Atributos de calidad específicos, métricas, escenarios de prueba
- **Pregunta clave:** "¿Qué tan bien debe funcionar nuestro sistema?"
- **Ejemplo:** Performance, usabilidad, mantenibilidad, escalabilidad

#### 11. Risks and Technical Debt → Riesgos y Deuda Técnica (`11_riesgos_deuda_tecnica.md`)
**Propósito:** Identifica problemas potenciales y preocupaciones del equipo.
- **Contenido:** Riesgos técnicos, deuda técnica conocida, mitigaciones
- **Pregunta clave:** "¿Qué podría salir mal y cómo lo gestionamos?"
- **Enfoque:** Registro vivo de riesgos con planes de mitigación

#### 12. Glossary → Glosario (`12_glosario.md`)
**Propósito:** Define términos importantes del dominio y técnicos.
- **Contenido:** Diccionario de términos, traducciones, definiciones específicas del proyecto
- **Pregunta clave:** "¿Qué significan los términos que usamos?"
- **Ejemplo:** SOPP, PPP, SPVQ, etc.

### Organización de Artefactos

**Archivos principales (`01` a `12`):** Cada uno es un archivo `.md` con el contenido de la sección arc42 correspondiente.

**Directorio `diagramas/`:** Almacena artefactos de soporte:
- Archivos fuente de diagramas (PlantUML, Draw.io, Mermaid)
- Imágenes generadas (PNG, JPG, SVG)

## Alternativas consideradas

### 1) Estructura ad-hoc por carpetas temáticas sin marco
**Descripción:** Organización libre basada en temas técnicos sin seguir un estándar establecido.
- **Pros:** 
  - Rapidez inicial en la creación de documentación
  - Flexibilidad total para adaptarse a necesidades específicas
  - Sin curva de aprendizaje de marcos externos
- **Contras:** 
  - Deriva documental y duplicación de información
  - Difícil onboarding para nuevos miembros del equipo
  - Nula estandarización, dificultando mantenimiento
  - Ausencia de estructura para decisiones y calidad

### 2) 4+1 Views / C4 Model únicamente
**Descripción:** Uso exclusivo de modelos visuales de arquitectura sin plantilla integral.
- **Pros:** 
  - Foco visual claro en componentes arquitectónicos
  - Comunicación sencilla mediante diagramas
  - Amplia adopción en la industria
- **Contras:** 
  - No cubre requisitos de calidad de forma sistemática
  - Ausencia de framework para documentar decisiones arquitectónicas
  - Limitado en gestión de riesgos y deuda técnica
  - No proporciona estructura para gobernanza documental integral

### 3) Confluence/Wiki tradicional
**Descripción:** Organización mediante páginas web jerárquicas sin estructura predefinida.
- **Pros:** 
  - Facilidad de colaboración y edición
  - Búsqueda integrada y versionado básico
  - Familiaridad del equipo con herramientas wiki
- **Contras:** 
  - Tendencia a la fragmentación y páginas huérfanas
  - Ausencia de metodología para completitud arquitectónica
  - Dificultad para mantener coherencia conceptual
  - Dependencia de herramientas externas y posible vendor lock-in

## Consecuencias

- **Positivas:**
  - (+) **Consistencia estructural:** arc42 proporciona marco coherente que previene fragmentación documental y asegura cobertura completa de aspectos arquitectónicos
  - (+) **Trazabilidad integral:** Conexiones claras entre decisiones (ADRs), vistas arquitectónicas y especificaciones detalladas, facilitando auditoría y evolución
  - (+) **Onboarding acelerado:** Estructura estándar permite rápida familiarización de nuevos miembros del equipo con el sistema y su documentación
  - (+) **Comunicación mejorada:** Lenguaje común y estructura predecible facilita comunicación entre stakeholders técnicos y de negocio
  - (+) **Escalabilidad documental:** Framework probado que soporta crecimiento en complejidad y amplitud de la documentación
  - (+) **Alineamiento con buenas prácticas:** Adopción de estándar reconocido internacionalmente para documentación arquitectónica

- **Negativas:**
  - (-) **Disciplina requerida:** Necesidad de mantener consistencia en numeración, enlaces y ubicación de contenido según convenciones arc42
  - (-) **Overhead inicial:** Inversión de tiempo en aprendizaje del framework y creación de estructura base
  - (-) **Rigidez estructural:** Menor flexibilidad comparado con enfoques ad-hoc, requiriendo adaptación a la estructura predefinida
  - (-) **Curva de aprendizaje:** Equipo debe familiarizarse con propósito y uso de las 12 secciones arc42
  - (-) **Mantenimiento continuo:** Necesidad de actualizar múltiples secciones cuando ocurren cambios arquitectónicos significativos

## Convenciones y Estilo

### Nomenclatura de Archivos
- **Archivos principales:** `NN_nombre.md` donde NN = 01..12 según estructura arc42
- **Archivos de soporte:** Nombres descriptivos en español, sin espacios (usar guiones bajos)
- **Diagramas:** Formato `nombre_diagrama.ext` en directorio `diagramas/`

### Estándares de Contenido
- **Idioma:** Español para todo el contenido, títulos consistentes con terminología arc42. Referencia a terminos en ingles a continuación de su traduccion al español.
- **Formato:** Markdown estándar con extensiones Mermaid para diagramas
- **Enlaces:** Relativos preferidos dentro del repositorio para mantener portabilidad
- **Estructura:** Cada sección debe responder su "pregunta clave" específica

### Política de ADRs Integrada
- **Ubicación:** `adrs/adr-NNN_<slug>.md`
- **Numeración:** Secuencial y monótona, sin reutilizar números
- **Estados:** Proposed, Accepted, Deprecated, Superseded
- **Referencias cruzadas:** Marcar "Superseded by ADR-XXX" cuando corresponda
- **Integración:** Referenciar ADRs relevantes en otras secciones arc42

### Gestión de Diagramas
- **Fuentes:** Almacenar archivos editables en `diagramas/`
- **Formatos soportados:** PlantUML (.puml), Mermaid (.mmd), Draw.io (.drawio)
- **Imágenes generadas:** Generar en mismo directorio `diagramas/` en formato PNG/SVG
- **Convención:** Un diagrama por concepto, nombres descriptivos

## Flujo de Contribución

### Proceso de Documentación
1. **Nuevas decisiones arquitectónicas:**
   - Crear ADR en `adrs/`
   - Referenciar en `09_decisiones_arquitectonicas.md` y otras secciones relevantes
   - Actualizar `12_glosario.md` si introduce nuevos términos

2. **Cambios en arquitectura:**
   - Evaluar impacto en múltiples archivos arc42
   - Actualizar `05_bloques_construccion.md` si afecta estructura
   - Revisar y actualizar `10_requisitos_calidad.md`
   - Documentar nuevos riesgos en `11_riesgos_deuda_tecnica.md`

3. **Cambios sustantivos:**
   - Pull Request con revisión técnica obligatoria
   - Actualizar índices y enlaces en `README.md` principal
   - Verificar coherencia con vistas detalladas en `3_tech_design/`

### Etiquetas de Versionado
- **Commits:** `docs(arc42): descripción del cambio`
- **Pull Requests específicos:** 
  - `docs(arc42-goals)`: Cambios en introducción y objetivos
  - `docs(arc42-context)`: Modificaciones de contexto y alcance
  - `docs(arc42-strategy)`: Actualizaciones de estrategia de solución
  - `docs(arc42-decisions)`: Nuevos ADRs o cambios en decisiones

### Revisión y Mantenimiento
- **Revisión periódica:** Trimestral para detectar inconsistencias
- **Actualización de enlaces:** Verificar referencias cruzadas en cada cambio
- **Sincronización:** Mantener coherencia entre arc42 y especificaciones técnicas detalladas

## Integración con Herramientas

### Generación de Diagramas
- **Mermaid:** Integrado en Markdown para diagramas simples
- **PlantUML:** Para diagramas complejos de arquitectura
- **C4 Model:** Para vistas de contexto, contenedores y componentes

### Validación de Documentación
- **Linting:** Verificar formato Markdown y enlaces
- **Completitud:** Checklist para asegurar que cada sección responde su pregunta clave
- **Coherencia:** Validar que decisiones se reflejan consistentemente en todas las secciones

## Historial de estado
- 2025-08-17: Propuesta (Javier Errazuriz)
- 2025-08-17: Aceptada (Javier Errazuriz)

## Referencias
- ADR-001: Adopción de ADRs (Documenting Architecture Decisions, Nygard): `adrs/adr001_adopcion_ADR.md`
- ADR-003: Política de Idioma para Documentación y Código: `adrs/adr003_politica_idioma.md`
- arc42 Template Overview: https://arc42.org/overview
- arc42 FAQ: https://arc42.org/faq
- Ejemplos de arc42: https://arc42.org/examples
- C4 Model: https://c4model.com/
- PlantUML: https://plantuml.com/