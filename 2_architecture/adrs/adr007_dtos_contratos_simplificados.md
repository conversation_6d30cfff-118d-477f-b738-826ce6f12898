# ADR-007: DTOs como Contratos Simplificados

- **Estado:** Aceptada  
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura
- **Supersede:** ADR-007 original (DTOs para Event Sourcing)

## Contexto

En una arquitectura CRUD simplificada (ADR-006), necesitamos **contratos de datos ligeros** que:

1. **Validen entrada** a Actions con reglas básicas
2. **Integren con Enums** para validación de transiciones
3. **Soporten version tracking** con datos específicos para cambios significativos  
4. **Desacoplen capas** entre UI y business logic
5. **Provean tipado fuerte** para prevenir errores

**Problema**: Usar arrays asociativos o pasar modelos Eloquent directamente crea acoplamiento y carece de validación específica.

## Decisión

Adoptar **DTOs simplificados** usando `spatie/laravel-data` que actúan como:

- **Contratos de entrada** para Actions
- **Validadores básicos** con reglas de negocio usando Enums
- **Portadores de contexto** para version tracking
- **Interfaces tipadas** entre UI y business logic

### Tipos de DTOs Simplificados

**1. CRUD DTOs**: Para operaciones estándar
- `CreateProductItemData` - Creación con validación básica
- `UpdateProductItemData` - Actualizaciones con change reason opcional

**2. Version Tracking DTOs**: Para cambios significativos
- `RequoteProductItemData` - Re-cotizaciones con tracking obligatorio

## Implementación Simplificada

### 1. DTO Estándar para Updates

```php
class UpdateProductItemData extends Data
{
    public function __construct(
        public ProductItem $productItem,
        public string $name,
        public ProductItemStatus $status,
        public array $specifications,
        public ?string $changeReason = null
    ) {}
    
    public static function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'status' => ['required', 'enum:' . ProductItemStatus::class],
            'specifications' => ['required', 'array'],
            'changeReason' => ['nullable', 'string', 'max:500'],
        ];
    }
    
    // Business validation usando StateTransitionService según ADR-010
    public function authorize(): bool
    {
        $stateTransitionService = app(StateTransitionService::class);
        return $stateTransitionService->isTransitionAllowed($this->productItem, $this->status, auth()->user());
    }
    
    public function getValidationMessage(): string
    {
        return "Cannot transition from {$this->productItem->status->getLabel()} to {$this->status->getLabel()}";
    }
}
```

### 2. DTO Especializado para Re-cotizaciones

```php
class RequoteProductItemData extends Data
{
    public function __construct(
        public ProductItem $productItem,
        public array $newSpecifications,
        public ProductItemStatus $newStatus,
        public float $previousPrice,
        public float $newPrice,
        public string $reason
    ) {}
    
    public static function rules(): array
    {
        return [
            'newSpecifications' => ['required', 'array', 'min:1'],
            'newStatus' => ['required', 'enum:' . ProductItemStatus::class],
            'previousPrice' => ['required', 'numeric', 'min:0'],
            'newPrice' => ['required', 'numeric', 'min:0'],
            'reason' => ['required', 'string', 'max:500'],
        ];
    }
    
    // Validación específica para requotes usando StateTransitionService
    public function authorize(): bool
    {
        $stateTransitionService = app(StateTransitionService::class);
        
        // Debe ser transición válida
        if (!$stateTransitionService->isTransitionAllowed($this->productItem, $this->newStatus, auth()->user())) {
            return false;
        }
        
        // Cambio significativo requerido
        return $this->hasSignificantChanges();
    }
    
    private function hasSignificantChanges(): bool
    {
        return $this->productItem->specifications !== $this->newSpecifications ||
               $this->previousPrice !== $this->newPrice ||
               $this->productItem->status !== $this->newStatus;
    }
}
```

### 3. DTO Simple para Creación

```php
class CreateProductItemData extends Data
{
    public function __construct(
        public Project $project,
        public string $name,
        public array $specifications,
        public ?ProductItemStatus $initialStatus = null
    ) {}
    
    public static function rules(): array
    {
        return [
            'project' => ['required', 'exists:projects,id'],
            'name' => ['required', 'string', 'max:255'],
            'specifications' => ['required', 'array', 'min:1'],
            'initialStatus' => ['nullable', 'enum:' . ProductItemStatus::class],
        ];
    }
    
    // Business validation básica
    public function authorize(): bool
    {
        // No crear en proyecto con estado incompatible
        return !$this->project->isCompleted();
    }
    
    // Default value usando business logic
    public function getResolvedInitialStatus(): ProductItemStatus
    {
        return $this->initialStatus ?? ProductItemStatus::Draft;
    }
}
```

### 4. Integración con Actions Simplificadas

```php
class RequoteProductItemAction
{
    public function __construct(
        private ProductItemVersionService $versionService
    ) {}
    
    public function execute(RequoteProductItemData $data): ProductItem
    {
        // DTO proporciona validación simple
        if (!$data->authorize()) {
            throw new ValidationException($data->getValidationMessage());
        }
        
        $item = $data->productItem;
        
        // Capturar datos para version tracking
        $previousData = [
            'specifications' => $item->specifications,
            'status' => $item->status->value,
            'price' => $data->previousPrice,
        ];
        
        // CRUD update simple
        $item->update([
            'specifications' => $data->newSpecifications,
            'status' => $data->newStatus,
        ]);
        
        // Version tracking usando service
        $this->versionService->createVersion(
            item: $item,
            changeType: 'requote',
            previousData: $previousData,
            newData: [
                'specifications' => $data->newSpecifications,
                'status' => $data->newStatus->value,
                'price' => $data->newPrice,
            ],
            summary: $data->reason
        );
        
        return $item;
    }
}
```

### 5. Action Estándar para Updates

```php
class UpdateProductItemAction
{
    public function __construct(
        private ProductItemVersionService $versionService
    ) {}
    
    public function execute(UpdateProductItemData $data): ProductItem
    {
        $item = $data->productItem;
        
        // Validation usando DTO + Enum
        if (!$data->authorize()) {
            throw new ValidationException($data->getValidationMessage());
        }
        
        $previousData = $this->captureRelevantData($item);
        
        // Standard CRUD update
        $item->update([
            'name' => $data->name,
            'specifications' => $data->specifications,
            'status' => $data->status,
        ]);
        
        $newData = $this->captureRelevantData($item);
        
        // Version tracking condicional para cambios significativos
        if ($this->hasSignificantChanges($previousData, $newData)) {
            $this->versionService->createVersion(
                item: $item,
                changeType: 'spec_update',
                previousData: $previousData,
                newData: $newData,
                summary: $data->changeReason
            );
        }
        
        return $item;
    }
    
    private function captureRelevantData(ProductItem $item): array
    {
        return [
            'name' => $item->name,
            'status' => $item->status->value,
            'specifications' => $item->specifications,
        ];
    }
    
    private function hasSignificantChanges(array $previous, array $new): bool
    {
        return $previous['specifications'] !== $new['specifications'] ||
               $previous['status'] !== $new['status'];
    }
}
```

## Beneficios de la Simplificación

### 1. Contratos Ligeros
DTOs definen exactamente qué datos requiere cada operación sin complejidad innecesaria.

### 2. Validación Práctica
Integración simple con Enum methods para business rules esenciales.

### 3. Version Tracking Selectivo
DTOs especializados para operaciones que requieren tracking (como requotes).

### 4. Tipado Fuerte
IDE puede validar operaciones en tiempo de desarrollo.

### 5. Desacoplamiento Básico
UI y business logic desacoplados via contratos simples.

## Consecuencias

### Positivas
- ✅ **Contratos Claros**: Cada operación tiene requerimientos explícitos
- ✅ **Validación Preventiva**: Business rules se validan antes de CRUD operations
- ✅ **Tipado Fuerte**: Eliminación de errores de tipos
- ✅ **Version Tracking Context**: DTOs proveen datos específicos para tracking
- ✅ **Simplicidad**: Sin overhead innecesario de Event Sourcing

### Limitaciones (Aceptables)
- ⚠️ **Validación Básica**: Solo business rules esenciales, no validación exhaustiva
- ⚠️ **Una Clase por Operación**: Cada caso de uso requiere DTO específico

### Ventajas vs Event Sourcing DTOs
- 🚀 **10x menos código**: DTOs simples vs DTOs con contexto complejo
- 👥 **Fácil de entender**: Cualquier dev Laravel puede trabajar inmediatamente
- 🔧 **Debugging simple**: Path directo sin múltiples layers de abstracción

## Comparación: Simplificado vs Event Sourcing

| Aspecto | Event Sourcing DTOs | DTOs Simplificados |
|---------|---------------------|---------------------|
| **Validación** | Business rules complejas + context enrichment | Reglas básicas + Enum validation |
| **Contexto** | Rich audit context para eventos | Context específico para version tracking |
| **Integración** | Con Aggregates + Events + Projectors | Con Actions + Models + Services |
| **Complejidad** | Alta - múltiples métodos por DTO | Baja - validación + authorize() |
| **Performance** | Overhead de Event Sourcing | Direct operations |

## Plan de Implementación

### Fase 1: DTOs Core (1 día)
1. `CreateProductItemData`
2. `UpdateProductItemData`
3. `RequoteProductItemData`

### Fase 2: Integration con Actions (1 día)
1. Actualizar Actions existentes
2. Agregar validation usando DTOs
3. Integrar con ProductItemVersionService

### Fase 3: Expansion (1 día)
1. DTOs para Project operations
2. DTOs para User management
3. Testing y refinement

**Total: ~3 días** vs semanas para DTOs complejos con Event Sourcing

## Referencias

- **[ADR-006: Arquitectura CRUD Simplificada](adr006_crud_arquitectura_simplificada.md)** - Arquitectura base
- **[ADR-008: Enums con Métodos](adr008_uso_enums_con_metodos.md)** - Business rules integration
- **[Arquitectura Integrada](../00_arquitectura_integrada.md)** - Especificación completa
- **[Spatie Laravel Data](https://spatie.be/docs/laravel-data)** - Implementación técnica