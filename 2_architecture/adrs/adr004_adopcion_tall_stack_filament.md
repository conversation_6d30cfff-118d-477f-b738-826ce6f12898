# ADR-004: Adopción de la Pila TALL y Filament

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura

## Contexto
Se requiere una pila tecnológica que permita un desarrollo rápido y eficiente para una aplicación web compleja, pero con un equipo de tamaño reducido. La estrategia debe minimizar la complejidad accidental (ej. gestionar dos bases de código separadas para frontend y backend, APIs, etc.) y maximizar la productividad del desarrollador.

## Decisión
Adoptar la **Pila TALL (Tailwind CSS, Alpine.js, Laravel, Livewire)** como la base tecnológica para la construcción de la aplicación. Además, se utilizará **Filament v4** como la herramienta principal para construir las interfaces de administración y los paneles de gestión de datos.

## Consecuencias

**Positivas:**
- (+) **Ecosistema Unificado:** Permite a los desarrolladores trabajar en un único entorno (Laravel/PHP) tanto para la lógica de negocio como para la interfaz de usuario reactiva, reduciendo el cambio de contexto.
- (+) **Desarrollo Rápido:** Filament y Livewire permiten crear interfaces complejas y dinámicas con mucho menos código que un enfoque tradicional de SPA + API.
- (+) **Mantenimiento Simplificado:** Una única base de código es más fácil de mantener, probar y desplegar.
- (+) **Rendimiento:** Livewire minimiza la cantidad de JavaScript enviado al cliente, resultando en tiempos de carga iniciales rápidos.

**Negativas:**
- (-) **Acoplamiento UI-Backend:** Existe un acoplamiento más fuerte entre la vista y la lógica del servidor en comparación con un enfoque de API desacoplada.
- (-) **Necesidad de API Futura:** Si en el futuro se requiere una aplicación móvil nativa, se deberá construir una capa de API dedicada, ya que la comunicación de Livewire no es adecuada para ese propósito.
