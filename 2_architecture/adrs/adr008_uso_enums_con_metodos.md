# ADR-008: Enums como Objetos de Valor para UI y Contexto

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura

## Contexto

En la arquitectura, necesitamos una forma consistente y tipada de representar conceptos con un conjunto finito de valores (ej. estados, categorías, roles). Estos conceptos a menudo requieren lógica de presentación (UI) asociada, como etiquetas y colores, y deben proporcionar contexto de negocio.

## Decisión

Adoptar **Enums tipados de PHP 8.1+ con métodos** como **Objetos de Valor** para representar estados y categorías. La responsabilidad de estos Enums se centrará en:

1.  **Definir valores válidos** para un concepto.
2.  **Centralizar la lógica de UI** (etiquetas, colores, iconos).
3.  **Proveer contexto de negocio** (fase del proceso, actor responsable).

**Importante:** La lógica de validación de transiciones de estado **NO** reside en el Enum, sino que se delega a un `StateTransitionService` dedicado (ver `ADR-010`).

### Tipos de Métodos por Propósito

**UI Logic Methods**: Para presentación
- `getLabel()`: Etiquetas localizadas
- `getColor()`: Colores para badges/UI

**Context Methods**: Para información de negocio
- `getResponsibleActor()`: Actor responsable por estado
- `getPhase()`: Fase de proceso (planning, execution, etc.)

## Implementación Simplificada

```php
enum ProductItemStatus: string
{
    case Draft = 'draft';
    case ReadyForSourcing = 'ready_for_sourcing';
    case SourcingInProgress = 'sourcing_in_progress';
    case InternalReviewPending = 'internal_review_pending';
    case RevisionRequested = 'revision_requested';
    case QuotedToCustomer = 'quoted_to_customer';
    case CustomerRevisionRequested = 'customer_revision_requested';
    case PendingVmApproval = 'pending_vm_approval';
    case VmRejected = 'vm_rejected';
    case PendingPpsApproval = 'pending_pps_approval';
    case PpsRejected = 'pps_rejected';
    case PendingPiApproval = 'pending_pi_approval';
    case ReadyForProduction = 'ready_for_production';
    case InProduction = 'in_production';
    case PendingShipment = 'pending_shipment';
    case InternationalTransit = 'international_transit';
    case CustomsClearance = 'customs_clearance';
    case DomesticTransit = 'domestic_transit';
    case Delivered = 'delivered';

    // UI Logic
    public function getLabel(): string
    {
        return match($this) {
            self::Draft => 'Borrador',
            self::ReadyForSourcing => 'Listo para Sourcing',
            self::SourcingInProgress => 'Sourcing en Progreso',
            self::InternalReviewPending => 'Pendiente de Revisión Interna',
            self::RevisionRequested => 'Requiere Revisión',
            self::QuotedToCustomer => 'Cotizado al Cliente',
            self::CustomerRevisionRequested => 'Revisión Solicitada por Cliente',
            self::PendingVmApproval => 'Pendiente Aprobación de Maqueta Virtual',
            self::VmRejected => 'Maqueta Virtual Rechazada',
            self::PendingPpsApproval => 'Pendiente Aprobación de Muestra Física',
            self::PpsRejected => 'Muestra Física Rechazada',
            self::PendingPiApproval => 'Pendiente Aprobación de Factura Proforma',
            self::ReadyForProduction => 'Listo para Producción',
            self::InProduction => 'En Producción',
            self::PendingShipment => 'Pendiente de Envío',
            self::InternationalTransit => 'En Tránsito Internacional',
            self::CustomsClearance => 'En Despacho de Aduanas',
            self::DomesticTransit => 'En Tránsito Doméstico',
            self::Delivered => 'Entregado',
        };
    }

    public function getColor(): string
    {
        return match($this) {
            self::Draft => 'gray',
            self::ReadyForSourcing, self::SourcingInProgress => 'blue',
            self::InternalReviewPending, self::RevisionRequested => 'yellow',
            self::QuotedToCustomer => 'purple',
            self::CustomerRevisionRequested => 'orange',
            self::PendingVmApproval, self::PendingPpsApproval, self::PendingPiApproval => 'cyan',
            self::VmRejected, self::PpsRejected => 'red',
            self::ReadyForProduction, self::InProduction => 'indigo',
            self::PendingShipment, self::InternationalTransit, self::CustomsClearance, self::DomesticTransit => 'amber',
            self::Delivered => 'green',
        };
    }

    // Business Context
    public function getPhase(): string
    {
        return match($this) {
            self::Draft, self::ReadyForSourcing, self::SourcingInProgress => 'planning',
            self::InternalReviewPending, self::RevisionRequested, self::QuotedToCustomer, self::CustomerRevisionRequested => 'negotiation',
            self::PendingVmApproval, self::VmRejected, self::PendingPpsApproval, self::PpsRejected, self::PendingPiApproval => 'approval',
            self::ReadyForProduction, self::InProduction => 'production',
            self::PendingShipment, self::InternationalTransit, self::CustomsClearance, self::DomesticTransit => 'logistics',
            self::Delivered => 'completed',
        };
    }
    
    // ❌ NO INCLUIR: public function canTransitionTo(...)
    // ✅ Usar StateTransitionService para lógica de transiciones
}
```

## Consecuencias

**Positivas:**
- (+) **Fuente Única de Verdad** para la definición de estados y su presentación.
- (+) **Consistencia en la UI** garantizada en toda la aplicación.
- (+) **Código Limpio y Tipado:** Elimina "magic strings" y provee contexto de negocio de forma segura.

**Negativas:**
- (-) La lógica de negocio está separada, requiriendo el uso combinado del Enum y servicios asociados (como `StateTransitionService`).
