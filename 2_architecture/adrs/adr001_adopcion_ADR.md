# ADR-001: Adopción de ADRs (Documenting Architecture Decisions, Nygard)

- Estado: Aceptada
- Fecha: 2025-08-17
- Deciders: <PERSON>
- Authors: <AUTHORS>

## Contexto
Es necesario registrar decisiones arquitectónicas significativas de forma ágil, modular y trazable a lo largo del proyecto PromoSmart. Los documentos largos tienden a desactualizarse; se prefieren piezas pequeñas y mantenibles que faciliten la comprensión de decisiones críticas y sus motivaciones.

**Fuerzas impulsoras:**
- Necesidad de documentar el "por qué" de las decisiones arquitectónicas
- Facilitar onboarding de nuevos miembros del equipo
- Mantener trazabilidad de cambios y evolución del sistema
- Evitar repetir discusiones ya resueltas
- Facilitar revisiones y auditorías técnicas

## Decisión
Adoptar el formato de Architecture Decision Records (ADRs) según Michael Nygard para documentar decisiones arquitectónicas del proyecto.

### **Implementación específica:**
**Detalles generales**
- Mantener los ADRs en el repositorio bajo `2_architecture/adrs/adrNNN_<slug>.md` (Markdown)
- Numeración secuencial y monótona (NNN = 001, 002, etc.)
- Estructura mínima por ADR: Título, Estado, Fecha, Deciders, Authors, Contexto, Decisión, Consecuencias
- Secciones opcionales: Alternativas consideradas, Historial de estado, Referencias
- Si una decisión es reemplazada, marcar el ADR como "Superseded by ADR-XXX" y conservar histórico

**Estados posibles del ADR:**
- **Propuesta (Proposed):** Decisión propuesta, en proceso de evaluación y discusión
- **Aceptada (Accepted):** Decisión aceptada e implementada activamente
- **Obsoleta (Deprecated):** Decisión obsoleta pero aún en uso (no recomendada para nuevos desarrollos)
- **Reemplazada (Superseded):** Decisión reemplazada por otra (debe indicar "Superseded by ADR-XXX")
- **Rechazada (Rejected):** Decisión evaluada y rechazada (conservar para evitar repetir discusiones)

**Convenciones de nomenclatura:**
- Archivo: `adrNNN_<slug>.md` donde NNN es número de 3 dígitos (001, 002, etc.)
- Slug: máximo 5 palabras clave separadas por guiones bajos, sin artículos ni preposiciones
- Ejemplos: `adr001_adopcion_adr.md`, `adr002_arquitectura_microservicios.md`

**Criterios para crear un ADR:**
- Decisiones que afecten estructura del sistema o arquitectura general
- Elección de tecnologías, frameworks o librerías principales
- Patrones arquitectónicos y principios de diseño
- Políticas de seguridad, rendimiento o escalabilidad
- Interfaces externas o integraciones críticas
- Cambios que requieran refactoring significativo
- Decisiones con impacto en múltiples equipos o módulos

**No requieren ADR:**
- Decisiones de implementación local o específicas de módulo
- Cambios menores de configuración
- Correcciones de bugs sin impacto arquitectónico
- Decisiones fácilmente reversibles sin costo

**Integración con documentación:**
- Los ADRs se referenciarán desde `09_decisiones_arquitectonicas.md` (arc42)
- Se enlazarán desde secciones afectadas en documentación técnica
- Cada ADR debe actualizarse cuando cambien las circunstancias

**Roles y responsabilidades:**
- **Author:** Redacta el ADR, puede ser cualquier miembro del equipo técnico
- **Decider:** Toma la decisión final, típicamente arquitecto de software o lead técnico
- **Reviewers:** Equipo técnico que revisa y proporciona feedback

**Flujo de aprobación:**
1. Author crea ADR en estado "Propuesta"
2. Revisión técnica por pares (mínimo 1 reviewer)
3. Discusión y refinamiento si es necesario
4. Decider acepta, rechaza o solicita cambios
5. ADR se marca como "Aceptada" y se comunica al equipo
6. Implementación y seguimiento de la decisión

**Mantenimiento:**
- Revisión trimestral de ADRs "Aceptadas" para verificar vigencia
- Actualización inmediata cuando cambien las circunstancias
- Marcado como "Obsoleta" o "Reemplazada" según corresponda


**Plantilla estándar:**
```markdown
# ADR-NNN: Título descriptivo de la decisión

- Estado: [Propuesta|Aceptada|Obsoleta|Reemplazada|Rechazada]
- Fecha: YYYY-MM-DD
- Deciders: [nombre(s) de quien(es) toman la decisión]
- Authors: <AUTHORS>

## Contexto
[Descripción del problema o situación que motiva la decisión]

**Fuerzas impulsoras:**
- [Factor 1]
- [Factor 2]

## Decisión
[Descripción clara de la decisión tomada]

**Implementación específica:**
- [Detalle de implementación 1]
- [Detalle de implementación 2]

## Alternativas consideradas (opcional)
### Alternativa 1: [Nombre]
- Pros: [ventajas]
- Contras: [desventajas]

## Consecuencias
**Positivas:**
- (+) [Consecuencia positiva 1]

**Negativas:**
- (-) [Consecuencia negativa 1]

## Historial de estado
- YYYY-MM-DD: Propuesta → Estado (Author)

## Referencias (opcional)
- [Enlace o documento relevante]
```


## Consecuencias

**Positivas:**
- (+) Trazabilidad completa de motivaciones y efectos de decisiones críticas
- (+) Facilita onboarding y comprensión del contexto histórico
- (+) Evita aceptación o reversión "a ciegas" de decisiones pasadas
- (+) Mejora calidad de discusiones arquitectónicas al formalizar el proceso
- (+) Documentación viva que evoluciona con el proyecto

**Negativas:**
- (-) Requiere disciplina para mantener estado y enlaces actualizados
- (-) Overhead adicional en el proceso de toma de decisiones
- (-) Necesidad de revisar y actualizar ADRs cuando cambien las circunstancias

## Historial de estado
- 2025-08-17: Propuesta → Aceptada (Javier Errazuriz)

## Referencias
- Michael Nygard, "Documenting Architecture Decisions": https://cognitect.com/blog/2011/11/15/documenting-architecture-decisions
- ADR GitHub repository: https://adr.github.io/
- ADR-000: Adopción de ADRs (Nygard): `2_arquitectura/adrs/adr_000_adoptar_adr_nygard.md`
- ADR Tools: https://github.com/npryce/adr-tools