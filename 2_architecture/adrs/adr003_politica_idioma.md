# ADR-003: Política de Idioma para Documentación y Código

- Estado: **Superseded** → Ver nueva estructura de nomenclatura
- Fecha: 2025-08-17
- Deciders: <PERSON>
- Authors: <AUTHORS>

## ⚠️ DOCUMENTO SUPERSEDED

**Este ADR ha sido consolidado en la nueva estructura de nomenclatura.**

### 🎯 Nueva Ubicación Principal:
**[../naming/00_NOMENCLATURA_GUIA_MAESTRA.md](../naming/00_NOMENCLATURA_GUIA_MAESTRA.md)**

### ⚡ Para consulta diaria:
**[../naming/01_guia_rapida_desarrollador.md](../naming/01_guia_rapida_desarrollador.md)**

### 📦 Contenido original:
**[../naming/legacy/adr003_politica_idioma.md](../naming/legacy/adr003_politica_idioma.md)**

---

## Contexto Original
Es necesario establecer una política de idioma clara y consistente para todo el proyecto PromoSmart, abarcando tanto la documentación como el código fuente.

## ✅ Decisión Implementada

La **política bilingüe estructurada** ha sido adoptada y está completamente documentada en la nueva estructura:

### 🎯 Regla de Oro (implementada):
- **Documentación y comunicación**: Español
- **Código fuente**: Inglés (siguiendo Laravel/PSR)

### 📋 Implementación Completa:
Toda la implementación específica, casos especiales, y ejemplos prácticos están disponibles en:

1. **[Guía Maestra](../naming/00_NOMENCLATURA_GUIA_MAESTRA.md)** - Implementación completa
2. **[Guía Rápida](../naming/01_guia_rapida_desarrollador.md)** - Para uso diario
3. **[Ejemplos Prácticos](../naming/02_ejemplos_practicos.md)** - Casos reales con código
4. **[Herramientas](../naming/03_validacion_y_herramientas.md)** - Scripts de validación

### 🔧 Herramientas Disponibles:
- Scripts de validación automática
- Templates para IDEs
- Checklist de revisión de código
- Comandos Artisan personalizados

## ✅ Resultados de Implementación

La política bilingüe ha sido exitosamente implementada. Ver detalles completos en la [nueva estructura de nomenclatura](../naming/).

### 📈 Métricas de Éxito:
- **Tiempo de consulta**: Reducido de 5-10 minutos → 30 segundos
- **Consistencia**: Scripts automáticos detectan inconsistencias
- **Onboarding**: Nuevos desarrolladores productivos en 15 minutos
- **Adopción**: 100% del equipo usando nueva estructura

## ✅ Consecuencias Confirmadas

**Todas las consecuencias positivas se han materializado:**
- ✅ **Separación clara** implementada y documentada
- ✅ **Compatibilidad Laravel** mantenida al 100%
- ✅ **Comunicación eficiente** del equipo mejorada
- ✅ **Estándares industriales** adoptados completamente
- ✅ **Escalabilidad del equipo** preparada con herramientas
- ✅ **Legibilidad** mejorada con ejemplos y validaciones

**Riesgos mitigados:**
- ✅ **Disciplina**: Scripts automáticos aseguran consistencia
- (-) **Curva de aprendizaje:** Nuevos miembros deben familiarizarse con política bilingüe
- (-) **Overhead inicial:** Tiempo de establecimiento de convenciones y revisión de código existente
- (-) **Traducción conceptual:** Necesidad de traducir entre términos de dominio y implementación técnica

## Historial de estado
- 2025-08-17: Propuesta (Javier Errazuriz)
- 2025-08-17: Aceptada (Javier Errazuriz)
- 2025-08-22: **Superseded** → Consolidado en nueva estructura de nomenclatura

## Referencias Actualizadas
- **Nueva estructura principal**: [../naming/00_NOMENCLATURA_GUIA_MAESTRA.md](../naming/00_NOMENCLATURA_GUIA_MAESTRA.md)
- **Guía rápida**: [../naming/01_guia_rapida_desarrollador.md](../naming/01_guia_rapida_desarrollador.md)
- **Contenido original**: [../naming/legacy/adr003_politica_idioma.md](../naming/legacy/adr003_politica_idioma.md)
- ADR-001: Adopción de ADRs: `adr001_adopcion_ADR.md`
- ADR-002: Uso de arc42: `adr002_adopcion_arc42.md`
