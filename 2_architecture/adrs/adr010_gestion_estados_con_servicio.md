# ADR-010: Gestión de Estados con Servicio Dedicado

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura

## Contexto

La gestión del ciclo de vida del `ProductItem` involucra más de 16 estados y reglas de transición complejas. El enfoque inicial de poner esta lógica en métodos dentro del propio `Enum` (`ADR-008`) presenta riesgos de mantenibilidad y viola el Principio de Responsabilidad Única. Se necesita un patrón más robusto y escalable que pueda manejar lógica condicional (basada en roles, datos del modelo, etc.) sin sobrecargar el `Enum`.

## Decisión

Adoptar un **patrón de Servicio de Transición de Estado (`StateTransitionService`)** para centralizar toda la lógica de validación de cambios de estado. Las responsabilidades se separan de la siguiente manera:

1.  **`Enum` (`ProductItemStatus`):** Actúa como un objeto de valor simple. Su única responsabilidad es definir los estados posibles y la lógica de presentación asociada (etiquetas, colores, etc.).
2.  **`StateTransitionService`:** Contiene una matriz de transiciones explícita y métodos para validar si una transición es permitida, encapsulando toda la lógica de negocio compleja.

## Implementación

-   Se crea el servicio `app/Services/StateTransitionService.php`.
-   Este servicio contiene una matriz privada `$transitions` que define los flujos válidos.
-   Provee un método `isTransitionAllowed(ProductItem $item, ProductItemStatus $newStatus, User $user)` que realiza validaciones compuestas.
-   Las `Actions` y `DTOs` inyectan y utilizan este servicio para la validación, en lugar de llamar a métodos en el `Enum`.

## Consecuencias

**Positivas:**
- (+) **Separación de Responsabilidades:** El `Enum` define el estado, el `Service` valida las transiciones. Código más limpio.
- (+) **Alta Testeabilidad:** El `StateTransitionService` puede ser probado unitariamente con diversos escenarios complejos.
- (+) **Mantenibilidad y Escalabilidad:** Añadir nuevas reglas o condiciones se hace en un único lugar sin modificar el `Enum`.
- (+) **Lógica Centralizada:** La matriz de transiciones es una fuente de verdad clara y legible para el flujo de estados.

**Negativas:**
- (-) **Mayor Boilerplate:** Introduce una clase adicional en comparación con el enfoque de "Enum con métodos".
- (-) **Indirección:** La validación requiere llamar a un servicio externo en lugar de a un método del propio objeto de estado.
