# ADR-013: Adopción de la Arquitectura por Capas para Filament con Spatie y Shield

- Estado: Aceptada
- Fecha: 2025-08-20
- Deciders: <PERSON><PERSON><PERSON> de Arquitectura

## Contexto
Se necesita una arquitectura de autorización y seguridad de interfaz de usuario que sea robusta, escalable y mantenible para el sistema PromoSmart. La solución debe manejar un esquema de permisos complejo (roles, permisos específicos) y un modelo de aislamiento de datos por equipos (países), integrándose de forma nativa y eficiente con el panel de administración construido en Filament v4.

## Decisión
Adoptar una **arquitectura por capas** bien definida que utiliza herramientas especializadas del ecosistema Laravel para cada responsabilidad de la autorización:

1.  **Capa de Modelo de Autorización (El Motor):** Se utilizará **Spatie <PERSON>vel Permissions** como la única fuente de verdad para definir Roles y Permisos. Se activará la funcionalidad de **"Teams"** para acotar los roles de los usuarios a un país específico.

2.  **Capa de Aislamiento de Datos (El Filtro):** Se implementará un **Global Scope** personalizado en los modelos de Eloquent relevantes. Este scope será responsable de filtrar automáticamente todas las consultas a la base de datos para que un usuario solo pueda ver los registros pertenecientes a su equipo (país) actual.

3.  **Capa de Lógica de Autorización (El Guardia):** Se usarán las **Policies de Laravel** para centralizar la lógica de negocio de las autorizaciones en el backend. Los métodos de las policies verificarán los permisos del usuario consultando a Spatie (`$user->can(...)`).

4.  **Capa de Seguridad de UI (El Automatizador):** Se utilizará **Filament Shield** para conectar la capa de autorización de Spatie con la interfaz de Filament. Shield será responsable de mostrar u ocultar automáticamente los elementos de la UI (enlaces de navegación, botones, acciones) basándose en los permisos del usuario, siguiendo una convención de nomenclatura estricta.

## Consecuencias

**Positivas:**
- (+) **Separación de Responsabilidades:** Cada herramienta tiene un rol claro y definido, lo que resulta en un código más limpio y mantenible.
- (+) **Arquitectura Robusta y Escalable:** El modelo es capaz de crecer para soportar nuevos países, roles y permisos con un esfuerzo mínimo.
- (+) **Automatización y Productividad:** Filament Shield elimina la necesidad de escribir lógica de autorización repetitiva en la capa de presentación, reduciendo errores y acelerando el desarrollo.
- (+) **Seguridad Integral:** La combinación del filtro de datos a nivel de base de datos (Global Scope) y la seguridad a nivel de UI (Shield) proporciona una defensa en profundidad.

**Negativas:**
- (-) **Curva de Aprendizaje Inicial:** El equipo debe comprender el rol de cada una de las cuatro herramientas y cómo interactúan entre sí.
- (-) **Configuración Inicial Detallada:** Requiere una configuración inicial cuidadosa de Spatie (con Teams), el Global Scope, las Policies y Shield para asegurar que todas las capas funcionen en armonía.

## Referencias
- ADR-004: Adopción de la Pila TALL y Filament
- ADR-005: Elección de PostgreSQL como Base de Datos
- Spatie Laravel Permissions Documentation
- Filament Shield Documentation
