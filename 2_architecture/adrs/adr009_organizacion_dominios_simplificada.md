# ADR-009: Organización por Dominios de Negocio Simplificada

- **Estado:** Aceptada
- **Fecha:** 2025-08-18
- **Deciders:** Equipo de Arquitectura
- **Supersede:** ADR-011 original (Organización con Event Sourcing)

## Contexto

En una arquitectura CRUD simplificada (ADR-006), necesitamos una estrategia de organización de código que:

1. **Refleje dominios de negocio** más que estructura técnica
2. **Sea simple y navegable** para cualquier dev Laravel
3. **Facilite ownership** claro por equipos
4. **Mantenga cohesión alta** dentro de contextos de negocio
5. **Evite over-engineering** innecesario

**Problema**: Organización puramente técnica (Controllers/, Services/, etc.) no refleja la complejidad de negocio. Pero tampoco necesitamos la complejidad de Event Sourcing cross-domain.

## Decisión

Adoptar **organización por dominios de negocio simplificada** que estructura código por contextos del dominio usando patrones estándar de Laravel.

### Estructura Organizacional Simplificada

```
app/
├── Actions/            # Casos de uso por dominio
│   ├── Project/        # Domain: Project Management
│   │   ├── CreateProjectAction.php
│   │   ├── UpdateProjectAction.php
│   │   └── CalculateProjectStatusAction.php
│   ├── ProductItem/    # Domain: Product Item Lifecycle
│   │   ├── CreateProductItemAction.php
│   │   ├── UpdateProductItemAction.php
│   │   └── RequoteProductItemAction.php
│   ├── User/          # Domain: User Management
│   │   ├── CreateUserAction.php
│   │   └── UpdateUserAction.php
│   └── Financial/     # Domain: Financial Operations
│       ├── CalculateCostingAction.php
│       └── GenerateInvoiceAction.php
├── Data/              # DTOs por dominio
│   ├── Project/
│   │   ├── CreateProjectData.php
│   │   └── UpdateProjectData.php
│   ├── ProductItem/
│   │   ├── CreateProductItemData.php
│   │   ├── UpdateProductItemData.php
│   │   └── RequoteProductItemData.php
│   ├── User/
│   │   └── CreateUserData.php
│   └── Financial/
│       └── CalculateCostingData.php
├── Services/          # Lógica compleja cross-domain
│   ├── ProductItemVersionService.php    # Version tracking
│   ├── ProjectStatusCalculatorService.php # Status derivation
│   └── ReportGenerationService.php      # Cross-domain reports
├── Models/            # Eloquent models (flat hierarchy)
│   ├── ProductItem.php
│   ├── ProductItemVersion.php
│   ├── Project.php
│   ├── User.php
│   └── Customer.php
├── Enums/             # Business rules (flat hierarchy)
│   ├── ProductItemStatus.php
│   ├── ProjectStatus.php
│   └── UserRole.php
└── Http/              # Standard Laravel
    ├── Controllers/
    └── Resources/      # Filament resources
```

## Principios de Organización

### 1. Domain-Driven Structure para Logic
**Actions/** y **Data/** organizados por dominio de negocio para facilitar navegación y ownership.

### 2. Flat Hierarchy para Shared Components
**Models/**, **Enums/**, **Services/** mantienen estructura plana porque son compartidos cross-domain.

### 3. Standard Laravel para Infrastructure
**Http/** mantiene estructura estándar de Laravel para consistencia con framework.

## Dominios de Negocio Identificados

### Primary Domains (Core Business)

**ProductItem** - El corazón del sistema
- Lifecycle management (16 estados)
- Version tracking para re-quotes
- Specifications y configuraciones
- **Actions**: Create, Update, Requote + version tracking
- **DTOs**: Specialized para cada operación

**Project** - Contenedor y aggregación
- Project management y estado derivado
- Relationship con ProductItems
- **Actions**: Create, Update, Calculate status
- **DTOs**: Project-specific validation

### Secondary Domains (Supporting)

**User** - User management y roles
- Authentication y authorization
- Role-based permissions
- **Actions**: CRUD operations
- **DTOs**: User validation

**Financial** - Calculations y costing
- Cost calculations
- Invoice generation  
- Profit analysis
- **Actions**: Calculate, Generate, Analyze
- **DTOs**: Financial-specific data

**Logistics** (Future) - Shipping y importaciones
- Import shipment records
- Status tracking
- **Actions**: Create shipment, Track status
- **DTOs**: Logistics-specific

## Benefits de la Organización Simplificada

### 1. Domain Context Navigation
Developers pueden navegar fácilmente por contexto de negocio (ProductItem, Project, etc.) en lugar de por tipo técnico.

### 2. Team Ownership Clear
Cada dominio puede ser owned por diferentes team members o especialistas.

### 3. Cohesión Alta
Todos los components relacionados con un dominio (Actions, DTOs) están co-localizados.

### 4. Coupling Bajo
Dominios interactúan via Services compartidos, no directamente.

### 5. Laravel Standard
Mantiene patterns familiares de Laravel sin complejidad innecesaria.

## Ejemplos Prácticos

### Domain Context: ProductItem

```php
// Navegación intuitiva por contexto
app/Actions/ProductItem/RequoteProductItemAction.php
app/Data/ProductItem/RequoteProductItemData.php

// Service compartido
app/Services/ProductItemVersionService.php

// Model compartido
app/Models/ProductItem.php
app/Models/ProductItemVersion.php

// Enum compartido  
app/Enums/ProductItemStatus.php
```

### Cross-Domain Integration

```php
// ProjectStatusCalculatorService integra múltiples dominios
class ProjectStatusCalculatorService
{
    public function calculate(Project $project): ProjectStatus
    {
        // Usa ProductItems del domain ProductItem
        $productItems = $project->productItems;
        
        // Aplica business rules del Enum
        return ProjectStatus::calculateFromItems($productItems);
    }
}

// Action de Project usa Service cross-domain
class CalculateProjectStatusAction
{
    public function __construct(
        private ProjectStatusCalculatorService $calculator
    ) {}
    
    public function execute(Project $project): Project
    {
        $project->status = $this->calculator->calculate($project);
        $project->save();
        
        return $project;
    }
}
```

## Migration Strategy

### Phase 1: Core Domains (Actions + DTOs)
1. Create ProductItem domain structure
2. Create Project domain structure  
3. Move existing Actions to domain folders

### Phase 2: Support Domains
1. Create User domain structure
2. Create Financial domain structure
3. Cross-domain Services

### Phase 3: Refinement
1. Optimize cross-domain interactions
2. Ensure proper separation of concerns
3. Documentation and team onboarding

## Consecuencias

### Positivas
- ✅ **Domain Context**: Navigation by business logic instead of technical structure
- ✅ **Team Ownership**: Clear responsibility boundaries
- ✅ **Cohesión Alta**: Related components co-located
- ✅ **Simplicidad**: Standard Laravel patterns without over-engineering  
- ✅ **Escalabilidad**: Easy to add new domains

### Limitaciones (Aceptables)
- ⚠️ **Duplication Potential**: Some cross-domain logic might need to be shared via Services
- ⚠️ **Navigation Learning**: Team needs to learn new structure (but it's intuitive)

### Comparison: Simplified vs Event Sourcing Organization

| Aspecto | Event Sourcing Organization | Simplified Domain Organization |
|---------|------------------------------|-------------------------------|
| **Structure** | Aggregates/Events/Projectors + Domain Actions | Actions/Data by domain + shared Services |
| **Complexity** | High - Event Sourcing concepts | Low - Standard Laravel patterns |
| **Navigation** | Technical + Domain mixed | Pure domain-driven |
| **Learning curve** | Steep | Minimal |
| **Cross-domain** | Event-driven coordination | Service-based integration |

## Referencias

- **[ADR-006: Arquitectura CRUD Simplificada](adr006_crud_arquitectura_simplificada.md)** - Arquitectura base
- **[ADR-007: DTOs Simplificados](adr007_dtos_contratos_simplificados.md)** - DTOs por dominio
- **[ADR-008: Enums con Métodos](adr008_uso_enums_con_metodos.md)** - Business rules compartidas
- **[Arquitectura Integrada](../00_arquitectura_integrada.md)** - Especificación completa