# 1. Introducción y Objetivos (Introduction and Goals)

> **Propósito:** Establece el fundamento del proyecto, el "porqué" del sistema, y comunica la visión y los objetivos de calidad que guían la arquitectura.

---

## 1.1. Requerimientos del Sistema (Resumen)

El sistema **PromoSmart** debe resolver los puntos de dolor de un proceso de negocio actualmente manual, fragmentado y propenso a errores. Los requerimientos fundamentales son:

1.  **Digitalizar y Centralizar:** Crear una única fuente de verdad para toda la información de ventas, adquisiciones y logística, eliminando la dependencia de hojas de cálculo.
2.  **Automatizar y Agilizar:** Reducir el trabajo manual en la generación de cotizaciones, el seguimiento de proyectos y el cálculo de la rentabilidad.
3.  **Proporcionar Visibilidad:** Ofrecer dashboards e informes en tiempo real sobre el estado de los proyectos y las métricas financieras clave.
4.  **<PERSON><PERSON><PERSON><PERSON>:** Mantener un historial completo de los cambios y decisiones para facilitar la auditoría y la resolución de problemas.

## 1.2. Objetivos de Calidad

La arquitectura del sistema debe estar diseñada para cumplir los siguientes objetivos de calidad:

-   **Mantenibilidad:** El uso de una arquitectura limpia y patrones de diseño probados debe facilitar la corrección de errores y la adición de nuevas funcionalidades en el futuro.
-   **Rendimiento:** El sistema debe ser rápido y responsivo para el usuario final, con tiempos de carga de página inferiores a 3 segundos para las operaciones comunes.
-   **Fiabilidad y Disponibilidad:** El sistema debe ser robusto, con una disponibilidad superior al 99.5% durante el horario laboral, y contar con un plan sólido de copias de seguridad y recuperación.
-   **Usabilidad:** La interfaz de usuario debe ser intuitiva y eficiente, permitiendo que un nuevo analista sea productivo con un mínimo de formación.
-   **Seguridad:** Los datos de la empresa y de los clientes deben estar protegidos mediante controles de acceso basados en roles y cifrado de información sensible.

## 1.3. Stakeholders

| Rol / Actor | Interés Principal en la Arquitectura |
| :--- | :--- |
| **Gerencia / Dueños** | Visibilidad financiera, rentabilidad del proyecto, escalabilidad del negocio. |
| **Líder de Equipo** | Eficiencia operativa, distribución de la carga de trabajo, capacidad de supervisión. |
| **Analistas (Ventas, Adquisiciones, etc.)** | Un sistema rápido, intuitivo y que reduzca los errores manuales y facilite la colaboración. |
| **Equipo de Desarrollo** | Una base de código mantenible, testeable y bien documentada que permita evolucionar el sistema. |
