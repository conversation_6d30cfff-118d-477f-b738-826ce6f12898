# 10. Requisitos de Calidad (Quality Requirements)

> **Propósito:** Define los atributos de calidad que la arquitectura debe soportar. Estos son los requisitos no funcionales que determinan "qué tan bien" debe funcionar el sistema.

---

## 10.1. Árbol de Calidad

```mermaid
graph TD
    A(Calidad del Sistema PromoSmart) --> B(Fiabilidad)
    A --> C(Rendimiento y Eficiencia)
    A --> D(Usabilidad)
    A --> E(Seguridad)
    A --> F(Mantenibilidad)

    B --> B1(Disponibilidad: 99.5%)
    B --> B2(Recuperación ante Desastres: < 4h)
    B --> B3(Integridad de Datos)

    C --> C1(Tiempo de Respuesta: < 3s)
    C --> C2(Escalabilidad: hasta 200 usuarios)
    C --> C3(Manejo de Carga Concurrente: 50 usuarios)

    D --> D1(Curva de Aprendizaje: < 2h)
    D --> D2(Consistencia de la Interfaz)
    D --> D3(Accesibilidad WCAG 2.1)

    E --> E1(Control de Acceso Basado en Roles)
    E --> E2(Cifrado de Datos en Reposo y Tránsito)
    E --> E3(Auditoría de Acciones Críticas)

    F --> F1(Cobertura de Pruebas: > 80%)
    F --> F2(Adherencia a Estándares de Código)
    F --> F3(Despliegue Automatizado CI/CD)
```

## 10.2. Escenarios de Calidad Clave

A continuación se detallan algunos de los escenarios de calidad más importantes que la arquitectura debe satisfacer.

### Escenario 1: Rendimiento bajo carga
-   **Estímulo:** 30 analistas utilizando el sistema simultáneamente al final del trimestre para generar informes.
-   **Respuesta Esperada:** El tiempo de carga de las páginas no debe superar los 5 segundos. La generación de un informe de rentabilidad de un proyecto debe tardar menos de 20 segundos.
-   **Métrica:** Tiempo de respuesta promedio, uso de CPU y memoria del servidor.

### Escenario 2: Recuperación de la base de datos
-   **Estímulo:** Falla crítica del servidor de base de datos principal.
-   **Respuesta Esperada:** El sistema debe ser capaz de restaurar la operación completa desde la última copia de seguridad en menos de 1 hora. La pérdida de datos no debe ser superior a 24 horas.
-   **Métrica:** Tiempo de Recuperación (RTO), Punto de Recuperación (RPO).

### Escenario 3: Intento de acceso no autorizado
-   **Estímulo:** Un analista de ventas intenta acceder a las funciones de configuración financiera reservadas para el Analista Financiero.
-   **Respuesta Esperada:** El sistema debe denegar el acceso de forma segura y registrar el intento de acceso no autorizado en el log de auditoría.
-   **Métrica:** Tasa de éxito de denegación de acceso, completitud del log de auditoría.

### Escenario 4: Escalabilidad del código
-   **Estímulo:** Se requiere añadir un nuevo tipo de aprobación (ej. "Aprobación de Sostenibilidad") al ciclo de vida del producto.
-   **Respuesta Esperada:** Un desarrollador debe poder añadir el nuevo estado y su lógica asociada modificando un número limitado de archivos, sin afectar otras partes del sistema, gracias al uso del State Machine Pattern.
-   **Métrica:** Tiempo de desarrollo para la nueva funcionalidad, número de regresiones introducidas (idealmente cero).
