# 2. Restricciones (Constraints)

> **Propósito:** Documenta las decisiones y limitaciones, tanto técnicas como de negocio, que se deben respetar y que acotan el margen de maniobra para el diseño de la arquitectura.

---

## 2.1. Restricciones Técnicas

-   **Stack Tecnológico Definido:** La arquitectura debe basarse en el stack tecnológico ya decidido: **Laravel 12** para el backend y **Livewire/Filament** para el frontend (ver [ADR-004](adrs/adr004_adopcion_tall_stack_filament.md)).
-   **Base de Datos Relacional:** Se debe utilizar **PostgreSQL 12+** como motor de base de datos principal, según la decisión formalizada en [ADR-005](adrs/adr005_eleccion_postgresql.md).
-   **Arquitectura Monolítica:** La solución debe ser una aplicación monolítica, no un sistema de microservicios, para agilizar el desarrollo y el despliegue inicial.
-   **Despliegue en Servidores Linux:** La infraestructura de despliegue debe estar basada en servidores Linux.

## 2.2. Restricciones Organizacionales y de Negocio

-   **Idioma del Equipo:** El equipo de desarrollo y de negocio es principalmente hispanohablante. La documentación conceptual y la comunicación deben ser en **español**.
-   **Convenciones de Código:** Para mantener la compatibilidad con el ecosistema global, todo el código fuente (clases, variables, etc.) debe escribirse en **inglés**, según lo definido en la política de idioma (ver ADR-003).
-   **Presupuesto y Plazos:** El desarrollo y la implementación de la Versión 1.0 deben realizarse dentro de los marcos de tiempo y presupuesto definidos por la gerencia (aunque no se detallan aquí, son una restricción implícita).
-   **Modelo de Negocio `Made-to-Order`:** La arquitectura no necesita contemplar la gestión de inventario físico, ya que el modelo de negocio es estrictamente bajo pedido.

## 2.3. Restricciones Políticas o Legales

-   **Regulaciones de Facturación en Chile:** El sistema debe ser capaz de generar datos que cumplan con los requisitos del Servicio de Impuestos Internos (SII) de Chile, aunque la integración directa no está en el alcance de la V1.0.
-   **Protección de Datos:** Se deben seguir las mejores prácticas para la protección de datos de clientes y proveedores.
