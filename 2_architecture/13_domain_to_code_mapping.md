# 13. Implementación Técnica - Dominio a Código

> **Propósito:** Implementación técnica detallada del mapeo entre conceptos de dominio y código, servicios, modelos y enums.

> **Nota:** Para conceptos de negocio y mapeo conceptual, ver [1_overview/08_Diccionario_Negocio_Sistema.md](../1_overview/08_Diccionario_Negocio_Sistema.md)

---

## Entidades Principales y sus Gestores

| Concepto (Español) | Modelo (Inglés) | Tabla BD | Servicios Gestores Clave |
| :--- | :--- | :--- | :--- |
| Proyecto | `Project` | `projects` | `ProjectStatusCalculatorService`, `ProjectProfitabilityService` |
| Ítem de Producto | `ProductItem` | `product_items` | `StateTransitionService`, `StateOrchestrationService`, `ProductSpecificationValidator` |
| Registro de Envío | `ImportShipmentRecord` | `import_shipment_records` | `CrossDomainStateSyncService` |
| Cliente | `Customer` | `customers` | N/A (CRUD Simple) |
| Proveedor | `Supplier` | `suppliers` | N/A (CRUD Simple) |
| Usuario | `User` | `users` | N/A (CRUD Simple) |
| **Categoría de Producto** | `ProductCategory` | `product_categories` | N/A (CRUD Simple) |
| **Subcategoría de Producto** | `ProductSubcategory` | `product_subcategories` | N/A (CRUD Simple) |
| **Estructura de Especificación** | `ProductSpecificationStructure` | `product_specification_structures` | `ProductSpecificationValidator` |

---

## Conceptos Arquitectónicos Clave y su Implementación

| Concepto | Implementación | Descripción |
| :--- | :--- | :--- |
| **Línea Base del Proyecto** | Columna JSON `item_baseline` en `projects` | Se genera una única vez a través de `CreateProjectBaselineAction` cuando el proyecto se confirma. Contiene un snapshot inmutable de los ítems para análisis financiero. Ver [3. Modelo de Negocio - Control de Gestión](../../1_overview/03_Modelo_de_Negocio.md#5-control-de-gestión-línea-base-vs-realidad) para reglas de negocio. |
| **Versionado de Entidades** | Tablas `*_versions` | Cada entidad con ciclo de vida (`Project`, `ProductItem`, `ImportShipmentRecord`) tiene una tabla de versiones para auditoría. El `VersioningService` centraliza la creación de estos registros. |
| **Roles de Usuario** | Columna `role` en `users` | Un `Enum` (`UserRole`) define los roles. No hay tabla `roles` ni pivotes, simplificando la gestión. |
| **Máquina de Estados** | Servicios dedicados | La lógica de transición se centraliza en `StateTransitionService` (valida) y `StateOrchestrationService` (ejecuta), desacoplando la lógica de los modelos. |
| **Valores Monetarios** | Value Object `Money` | Todos los montos monetarios se representan con un objeto `Money` que encapsula cantidad y divisa. Se persiste en la BD a través de un `MoneyCast` personalizado. |
| **Taxonomía de Productos** | Jerarquía de Modelos | Sistema de clasificación jerárquica: `ProductCategory` → `ProductSubcategory` → `ProductSpecificationStructure`. Las especificaciones se validan según la taxonomía a través del `ProductSpecificationValidator`. |
| **Especificaciones de Producto** | Campo JSON en `ProductItem` | Las especificaciones se almacenan como JSON validado contra la estructura definida por la subcategoría del producto. |
| **Validación por Categoría** | `CategorySpecificValidator` | Validaciones específicas por categoría (Merchandising, Material PDV, Textiles) con reglas de negocio diferenciadas. |

---

## Mapeo de Artefactos de Negocio

| Artefacto (Español) | Implementación | Tipo en `ProjectArtifactType` Enum |
| :--- | :--- | :--- |
| Cotización para el Cliente | Modelo Polimórfico `ProjectArtifact` | `customer_quotation` |
| Orden de Compra del Cliente | Modelo Polimórfico `ProjectArtifact` | `customer_purchase_order` |
| Orden de Compra al Proveedor | Modelo Polimórfico `ProjectArtifact` | `supplier_purchase_order` |
| Maqueta Virtual | Modelo Dedicado `VirtualMockup` | `virtual_mockup` |
| Muestra de Preproducción | Modelo Dedicado `PreProductionSample` | `pre_production_sample` |
| Factura Proforma | Modelo Polimórfico `ProjectArtifact` | `proforma_invoice` |

---

## Mapeo de Estados de `ProductItem`

> **Referencia de Negocio:** Ver [3. Modelo de Negocio - Ciclo de Vida del Producto](../../1_overview/03_Modelo_de_Negocio.md#2-el-motor-del-proceso-el-ciclo-de-vida-del-producto) para el contexto conceptual.

| Estado Conceptual (Español) | Caso Enum `ProductItemStatus` (`PascalCase`) | Valor Backed (`snake_case`) |
| :--- | :--- | :--- |
| Borrador | `Draft` | `'draft'` |
| Listo para Sourcing | `ReadyForSourcing` | `'ready_for_sourcing'` |
| Sourcing en Progreso | `SourcingInProgress` | `'sourcing_in_progress'` |
| Pendiente de Revisión Interna | `InternalReviewPending` | `'internal_review_pending'` |
| Requiere Revisión | `RevisionRequested` | `'revision_requested'` |
| Cotizado al Cliente | `QuotedToCustomer` | `'quoted_to_customer'` |
| Revisión Solicitada por Cliente | `CustomerRevisionRequested` | `'customer_revision_requested'` |
| Pendiente Aprobación de Maqueta Virtual | `PendingVmApproval` | `'pending_vm_approval'` |
| Maqueta Virtual Rechazada | `VmRejected` | `'vm_rejected'` |
| Pendiente Aprobación de Muestra Física | `PendingPpsApproval` | `'pending_pps_approval'` |
| Muestra Física Rechazada | `PpsRejected` | `'pps_rejected'` |
| Pendiente Aprobación de Factura Proforma | `PendingPiApproval` | `'pending_pi_approval'` |
| Listo para Producción | `ReadyForProduction` | `'ready_for_production'` |
| En Producción | `InProduction` | `'in_production'` |
| Pendiente de Envío | `PendingShipment` | `'pending_shipment'` |
| En Tránsito Internacional | `InternationalTransit` | `'international_transit'` |
| En Despacho de Aduanas | `CustomsClearance` | `'customs_clearance'` |
| En Tránsito Doméstico | `DomesticTransit` | `'domestic_transit'` |
| Entregado | `Delivered` | `'delivered'` |

---

## Mapeo de Taxonomía de Productos

| Concepto (Español) | Implementación Técnica | Descripción |
| :--- | :--- | :--- |
| **Categorías Principales** | `ProductCategory` | 3 categorías base: Merchandising, Material PDV, Textiles |
| Merchandising | `name = 'Merchandising'` | Productos promocionales de marca, regalos corporativos |
| Material PDV | `name = 'Material PDV'` | Artículos para punto de venta y exhibición |
| Textiles | `name = 'Textiles'` | Productos cuya característica principal es construcción en tela |

### Subcategorías por Categoría

#### Merchandising
| Subcategoría (Español) | `slug` Técnico | Descripción |
| :--- | :--- | :--- |
| Artículos para Beber | `drinking_items` | Tazones, botellas de agua, vasos térmicos |
| Instrumentos de Escritura | `writing_instruments` | Lápices, portaminas, destacadores |
| Vestuario y Accesorios | `apparel_accessories` | Poleras, jockeys, lanyards, bolsos |
| Accesorios Tecnológicos | `tech_accessories` | Pendrives, baterías externas, soportes |
| Artículos de Oficina | `office_desk` | Cuadernos, mousepads, calendarios |
| Estilo de Vida y Aire Libre | `lifestyle_outdoor` | Llaveros, paraguas, multiherramientas |
| Premios y Reconocimientos | `awards_recognition` | Galvanos, trofeos |

#### Material PDV
| Subcategoría (Español) | `slug` Técnico | Descripción |
| :--- | :--- | :--- |
| Señalética y Banners | `signage_banners` | Pósteres, pendones roller, banderas |
| Banderas Miniatura | `miniature_flags` | Banderas tipo cuchillo, escritorio |
| Soportes y Exhibición | `display_stands` | Glorificadores, portafolletos, displays |
| Materiales Impresos | `printed_materials` | Volantes, folletos, habladores de mesa |
| Mobiliario Promocional | `promotional_furniture` | Mesones, carpas, vitrinas |
| Pantallas Digitales | `digital_displays` | Pantallas digitales y kioscos interactivos |

#### Textiles
| Subcategoría (Español) | `slug` Técnico | Descripción |
| :--- | :--- | :--- |
| Vestuario/Prendas | `garments` | Poleras, chaquetas, uniformes de calidad |
| Textiles para el Hogar | `home_textiles` | Toallas, mantas, delantales, fundas |
| Bolsos de Tela | `fabric_bags` | Bolsos y soluciones de transporte en tela |
| Textiles Técnicos | `technical_textiles` | Telas de alto rendimiento |
| Materiales Crudos | `raw_materials` | Rollos de tela para procesamiento |

### Atributos de Especificación por Categoría

| Categoría | Atributos Específicos | Descripción |
| :--- | :--- | :--- |
| **Merchandising** | `capacity_volume`, `maximum_load_capacity`, `material_certifications` | Volúmenes, capacidades de carga, certificaciones |
| **Material PDV** | `assembly_required`, `structural_design`, `folded_collapsed_dimensions`, `mounting_requirements` | Ensamblaje, diseño estructural, dimensiones plegadas |
| **Textiles** | `gsm_value`, `fit_style`, `available_sizes`, `care_instructions`, `garment_construction` | Gramaje, calce, tallas, cuidado, confección |

---

## Referencias de Taxonomía

- **Documento Completo**: [14. Taxonomía de Productos](./14_taxonomia_productos.md)
- **Archivo Fuente**: [`taxonomia.md`](../taxonomia.md)
- **Servicios de Validación**: `ProductSpecificationValidator`, `CategorySpecificValidator`
