# 10. Especificación de Sincronización Cross-Domain de Estados

> **Propósito:** Especificación técnica detallada de la sincronización automática de estados entre ProductItem, ImportShipmentRecord y Project, incluyendo reglas de derivación, resolución de conflictos y implementación de servicios.

---

## Contenido

1. [Arquitectura de Sincronización](#arquitectura-de-sincronización)
2. [Matriz de Sincronización ImportShipmentRecord ↔ ProductItem](#matriz-de-sincronización-importshipmentrecord--productitem)
3. [Reglas de Derivación Project ← ProductItems](#reglas-de-derivación-project--productitems)
4. [Servicios de Orquestación](#servicios-de-orquestación)
5. [Algoritmos de Resolución de Conflictos](#algoritmos-de-resolución-de-conflictos)
6. [Casos Edge y Excepciones](#casos-edge-y-excepciones)
7. [Implementación Técnica](#implementación-técnica)

---

## Arquitectura de Sincronización

### Principios de Diseño

```mermaid
graph TD
    subgraph "Estado Maestro"
        ISR[ImportShipmentRecord]
        ISR --> |"Sincronización Automática"| PI1[ProductItem 1]
        ISR --> |"Sincronización Automática"| PI2[ProductItem 2]
        ISR --> |"Sincronización Automática"| PIN[ProductItem N]
    end
    
    subgraph "Estado Derivado"
        PI1 --> |"Cálculo Agregado"| PROJ[Project Status]
        PI2 --> |"Cálculo Agregado"| PROJ
        PIN --> |"Cálculo Agregado"| PROJ
    end
    
    subgraph "Servicios de Orquestación"
        CSS[CrossDomainStateSyncService]
        PSC[ProjectStatusCalculatorService]
        SOS[StateOrchestrationService]
    end
    
    CSS --> ISR
    PSC --> PROJ
    SOS --> CSS
    SOS --> PSC
```

### Jerarquía de Estados

1. **ImportShipmentRecord**: Estado maestro para fases logísticas
2. **ProductItem**: Estado sincronizado + estados propios de negocio
3. **Project**: Estado derivado agregado de todos sus ProductItems

---

## Matriz de Sincronización ImportShipmentRecord ↔ ProductItem

### Sincronización Automática (Maestro → Esclavo)

| Estado ImportShipmentRecord | Acción en ProductItems | Momento | Condiciones | Excepciones |
|---|---|---|---|---|
| **Planning** | Sin cambio | N/A | ProductItems deben estar en `InProduction` | Bloquear si ProductItems en estados anteriores |
| **PickupScheduled** | Bloquear modificaciones | Inmediato | Consolidación finalizada | Solo Import Analyst puede modificar |
| **InTransit** | **→ InternationalTransit** | Inmediato | Todos los ProductItems del envío | Ninguna - Sincronización obligatoria |
| **ArrivedPort** | Sin cambio | N/A | Preparación para aduanas | Mantener `InternationalTransit` |
| **CustomsClearance** | **→ CustomsClearance** | Inmediato | Proceso aduanero iniciado | Ninguna - Sincronización obligatoria |
| **Cleared** | Sin cambio | N/A | Productos liberados | Mantener `CustomsClearance` |
| **DomesticDelivery** | Sin cambio | N/A | Entrega en proceso | Mantener `CustomsClearance` |
| **Delivered** | **→ Delivered** | Inmediato | Entrega completada | Ninguna - Sincronización obligatoria |

### Validaciones Pre-Sincronización

```php
// Pseudocódigo de validación
class CrossDomainStateSyncService
{
    public function validateSyncPreconditions(ImportShipmentRecord $shipment, ImportShipmentStatus $newStatus): bool
    {
        return match($newStatus) {
            ImportShipmentStatus::Planning => $this->validatePlanningPreconditions($shipment),
            ImportShipmentStatus::InTransit => $this->validateInTransitPreconditions($shipment),
            ImportShipmentStatus::CustomsClearance => $this->validateCustomsPreconditions($shipment),
            ImportShipmentStatus::Delivered => $this->validateDeliveryPreconditions($shipment),
            default => true
        };
    }
    
    private function validateInTransitPreconditions(ImportShipmentRecord $shipment): bool
    {
        // Todos los ProductItems deben estar en InProduction o posterior
        return $shipment->productItems->every(fn($item) => 
            $item->status->isAtLeast(ProductItemStatus::InProduction)
        );
    }
}
```

---

## Reglas de Derivación Project ← ProductItems

### Algoritmo de Cálculo de ProjectStatus

```php
class ProjectStatusCalculatorService
{
    private array $statusPrecedence = [
        'CANCELLED' => 1,           // Máxima prioridad
        'REVISION_REQUESTED' => 2,
        'DRAFT' => 3,
        'SOURCING_IN_PROGRESS' => 4,
        'QUOTED_TO_CUSTOMER' => 5,
        'PENDING_APPROVAL' => 6,
        'IN_PRODUCTION' => 7,
        'IN_TRANSIT' => 8,
        'DELIVERED' => 9            // Mínima prioridad
    ];
    
    public function calculate(Project $project): ProjectStatus
    {
        $items = $project->productItems;
        
        if ($items->isEmpty()) {
            return ProjectStatus::DRAFT;
        }
        
        // Estados críticos tienen precedencia absoluta
        if ($items->contains('status', ProductItemStatus::CANCELLED)) {
            return ProjectStatus::CANCELLED;
        }
        
        if ($items->contains('status', ProductItemStatus::REVISION_REQUESTED)) {
            return ProjectStatus::REVISION_REQUESTED;
        }
        
        // Lógica de mayoría para estados normales
        return $this->calculateByMajority($items);
    }
    
    private function calculateByMajority(Collection $items): ProjectStatus
    {
        $statusCounts = $items->groupBy('status')->map->count();
        $totalItems = $items->count();
        
        // Si 80%+ están en el mismo estado, usar ese estado
        foreach ($statusCounts as $status => $count) {
            if ($count / $totalItems >= 0.8) {
                return $this->mapToProjectStatus($status);
            }
        }
        
        // Si no hay mayoría clara, usar el estado más crítico presente
        $currentStatuses = $items->pluck('status')->unique();
        return $this->findMostCriticalStatus($currentStatuses);
    }
}
```

### Matriz de Mapeo ProductItemStatus → ProjectStatus

| ProductItemStatus | ProjectStatus | Condición |
|---|---|---|
| `Draft`, `ReadyForSourcing`, `SourcingInProgress` | `SOURCING` | Mayoría en fase de sourcing |
| `QuotedToCustomer`, `CustomerRevisionRequested` | `QUOTED` | Mayoría cotizada |
| `PendingVmApproval`, `PendingPpsApproval` | `PENDING_APPROVAL` | Mayoría en aprobaciones |
| `InProduction` | `IN_PRODUCTION` | Mayoría en producción |
| `InternationalTransit`, `CustomsClearance` | `IN_TRANSIT` | Mayoría en logística |
| `Delivered` | `COMPLETED` | Todos entregados |
| `CANCELLED` | `CANCELLED` | Al menos uno cancelado |

---

## Servicios de Orquestación

### StateOrchestrationService - Coordinador Principal

```php
class StateOrchestrationService
{
    public function __construct(
        private CrossDomainStateSyncService $syncService,
        private ProjectStatusCalculatorService $projectCalculator,
        private StateTransitionService $transitionService
    ) {}
    
    public function orchestrateShipmentTransition(
        ImportShipmentRecord $shipment, 
        ImportShipmentStatus $newStatus,
        User $user
    ): void {
        DB::transaction(function() use ($shipment, $newStatus, $user) {
            // 1. Validar transición del shipment
            if (!$this->transitionService->isShipmentTransitionAllowed($shipment, $newStatus, $user)) {
                throw new InvalidTransitionException("Shipment transition not allowed");
            }
            
            // 2. Actualizar ImportShipmentRecord
            $shipment->update(['status' => $newStatus]);
            
            // 3. Sincronizar ProductItems si es necesario
            if ($this->syncService->requiresProductItemSync($newStatus)) {
                $this->syncService->syncProductItems($shipment, $newStatus);
            }
            
            // 4. Recalcular estados de proyectos afectados
            $affectedProjects = $this->getAffectedProjects($shipment);
            foreach ($affectedProjects as $project) {
                $newProjectStatus = $this->projectCalculator->calculate($project);
                $project->update(['status' => $newProjectStatus]);
            }
            
            // 5. Disparar eventos de negocio
            $this->dispatchBusinessEvents($shipment, $newStatus);
        });
    }
}
```

### CrossDomainStateSyncService - Sincronización Específica

```php
class CrossDomainStateSyncService
{
    public function syncProductItems(ImportShipmentRecord $shipment, ImportShipmentStatus $shipmentStatus): void
    {
        $targetProductItemStatus = $this->mapShipmentToProductItemStatus($shipmentStatus);
        
        if (!$targetProductItemStatus) {
            return; // No requiere sincronización
        }
        
        $shipment->productItems()->update([
            'status' => $targetProductItemStatus,
            'updated_at' => now(),
            'last_sync_at' => now()
        ]);
        
        // Log de sincronización masiva
        Log::info("Synced {$shipment->productItems()->count()} ProductItems to {$targetProductItemStatus->value}", [
            'shipment_id' => $shipment->id,
            'shipment_status' => $shipmentStatus->value,
            'product_item_status' => $targetProductItemStatus->value
        ]);
    }
    
    private function mapShipmentToProductItemStatus(ImportShipmentStatus $shipmentStatus): ?ProductItemStatus
    {
        return match($shipmentStatus) {
            ImportShipmentStatus::InTransit => ProductItemStatus::InternationalTransit,
            ImportShipmentStatus::CustomsClearance => ProductItemStatus::CustomsClearance,
            ImportShipmentStatus::Delivered => ProductItemStatus::Delivered,
            default => null
        };
    }
}
```

---

## Algoritmos de Resolución de Conflictos

### Conflictos de Sincronización

| Tipo de Conflicto | Descripción | Algoritmo de Resolución | Ejemplo |
|---|---|---|---|
| **ProductItem Adelantado** | ProductItem en estado posterior al ImportShipmentRecord | Sincronizar hacia atrás (ImportShipmentRecord gana) | ProductItem en `Delivered` pero ImportShipmentRecord en `InTransit` |
| **ProductItem Atrasado** | ProductItem en estado anterior al esperado | Permitir pero alertar | ProductItem en `InProduction` cuando debería estar en `InternationalTransit` |
| **Proyecto Inconsistente** | ProductItems del mismo proyecto en diferentes envíos | Calcular por mayoría ponderada | 70% de ProductItems en `InTransit`, 30% en `Delivered` |
| **Estado Bloqueado** | ProductItem no puede cambiar por reglas de negocio | Marcar excepción y continuar | ProductItem con `CANCELLED` no se sincroniza |

### Implementación de Resolución

```php
class ConflictResolutionService
{
    public function resolveProductItemConflict(
        ProductItem $item,
        ProductItemStatus $expectedStatus,
        ImportShipmentRecord $shipment
    ): ConflictResolution {

        $currentStatus = $item->status;
        $conflict = $this->detectConflictType($currentStatus, $expectedStatus);

        return match($conflict) {
            ConflictType::ITEM_AHEAD => $this->resolveItemAhead($item, $expectedStatus, $shipment),
            ConflictType::ITEM_BEHIND => $this->resolveItemBehind($item, $expectedStatus),
            ConflictType::BLOCKED_STATE => $this->resolveBlockedState($item, $expectedStatus),
            ConflictType::NO_CONFLICT => ConflictResolution::proceed()
        };
    }

    private function resolveItemAhead(ProductItem $item, ProductItemStatus $expectedStatus, ImportShipmentRecord $shipment): ConflictResolution
    {
        // ImportShipmentRecord es maestro - sincronizar hacia atrás
        Log::warning("ProductItem ahead of shipment - forcing sync", [
            'product_item_id' => $item->id,
            'current_status' => $item->status->value,
            'expected_status' => $expectedStatus->value,
            'shipment_id' => $shipment->id
        ]);

        return ConflictResolution::forceSyncBackward($expectedStatus);
    }

    private function resolveItemBehind(ProductItem $item, ProductItemStatus $expectedStatus): ConflictResolution
    {
        // Permitir pero alertar - puede ser válido
        Log::info("ProductItem behind expected status - allowing", [
            'product_item_id' => $item->id,
            'current_status' => $item->status->value,
            'expected_status' => $expectedStatus->value
        ]);

        return ConflictResolution::allowWithAlert();
    }
}
```

---

## Casos Edge y Excepciones

### Escenarios Complejos

#### Caso 1: Envío Parcial con Múltiples Proyectos

**Situación:** ImportShipmentRecord con ProductItems de 3 proyectos diferentes, uno de los proyectos se cancela durante el tránsito.

**Resolución:**
```php
class PartialCancellationHandler
{
    public function handleProjectCancellation(Project $cancelledProject, ImportShipmentRecord $shipment): void
    {
        // 1. Identificar ProductItems del proyecto cancelado en el envío
        $cancelledItems = $shipment->productItems()
            ->where('project_id', $cancelledProject->id)
            ->get();

        // 2. Marcar como cancelados pero mantener en el envío para tracking
        $cancelledItems->each(function($item) {
            $item->update([
                'status' => ProductItemStatus::CANCELLED,
                'cancellation_reason' => 'Project cancelled during transit',
                'maintain_in_shipment' => true // Flag especial
            ]);
        });

        // 3. Recalcular costos del envío
        $this->recalculateShipmentCosts($shipment, $cancelledItems);

        // 4. Notificar a stakeholders
        $this->notifyPartialCancellation($shipment, $cancelledProject, $cancelledItems);
    }
}
```

#### Caso 2: Problema Aduanero con Retroceso

**Situación:** ImportShipmentRecord debe retroceder de `CustomsClearance` a `ArrivedPort` por documentación incorrecta.

**Resolución:**
```php
class CustomsRollbackHandler
{
    public function handleCustomsRollback(ImportShipmentRecord $shipment, string $reason): void
    {
        DB::transaction(function() use ($shipment, $reason) {
            // 1. Retroceder ImportShipmentRecord
            $shipment->update([
                'status' => ImportShipmentStatus::ArrivedPort,
                'customs_rollback_reason' => $reason,
                'customs_rollback_at' => now()
            ]);

            // 2. NO retroceder ProductItems - mantener en CustomsClearance
            // Los productos siguen en proceso aduanero, solo el envío retrocede administrativamente

            // 3. Registrar costos adicionales
            $this->registerAdditionalCosts($shipment, 'customs_rollback');

            // 4. Crear tareas de seguimiento
            $this->createFollowUpTasks($shipment, $reason);
        });
    }
}
```

#### Caso 3: Entrega Parcial Múltiple

**Situación:** ImportShipmentRecord en `DomesticDelivery` con entregas escalonadas en 5 ciudades.

**Resolución:**
```php
class PartialDeliveryTracker
{
    public function trackPartialDelivery(ImportShipmentRecord $shipment): void
    {
        $deliveryStatus = $this->calculateDeliveryCompleteness($shipment);

        if ($deliveryStatus->isFullyDelivered()) {
            // Todas las entregas completadas - cambiar estado del envío
            $shipment->update(['status' => ImportShipmentStatus::Delivered]);

            // Sincronizar todos los ProductItems
            $this->syncService->syncProductItems($shipment, ImportShipmentStatus::Delivered);
        } else {
            // Entregas parciales - mantener en DomesticDelivery
            $this->updatePartialDeliveryStatus($shipment, $deliveryStatus);
        }
    }

    private function calculateDeliveryCompleteness(ImportShipmentRecord $shipment): DeliveryStatus
    {
        $totalItems = $shipment->productItems()->count();
        $deliveredItems = $shipment->productItems()
            ->where('delivery_confirmed_at', '!=', null)
            ->count();

        return new DeliveryStatus(
            total: $totalItems,
            delivered: $deliveredItems,
            percentage: ($deliveredItems / $totalItems) * 100
        );
    }
}
```

---

## Implementación Técnica

### Event-Driven Architecture

```php
// Events disparados por cambios de estado
class ImportShipmentStatusChanged
{
    public function __construct(
        public ImportShipmentRecord $shipment,
        public ImportShipmentStatus $oldStatus,
        public ImportShipmentStatus $newStatus,
        public User $user
    ) {}
}

// Listeners para sincronización automática
class SyncProductItemsOnShipmentChange
{
    public function handle(ImportShipmentStatusChanged $event): void
    {
        $this->stateOrchestrationService->orchestrateShipmentTransition(
            $event->shipment,
            $event->newStatus,
            $event->user
        );
    }
}
```

### Model Observers

```php
class ImportShipmentRecordObserver
{
    public function updating(ImportShipmentRecord $shipment): void
    {
        if ($shipment->isDirty('status')) {
            $oldStatus = ImportShipmentStatus::from($shipment->getOriginal('status'));
            $newStatus = $shipment->status;

            // Validar transición antes de guardar
            if (!$this->stateTransitionService->isShipmentTransitionAllowed($shipment, $newStatus, auth()->user())) {
                throw new InvalidTransitionException("Invalid shipment status transition");
            }
        }
    }

    public function updated(ImportShipmentRecord $shipment): void
    {
        if ($shipment->wasChanged('status')) {
            event(new ImportShipmentStatusChanged(
                $shipment,
                ImportShipmentStatus::from($shipment->getOriginal('status')),
                $shipment->status,
                auth()->user()
            ));
        }
    }
}
```

### Comandos Artisan para Mantenimiento

```php
// Comando para detectar y corregir inconsistencias
class DetectStateInconsistencies extends Command
{
    protected $signature = 'states:detect-inconsistencies {--fix : Fix detected inconsistencies}';

    public function handle(): void
    {
        $inconsistencies = $this->detectInconsistencies();

        $this->info("Found {$inconsistencies->count()} inconsistencies");

        if ($this->option('fix')) {
            $this->fixInconsistencies($inconsistencies);
        }
    }

    private function detectInconsistencies(): Collection
    {
        return ImportShipmentRecord::with('productItems')
            ->get()
            ->filter(function($shipment) {
                return $this->hasInconsistentProductItems($shipment);
            });
    }
}
```

---

## Referencias

- **Documentos relacionados:**
  - [09_Especificacion_Maquina_Estados_ProductItem.md](../1_overview/09_Especificacion_Maquina_Estados_ProductItem.md) → Estados de ProductItem
  - [11_Especificacion_Maquina_Estados_ImportShipmentRecord.md](../1_overview/11_Especificacion_Maquina_Estados_ImportShipmentRecord.md) → Estados de ImportShipmentRecord
  - [ADR-010: Gestión de Estados con Servicio Dedicado](adrs/adr010_gestion_estados_con_servicio.md) → Patrón de servicios
- **Implementación técnica:** Ver `05_bloques_construccion.md` para detalles de implementación
- **Servicios clave:** `StateOrchestrationService`, `CrossDomainStateSyncService`, `ProjectStatusCalculatorService`
