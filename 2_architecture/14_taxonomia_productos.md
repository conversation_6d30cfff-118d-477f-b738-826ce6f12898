# 14. Implementación Técnica de Taxonomía de Productos

> **Propósito:** Implementación técnica de la taxonomía de productos, servicios de validación y mapeo entre modelos Laravel y base de datos.

> **Nota:** Para conceptos de negocio y clasificación de productos, ver [1_overview/04_Tipos_de_Productos.md](../1_overview/04_Tipos_de_Productos.md)

---

## Contenido

1. [Mapeo a Implementación Técnica](#mapeo-a-implementación-técnica)
2. [Estructura de Especificaciones](#estructura-de-especificaciones)
3. [Validación y Business Rules](#validación-y-business-rules)
4. [Integración con el Sistema](#integración-con-el-sistema)
5. [Servicios y Validadores](#servicios-y-validadores)

---

## Mapeo a Implementación Técnica

### Jerarquía de Modelos

```
ProductCategory (3 categorías principales)
    ↓
ProductSubcategory (múltiples por categoría)
    ↓
ProductSpecificationStructure (1 por subcategoría)
    ↓
ProductItem.specifications (JSON validado)
```

### Correspondencia Técnica

| Concepto (Taxonomía) | Modelo Laravel | Tabla BD | Descripción |
|---|---|---|---|
| **Categoría de Producto** | `ProductCategory` | `product_categories` | Merchandising, Material PDV, Textiles |
| **Subcategoría de Producto** | `ProductSubcategory` | `product_subcategories` | Clasificación específica dentro de categoría |
| **Estructura de Especificación** | `ProductSpecificationStructure` | `product_specification_structures` | Plantilla de atributos por subcategoría |
| **Especificaciones del Producto** | `ProductItem.specifications` | Campo JSON en `product_items` | Datos específicos del producto |

---

## Mapeo a Implementación Técnica

### Jerarquía de Modelos

```
ProductCategory (3 categorías principales)
    ↓
ProductSubcategory (múltiples por categoría)
    ↓
ProductSpecificationStructure (1 por subcategoría)
    ↓
ProductItem.specifications (JSON validado)
```

### Correspondencia Técnica

| Concepto (Taxonomía) | Modelo Laravel | Tabla BD | Descripción |
|---|---|---|---|
| **Categoría de Producto** | `ProductCategory` | `product_categories` | Merchandising, Material PDV, Textiles |
| **Subcategoría de Producto** | `ProductSubcategory` | `product_subcategories` | Clasificación específica dentro de categoría |
| **Estructura de Especificación** | `ProductSpecificationStructure` | `product_specification_structures` | Plantilla de atributos por subcategoría |
| **Especificaciones del Producto** | `ProductItem.specifications` | Campo JSON en `product_items` | Datos específicos del producto |

### Enums y Constantes Técnicas

```php
// Categorías principales
enum ProductCategoryEnum: string
{
    case Merchandising = 'merchandising';
    case MaterialPDV = 'material_pdv';
    case Textiles = 'textiles';
}

// Subcategorías por slug técnico
enum ProductSubcategoryEnum: string
{
    // Merchandising
    case DrinkingItems = 'drinking_items';
    case WritingInstruments = 'writing_instruments';
    case ApparelAccessories = 'apparel_accessories';
    case TechAccessories = 'tech_accessories';
    case OfficeDesk = 'office_desk';
    case LifestyleOutdoor = 'lifestyle_outdoor';
    case AwardsRecognition = 'awards_recognition';
    
    // Material PDV
    case SignageBanners = 'signage_banners';
    case MiniatureFlags = 'miniature_flags';
    case DisplayStands = 'display_stands';
    case PrintedMaterials = 'printed_materials';
    case PromotionalFurniture = 'promotional_furniture';
    case DigitalDisplays = 'digital_displays';
    
    // Textiles
    case Garments = 'garments';
    case HomeTextiles = 'home_textiles';
    case FabricBags = 'fabric_bags';
    case TechnicalTextiles = 'technical_textiles';
    case RawMaterials = 'raw_materials';
}

---

## Estructura de Especificaciones

### ProductSpecificationStructure

Cada subcategoría tiene asociada una estructura que define:

```php
class ProductSpecificationStructure extends Model
{
    protected $fillable = [
        'product_subcategory_id',
        'required_attributes',     // JSON array de campos obligatorios
        'optional_attributes',     // JSON array de campos opcionales  
        'validation_rules',        // JSON array de reglas de validación
        'category_specific_rules', // JSON array de reglas específicas por categoría
    ];

    protected $casts = [
        'required_attributes' => 'array',
        'optional_attributes' => 'array',
        'validation_rules' => 'array',
        'category_specific_rules' => 'array',
    ];
}
```

### Dimensiones de Especificación por Categoría

#### Atributos Comunes (Todas las Categorías)
```json
{
  "basic_info": [
    "official_product_name",
    "short_marketing_description",
    "target_audience"
  ],
  "core_attributes": [
    "primary_material",
    "overall_external_dimensions", 
    "net_weight"
  ],
  "branding": [
    "printing_methods",
    "imprint_locations",
    "maximum_printable_area"
  ],
  "packaging": [
    "unit_packaging_description",
    "master_carton_dimensions",
    "units_per_master_carton"
  ],
  "commercial": [
    "exw_unit_cost",
    "moq_per_sku",
    "production_lead_time"
  ]
}
```

#### Atributos Específicos por Categoría

**Merchandising**:
```json
{
  "specific_attributes": [
    "capacity_volume",        // Para artículos de beber
    "maximum_load_capacity",  // Para bolsos, soportes
    "material_certifications" // BPA-free, grado alimenticio
  ]
}
```

**Material PDV**:
```json
{
  "specific_attributes": [
    "assembly_required",
    "structural_design",      // pop-up, roller, tipo A
    "folded_collapsed_dimensions",
    "mounting_requirements"
  ]
}
```

**Textiles**:
```json
{
  "specific_attributes": [
    "gsm_value",             // Gramaje de la tela
    "fit_style",             // Corte, calce
    "available_sizes",       // Rango de tallas
    "care_instructions",     // Instrucciones de cuidado
    "garment_construction"   // Detalles de confección
  ]
}
```

---

## Validación y Business Rules

### Validación por Niveles

#### 1. Validación Estructural
```php
class ProductSpecificationValidator
{
    public function validateStructure(ProductItem $item): ValidationResult
    {
        // Verificar campos obligatorios según subcategoría
        // Validar tipos de datos según validation_rules
        // Aplicar reglas específicas por categoría
    }
}
```

#### 2. Validación de Negocio
```php
class CategorySpecificValidator
{
    public function validateMerchandising(array $specs): array
    {
        $errors = [];
        
        // Validar capacidad de volumen para artículos de beber
        if (isset($specs['capacity_volume']) && $specs['capacity_volume'] <= 0) {
            $errors[] = 'Capacity volume must be greater than 0';
        }
        
        // Validar certificaciones de materiales para grado alimenticio
        if (isset($specs['food_contact_product']) && $specs['food_contact_product']) {
            if (empty($specs['food_grade_certification'])) {
                $errors[] = 'Food grade certification required for food contact products';
            }
        }
        
        return $errors;
    }
    
    public function validateTextiles(array $specs): array
    {
        $errors = [];
        
        // Validar GSM para textiles
        if (isset($specs['gsm_value']) && ($specs['gsm_value'] < 80 || $specs['gsm_value'] > 600)) {
            $errors[] = 'GSM value must be between 80 and 600';
        }
        
        // Validar tallas para prendas
        if (isset($specs['garment_construction']) && empty($specs['available_sizes'])) {
            $errors[] = 'Available sizes required for garments';
        }
        
        return $errors;
    }
    
    public function validatePDV(array $specs): array
    {
        $errors = [];
        
        // Validar dimensiones plegadas para displays plegables
        if (isset($specs['structural_design']) && $specs['structural_design'] === 'foldable') {
            if (empty($specs['folded_collapsed_dimensions'])) {
                $errors[] = 'Folded dimensions required for foldable displays';
            }
        }
        
        return $errors;
    }
}
```

#### 3. Validación de Integridad
```php
public function validateDataIntegrity(ProductItem $item): ValidationResult
{
    $errors = [];
    
    // Verificar coherencia entre categoría y especificaciones
    $category = $item->productSubcategory->productCategory->name;
    $specs = $item->specifications;
    
    switch ($category) {
        case 'Merchandising':
            if (isset($specs['assembly_required']) && $specs['assembly_required']) {
                $errors[] = 'Assembly typically not applicable for merchandising';
            }
            break;
            
        case 'Textiles':
            if (empty($specs['primary_material']) || !$this->isTextileMaterial($specs['primary_material'])) {
                $errors[] = 'Primary material must be textile-based for textile category';
            }
            break;
            
        case 'Material PDV':
            if (empty($specs['maximum_printable_area'])) {
                $errors[] = 'Printable area required for PDV materials';
            }
            break;
    }
    
    return new ValidationResult($errors);
}
```

---

## Integración con el Sistema

### En ProductItem Model

```php
class ProductItem extends Model
{
    /**
     * Validar especificaciones completas según taxonomía.
     */
    public function hasCompleteSpecifications(): bool
    {
        if (!$this->relationLoaded('productSubcategory.productSpecificationStructure')) {
            $this->load('productSubcategory.productSpecificationStructure');
        }
        
        $structure = $this->productSubcategory->productSpecificationStructure;
        if (!$structure || empty($structure->required_attributes)) {
            return true; // No requirements defined
        }

        // Validar campos obligatorios según taxonomía
        foreach ($structure->required_attributes as $attribute) {
            if (empty($this->specifications[$attribute])) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * Validar especificaciones según reglas de negocio.
     */
    public function validateSpecifications(): ValidationResult
    {
        $validator = app(ProductSpecificationValidator::class);
        return $validator->validateSpecifications($this);
    }
    
    /**
     * Obtener plantilla de especificaciones para subcategoría.
     */
    public function getSpecificationTemplate(): array
    {
        $structure = $this->productSubcategory->productSpecificationStructure;
        
        return [
            'required' => $structure->required_attributes ?? [],
            'optional' => $structure->optional_attributes ?? [],
            'rules' => $structure->validation_rules ?? [],
            'category_rules' => $structure->category_specific_rules ?? [],
        ];
    }
}
```

### En Actions

```php
class CreateProductItemAction
{
    public function execute(CreateProductItemData $data): ProductItem
    {
        return DB::transaction(function () use ($data) {
            // Validar especificaciones según taxonomía antes de crear
            $validator = app(ProductSpecificationValidator::class);
            $tempItem = new ProductItem([
                'product_subcategory_id' => $data->productSubcategoryId,
                'specifications' => $data->specifications,
            ]);
            $tempItem->setRelation('productSubcategory', 
                ProductSubcategory::with('productSpecificationStructure', 'productCategory')
                    ->find($data->productSubcategoryId)
            );
            
            $validation = $validator->validateSpecifications($tempItem);
            if (!$validation->isValid()) {
                throw new ValidationException('Specification validation failed: ' . 
                    implode(', ', $validation->getErrors()));
            }
            
            // Crear el producto si la validación pasa
            $item = ProductItem::create([
                'project_id' => $data->projectId,
                'product_subcategory_id' => $data->productSubcategoryId,
                'name' => $data->name,
                'status' => $data->status ?? ProductItemStatus::Draft,
                'specifications' => $data->specifications,
                'quantity' => $data->quantity,
            ]);

            // Resto de la lógica...
            return $item;
        });
    }
}
```

### En DTOs

```php
class CreateProductItemData extends Data
{
    public function validateSpecifications(): bool
    {
        // Validación básica de formato
        foreach ($this->specifications as $key => $value) {
            if (!is_string($key)) {
                return false;
            }
            
            if (!is_string($value) && !is_array($value) && !is_numeric($value)) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * Validar especificaciones contra estructura de subcategoría.
     */
    public function validateAgainstStructure(ProductSubcategory $subcategory): array
    {
        $structure = $subcategory->productSpecificationStructure;
        $errors = [];
        
        if (!$structure) {
            return $errors; // No structure defined, allow all
        }
        
        // Verificar campos obligatorios
        foreach ($structure->required_attributes as $required) {
            if (empty($this->specifications[$required])) {
                $errors[] = "Required field '{$required}' is missing";
            }
        }
        
        return $errors;
    }
}
```

---

## Casos de Uso por Categoría

### Ejemplo: Polera (Textiles > Garments)

```json
{
  "official_product_name": "Polera Algodón Premium",
  "primary_material": "Cotton",
  "secondary_materials": "5% Elastane",
  "gsm_value": 180,
  "fit_style": "Regular Fit",
  "available_sizes": ["XS", "S", "M", "L", "XL", "XXL"],
  "garment_construction": "Side seams, double-needle hem",
  "neckline_style": "Crew neck",
  "sleeve_style": "Short sleeve",
  "printing_methods": ["Screen printing", "DTG", "Embroidery"],
  "maximum_printable_area": "25cm x 30cm",
  "care_instructions": {
    "washing": "Machine wash cold",
    "drying": "Tumble dry low",
    "ironing": "Iron medium heat"
  }
}
```

### Ejemplo: Pendón Roller (Material PDV > Signage)

```json
{
  "official_product_name": "Pendón Roller 80x200cm",
  "primary_material": "PVC Banner",
  "overall_external_dimensions": "80cm x 200cm x 15cm",
  "structural_design": "Roller banner",
  "assembly_required": true,
  "assembly_type": "Self-assembly with instructions",
  "folded_collapsed_dimensions": "85cm x 30cm x 15cm",
  "maximum_printable_area": "80cm x 200cm",
  "printing_methods": ["Digital UV", "Eco-solvent"],
  "mounting_requirements": "Floor standing base included"
}
```

### Ejemplo: Botella de Agua (Merchandising > Drinking Items)

```json
{
  "official_product_name": "Botella de Agua Acero Inoxidable",
  "primary_material": "Stainless Steel 304",
  "capacity_volume": "500ml",
  "overall_external_dimensions": "7cm x 7cm x 24cm",
  "material_certifications": ["BPA-free", "Food grade"],
  "printing_methods": ["Laser engraving", "Screen printing"],
  "maximum_printable_area": "5cm x 8cm",
  "food_contact_product": true,
  "food_grade_certification": "FDA Food Grade"
}
```

---

## Migración y Evolución

### Estrategia de Implementación

1. **Fase 1**: Crear estructuras base y validadores
2. **Fase 2**: Migrar productos existentes a nueva taxonomía
3. **Fase 3**: Implementar validaciones en tiempo real
4. **Fase 4**: Agregar nuevas subcategorías según necesidades

### Extensibilidad

El sistema está diseñado para permitir:
- ✅ Adición de nuevas categorías sin cambios de código
- ✅ Modificación de estructuras de especificación
- ✅ Validaciones customizadas por cliente
- ✅ Integración con sistemas externos

---

## Referencias

- **Documento Fuente**: [`taxonomia.md`](../taxonomia.md)
- **Modelos Relacionados**: `ProductCategory`, `ProductSubcategory`, `ProductSpecificationStructure`, `ProductItem`
- **Servicios**: `ProductSpecificationValidator`, `CategorySpecificValidator`
- **ADRs Relacionados**: ADR006 (CRUD Simplificado), ADR007 (DTOs), ADR008 (Enums con Métodos)

---

Esta taxonomía proporciona la base estructural para la gestión consistente y escalable de productos en PromoSmart, asegurando que cada producto tenga las especificaciones completas y validadas según su categoría de negocio.