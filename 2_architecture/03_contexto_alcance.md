# 3. Contexto y Alcance (Context and Scope)

> **Propósito:** Define los límites del sistema, mostrando cómo interactúa con su entorno (actores y sistemas externos) y qué funcionalidades están dentro y fuera de su alcance para la versión actual.

---

## 3.1. Contexto del Sistema

El sistema PromoSmart no opera en el vacío. Se comunica e interactúa con múltiples actores y, potencialmente, con otros sistemas. El siguiente diagrama ilustra este contexto.

```mermaid
graph TD
    subgraph Externos
        CLIENTE[Cliente]
        PROVEEDOR[Proveedor]
        AGENTE_CARGA[Agente de Carga]
        AGENTE_ADUANA[Agente de Aduanas]
        SISTEMA_CONTABLE[Sistema Contable Externo]
    end

    subgraph Internos
        direction LR
        VENTAS[Analista de Ventas]
        ADQUISICIONES[Analista de Adquisiciones]
        DISEÑO[Analista de Diseño]
        IMPORTACIONES[Analista de Importaciones]
        FINANZAS[Analista Financiero]
    end

    subgraph Sistema PromoSmart
        SISTEMA[**Sistema PromoSmart v1.0**]
    end

    CLIENTE -- "Envía Orden de Compra" --> SISTEMA
    SISTEMA -- "Envía Cotización" --> CLIENTE

    PROVEEDOR -- "Envía Cotización de Costos" --> SISTEMA
    SISTEMA -- "Envía Orden de Compra" --> PROVEEDOR

    AGENTE_CARGA -- "Informa Estado de Envío" --> SISTEMA
    AGENTE_ADUANA -- "Informa Estado de Aduana" --> SISTEMA

    SISTEMA -- "Exporta Datos Financieros" --> SISTEMA_CONTABLE

    VENTAS -- "Gestiona Proyectos" --> SISTEMA
    ADQUISICIONES -- "Gestiona Proveedores" --> SISTEMA
    DISEÑO -- "Gestiona Aprobaciones" --> SISTEMA
    IMPORTACIONES -- "Gestiona Logística" --> SISTEMA
    FINANZAS -- "Gestiona Costos" --> SISTEMA
```

## 3.2. Alcance Funcional (Scope)

El alcance define qué hará y qué no hará el sistema en su Versión 1.0.

### Funcionalidades DENTRO del Alcance

-   **Gestión de Proyectos y Productos:** Ciclo de vida completo desde la creación hasta la entrega.
-   **Proceso de Cotización:** Tanto a proveedores como a clientes, incluyendo control de versiones.
-   **Gestión de Aprobaciones:** Flujos de aprobación para Maquetas Virtuales (VM), Muestras de Preproducción (PPS) y Facturas Proforma (PI).
-   **Seguimiento Logístico:** Creación y seguimiento de envíos de importación (ISR).
-   **Gestión Financiera Básica:** Seguimiento de costos reales vs. presupuestados y análisis de rentabilidad por proyecto.
-   **Generación de Artefactos:** Creación de documentos clave como Cotizaciones y Órdenes de Compra.

### Funcionalidades FUERA del Alcance

-   **Portal de Cliente:** Los clientes no tendrán un portal para auto-servicio en la V1.0; la comunicación sigue siendo a través de los analistas.
-   **Integración Contable Profunda:** El sistema exportará datos, pero no se integrará en tiempo real con el software de contabilidad.
-   **Gestión de Inventario:** No es aplicable debido al modelo de negocio `made-to-order`.
-   **CRM Avanzado:** No se incluirán funcionalidades de marketing o gestión avanzada de relaciones con clientes.
-   **Aplicación Móvil:** No se desarrollará una aplicación móvil nativa en esta fase.
