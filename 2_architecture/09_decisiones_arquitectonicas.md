# 9. Decisiones Arquitectónicas (Architectural Decisions)

> **Propósito:** Documenta las decisiones arquitectónicas fundamentales que definen la estructura simplificada del sistema PromoSmart.

---

## Decisión Arquitectónica Unificada

PromoSmart implementa una **arquitectura CRUD simplificada con version tracking selectivo** que proporciona exactamente las capacidades requeridas por el negocio sin over-engineering innecesario.

### Fundamentos de la Decisión

**Contexto Original**: Inicialmente se consideró Event Sourcing completo para:
- Auditoría completa e inmutable
- Ciclos de vida complejos de productos 
- Múltiples actores con responsabilidades específicas
- Reconstrucción histórica completa

**Clarificación del Requisito Real**: "Solo necesito lista de versiones"

**Decisión Final**: Arquitectura CRUD + Version Tracking simplificada:

```
CRUD + Version Tracking Selectivo
    ↓ Implementado con
Actions + DTOs + Services + Enums con Métodos  
    ↓ Estructurados por
Organización por Dominios Simplificada
    ↓ Implementados sobre
Stack Tecnológico (Laravel 12 + TALL + Filament + PostgreSQL)
```

### Comparación: Arquitectura Anterior vs Actual

| Aspecto | Event Sourcing (Eliminado) | CRUD + Version Tracking (Actual) |
|---------|----------------------------|-----------------------------------|
| **Complejidad** | ~2000 líneas código | ~200 líneas código |
| **Tiempo desarrollo** | 3-4 semanas | 3-4 días |
| **Learning curve** | Alta (Event Sourcing) | Baja (CRUD estándar) |
| **Auditoría** | Reconstrucción completa | Lista de versiones significativas |
| **Performance** | Overhead significativo | Queries directos optimizados |
| **Mantenibilidad** | Múltiples moving parts | Pocas partes, simple debugging |

### Componentes de la Arquitectura Simplificada

#### 1. CRUD + Version Tracking Selectivo
- **Propósito**: Operaciones directas con tracking de cambios significativos
- **Implementación**: ProductItemVersionService para re-cotizaciones
- **Beneficio**: Simplicidad + auditoría where needed

#### 2. Actions como Orquestadores CRUD
- **Propósito**: Encapsular casos de uso simples con validación y logging
- **Implementación**: Single-purpose classes con DTO validation
- **Beneficio**: Testabilidad y reutilización sin complejidad

#### 3. DTOs como Contratos Tipados
- **Propósito**: Tipado fuerte, validación explícita
- **Implementación**: Spatie Laravel Data simplificado
- **Beneficio**: Contratos claros sin business rules complejas

#### 4. Enums como Objetos de Valor
- **Propósito**: Business rules + UI logic centralizadas
- **Implementación**: PHP 8.1+ Backed Enums con métodos de utilidad para UI y contexto. La lógica de transición se delega a un Service.
- **Beneficio**: Single source of truth para estados y UI, desacoplado de la lógica de negocio compleja.

#### 5. Services para Lógica Cross-Domain
- **Propósito**: Lógica que trasciende un solo modelo
- **Implementación**: ProductItemVersionService, ProjectStatusCalculatorService
- **Beneficio**: Separación de responsabilidades sin overhead

#### 6. Money Value Object
- **Propósito**: Manejo seguro y encapsulado de valores monetarios
- **Implementación**: Clase `Money` y `MoneyCast` personalizado.
- **Beneficio**: Previene errores de moneda, centraliza la lógica y mejora la legibilidad.

### Stack Tecnológico Simplificado

**TALL Stack + Filament + PostgreSQL (sin Event Sourcing)**

```php
// Ejemplo de implementación simplificada siguiendo ADR-010
class RequoteProductItemAction extends BaseAction
{
    public function __construct(
        private StateTransitionService $stateTransitionService,
        private ProductItemVersionService $versionService
    ) {}
    
    public function execute(RequoteProductItemData $data): ProductItem
    {
        $this->authorize('update', $data->productItem);
        
        // Validar transición usando StateTransitionService (ADR-010)
        if (!$this->stateTransitionService->isTransitionAllowed($data->productItem, $data->newStatus, auth()->user())) {
            throw new ValidationException('Invalid state transition');
        }
        
        // Capturar estado previo
        $previousData = [
            'specifications' => $data->productItem->specifications,
            'status' => $data->productItem->status->value,
        ];
        
        // CRUD simple
        $data->productItem->update([
            'specifications' => $data->newSpecifications,
            'status' => $data->newStatus,
        ]);
        
        // Version tracking para requotes
        $this->versionService->createVersion(
            item: $data->productItem,
            changeType: 'requote',
            previousData: $previousData,
            newData: [
                'specifications' => $data->newSpecifications,
                'status' => $data->newStatus->value,
            ],
            summary: $data->reason
        );
        
        return $data->productItem;
    }
}
```

### Flujo de Datos Simplificado

1. **UI (Filament)** → Captura entrada del usuario
2. **DTOs** → Validan y tipan datos de entrada
3. **Actions** → Ejecutan CRUD con validaciones business usando StateTransitionService
4. **Models (Eloquent)** → Actualizan base de datos directamente
5. **Services** → Version tracking cuando es significativo + StateTransitionService para lógica de transiciones
6. **Enums** → Proveen UI logic (labels, colors) - NO business rules
7. **Logging** → Manual y explícito para auditoría

### Consecuencias de la Simplificación

**Beneficios**:
- ✅ **Development Speed**: 10x más rápido que Event Sourcing
- ✅ **Team Onboarding**: Cualquier dev Laravel puede trabajar inmediatamente
- ✅ **Debugging Simple**: Path directo, troubleshooting straightforward
- ✅ **Performance Óptima**: Sin overhead, queries directos optimizados
- ✅ **Mantenibilidad**: Pocas moving parts, menos superficie para bugs
- ✅ **Satisface Requisitos**: Lista de versiones exactly as needed

**Lo que NO ofrece (y no se necesita)**:
- ❌ Reconstrucción histórica completa (no requerido)
- ❌ Business rules enforcement automático (validación manual suficiente)
- ❌ Side effects automáticos (notificaciones manuales OK)
- ❌ Event store inmutable (audit logs suficientes)

**Trade-offs Aceptados**:
- ⚠️ **Auditoría Limitada**: Solo cambios significativos vs todos los cambios
- ⚠️ **Manual Side Effects**: Notificaciones y updates manuales
- ⚠️ **No Time Travel**: No reconstrucción de estado histórico exacto

### ADRs Actualizados (Arquitectura Simplificada)

#### **Decisiones Fundamentales**
- **[ADR-001: Adopción de ADRs](adrs/adr001_adopcion_ADR.md)** ✅
- **[ADR-002: Marco de Documentación arc42](adrs/adr002_adopcion_arc42.md)** ✅
- **[ADR-003: Política de Idioma Bilingüe](adrs/adr003_politica_idioma.md)** ✅

#### **Decisiones de Stack Tecnológico**
- **[ADR-004: TALL Stack + Filament](adrs/adr004_adopcion_tall_stack_filament.md)** ✅
- **[ADR-005: PostgreSQL](adrs/adr005_eleccion_postgresql.md)** ✅

#### **Decisiones Arquitectónicas Simplificadas**
- **[ADR-006: CRUD + Version Tracking Arquitectura](adrs/adr006_crud_arquitectura_simplificada.md)** ✅
- **[ADR-007: DTOs Contratos Simplificados](adrs/adr007_dtos_contratos_simplificados.md)** ✅
- **[ADR-008: Enums con Métodos Business Logic](adrs/adr008_uso_enums_con_metodos.md)** ✅
- **[ADR-009: Organización Dominios Simplificada](adrs/adr009_organizacion_dominios_simplificada.md)** ✅
- **[ADR-010: Gestión de Estados con Servicio Dedicado](adrs/adr010_gestion_estados_con_servicio.md)** ✅
- **[ADR-011: Adopción del Patrón Money como Value Object](adrs/adr011_money_value_object.md)** ✅

#### **ADRs Eliminados/Obsoletos**
- ~~ADR-010: Spatie Laravel Event Sourcing~~ → Eliminado (arquitectura descartada)

### Referencias de Implementación

- **[Arquitectura Integrada CRUD + Version Tracking](00_arquitectura_integrada.md)** - Especificación completa simplificada
- **[Patrones Simplificados](../3_tech_design/patterns/)** - Action, Service y Data Snapshot patterns
  - [Action Pattern](../3_tech_design/patterns/01_action_pattern.md) - Orquestación CRUD + Version Tracking
  - [Service Layer Pattern](../3_tech_design/patterns/05_service_layer_pattern.md) - ProductItemVersionService y cross-domain logic
  - [Data Snapshot Pattern](../3_tech_design/patterns/04_data_snapshot_pattern.md) - Snapshots opcionales para documentos
- **[Bloques de Construcción Simplificados](05_bloques_construccion.md)** - Estructura de componentes CRUD + Version Tracking

### Plan de Implementación Simplificado

#### Fase 1: Fundación (1-2 días)
1. ✅ Crear migration para `product_item_versions`
2. ✅ Crear `ProductItemVersion` model  
3. ✅ Crear `ProductItemVersionService`

#### Fase 2: Actions con Version Tracking (2-3 días)
1. ✅ Implementar `RequoteProductItemAction` con version tracking
2. ✅ Actualizar Actions existentes con logging
3. ✅ Añadir UI components para mostrar versiones

#### Fase 3: Testing y Deployment (1 día)
1. ✅ Testing de versioning y CRUD operations
2. ✅ Refinements en UI para version history
3. ✅ Documentation final

**Total: ~1 semana** de implementación vs 3-4 semanas para Event Sourcing.

---

## Justificación de la Decisión

Esta arquitectura simplificada proporciona **exactamente lo que el negocio necesita**:

- ✅ **Lista de versiones de productos**: Perfectamente implementado
- ✅ **Tracking de re-cotizaciones**: Automático y completo
- ✅ **Audit trail básico**: Suficiente para requisitos reales
- ✅ **Quién cambió qué y cuándo**: Información completa disponible

**Sin la complejidad innecesaria** de Event Sourcing para un requisito que no lo justifica.

**Principio rector**: La complejidad arquitectónica debe ser proporcional al valor de negocio real, no a lo que "podría necesitarse en el futuro".