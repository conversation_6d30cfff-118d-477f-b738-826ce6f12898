# Cheat Sheet - Nomenclatura PromoSmart

> **⚡ Consulta Rápida**: Los patrones más usados para desarrollo diario. Para información completa, ve a [00_NOMENCLATURA_GUIA_MAESTRA.md](00_NOMENCLATURA_GUIA_MAESTRA.md).

---

## 🔥 Patrones Más Usados

### 🏗️ Crear nueva entidad

```php
// ✅ Correcto
class ProductItem extends Model
{
    protected $table = 'product_items'; // snake_case, plural
    
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
}

// ❌ Incorrecto  
class ItemProducto extends Model { }
class ProductoItem extends Model { }
```

### ⚡ Crear nuevo Action

```php
// ✅ Correcto - app/Actions/ProductItem/CreateProductItemAction.php
class CreateProductItemAction
{
    public function execute(CreateProductItemData $data): ProductItem
    {
        // Lógica con comentarios en español si es compleja
        return ProductItem::create($data->toArray());
    }
}

// ❌ Incorrecto
class CrearItemProductoAction { }
class ProductItemCreateAction { }
```

### 🎛️ Crear nuevo Enum

```php
// ✅ Correcto - app/Enums/ProductItemStatus.php
enum ProductItemStatus: string
{
    case Draft = 'draft';
    case ReadyForSourcing = 'ready_for_sourcing';
    
    public function getLabel(): string
    {
        return match($this) {
            self::Draft => 'Borrador',
            self::ReadyForSourcing => 'Listo para Sourcing',
        };
    }
    
    public function getColor(): string
    {
        return match($this) {
            self::Draft => 'gray',
            self::ReadyForSourcing => 'blue',
        };
    }
}

// ❌ Incorrecto
enum EstadoProductItem: string { }
```

### 🗃️ Crear migración

```php
// ✅ Correcto - Nombre: create_product_items_table.php
Schema::create('product_items', function (Blueprint $table) {
    $table->id();
    $table->foreignId('project_id')->constrained();
    $table->string('name');
    $table->string('status')->default('draft');
    $table->timestamps();
    
    $table->index(['project_id', 'status']);
});

// ❌ Incorrecto
Schema::create('items_producto', function (Blueprint $table) { }
```

---

## 🎯 Conversión Rápida - Entidades Principales

| Español | Inglés | Tabla | Uso Común |
|---|---|---|---|
| Proyecto | `Project` | `projects` | `Project::with('productItems')` |
| Ítem de Producto | `ProductItem` | `product_items` | `ProductItem::draft()->get()` |
| Cliente | `Customer` | `customers` | `Customer::active()->get()` |
| Proveedor | `Supplier` | `suppliers` | `Supplier::verified()->get()` |
| Usuario | `User` | `users` | `User::role('analyst')` |
| Registro de Envío | `ImportShipmentRecord` | `import_shipment_records` | `ImportShipmentRecord::pending()` |

---

## 🎛️ Estados de ProductItem - Referencia Rápida

```php
// Uso común
ProductItemStatus::Draft->value;                    // 'draft'
ProductItemStatus::ReadyForSourcing->getLabel();    // 'Listo para Sourcing'
ProductItemStatus::SourcingInProgress->getColor();  // 'yellow'

// Estados disponibles
ProductItemStatus::Draft                           // 'draft'
ProductItemStatus::ReadyForSourcing               // 'ready_for_sourcing'
ProductItemStatus::SourcingInProgress             // 'sourcing_in_progress'
ProductItemStatus::InternalReviewPending          // 'internal_review_pending'
ProductItemStatus::RevisionRequested              // 'revision_requested'
ProductItemStatus::QuotedToCustomer               // 'quoted_to_customer'
ProductItemStatus::CustomerRevisionRequested      // 'customer_revision_requested'
ProductItemStatus::PendingVmApproval              // 'pending_vm_approval'
ProductItemStatus::VmApproved                     // 'vm_approved'
ProductItemStatus::PendingPpsApproval             // 'pending_pps_approval'
ProductItemStatus::PpsApproved                    // 'pps_approved'
ProductItemStatus::InProduction                   // 'in_production'
ProductItemStatus::ProductionCompleted            // 'production_completed'
ProductItemStatus::InTransit                      // 'in_transit'
ProductItemStatus::InCustoms                      // 'in_customs'
ProductItemStatus::ReadyForDelivery               // 'ready_for_delivery'
ProductItemStatus::Delivered                      // 'delivered'
```

---

## 🚨 Errores Comunes y Correcciones

### ❌ Nombres de clases
```php
// ❌ Incorrecto
class ProyectoModel extends Model { }
class ItemDeProducto extends Model { }
class ServicioDeEstados { }

// ✅ Correcto
class Project extends Model { }
class ProductItem extends Model { }
class StateTransitionService { }
```

### ❌ Nombres de métodos
```php
// ❌ Incorrecto
public function crearProducto() { }
public function obtenerEstado() { }
public function validarDatos() { }

// ✅ Correcto
public function createProduct() { }
public function getStatus() { }
public function validateData() { }
```

### ❌ Nombres de variables
```php
// ❌ Incorrecto
$itemProducto = ProductItem::find(1);
$estadoActual = $item->status;
$listaClientes = Customer::all();

// ✅ Correcto
$productItem = ProductItem::find(1);
$currentStatus = $item->status;
$customers = Customer::all();
```

### ❌ Nombres de tablas y columnas
```php
// ❌ Incorrecto
Schema::create('items_producto', function (Blueprint $table) {
    $table->string('nombre_producto');
    $table->string('estado_actual');
});

// ✅ Correcto
Schema::create('product_items', function (Blueprint $table) {
    $table->string('product_name');
    $table->string('current_status');
});
```

---

## 🔧 Templates Rápidos

### Nuevo Action
```php
class {Verb}{Entity}Action
{
    public function execute({Entity}Data $data): {Entity}
    {
        // TODO: Implementar lógica
    }
}
```

### Nuevo DTO
```php
class {Entity}Data extends Data
{
    public function __construct(
        public string $name,
        public {Entity}Status $status,
    ) {}
}
```

### Nuevo Enum
```php
enum {Entity}{Property}: string
{
    case {Case} = '{snake_case_value}';
    
    public function getLabel(): string
    {
        return match($this) {
            self::{Case} => '{Label en Español}',
        };
    }
    
    public function getColor(): string
    {
        return match($this) {
            self::{Case} => 'blue',
        };
    }
}
```

### Nuevo Test
```php
class {Action}Test extends TestCase
{
    /** @test */
    public function it_can_{action_description}_successfully()
    {
        // Arrange
        
        // Act
        
        // Assert
    }
}
```

---

## 🎯 Checklist de Revisión Rápida

### ✅ Antes de hacer commit:
- [ ] Clases en inglés, PascalCase
- [ ] Métodos en inglés, camelCase  
- [ ] Variables en inglés, camelCase
- [ ] Tablas en inglés, snake_case, plural
- [ ] Columnas en inglés, snake_case
- [ ] Enums: Cases PascalCase, values snake_case
- [ ] Actions siguen pattern `{Verb}{Entity}Action`
- [ ] DTOs siguen pattern `{Entity}Data`
- [ ] Tests en inglés con descripciones claras
- [ ] Comentarios de lógica compleja en español
- [ ] Labels de UI en español

### 🔍 Comandos de validación rápida:
```bash
# Buscar clases con nombres en español
find app/ -name "*.php" -exec grep -l "class.*[ÁÉÍÓÚáéíóúñÑ]" {} \;

# Buscar métodos con nombres en español  
find app/ -name "*.php" -exec grep -l "function.*[ÁÉÍÓÚáéíóúñÑ]" {} \;
```

---

## 📚 Enlaces Rápidos

- **📖 Guía completa**: [00_NOMENCLATURA_GUIA_MAESTRA.md](00_NOMENCLATURA_GUIA_MAESTRA.md)
- **💡 Ejemplos**: [02_ejemplos_practicos.md](02_ejemplos_practicos.md)
- **🔧 Herramientas**: [03_validacion_y_herramientas.md](03_validacion_y_herramientas.md)
