# 🎯 Nomenclatura PromoSmart - Estructura Centralizada

> **Nueva estructura de documentación de nomenclatura**: Todo lo que necesitas para mantener consistencia en el código y documentación.

> **🏗️ Coherente con ADR-009**: Todos los ejemplos y convenciones siguen la **Organización por Dominios Simplificada** donde Actions/Data se organizan por dominio, pero Models/Enums/Services mantienen estructura plana.

---

## 🚀 Inicio Rápido

### Para Desarrolladores Nuevos
1. **Lee primero**: [00_NOMENCLATURA_GUIA_MAESTRA.md](00_NOMENCLATURA_GUIA_MAESTRA.md) (15 minutos)
2. **Guarda en bookmarks**: [01_guia_rapida_desarrollador.md](01_guia_rapida_desarrollador.md) (consulta diaria)
3. **Instala herramientas**: Scripts de [03_validacion_y_herramientas.md](03_validacion_y_herramientas.md)

### Para Desarrolladores Existentes
1. **Migra tus referencias**: Ver [04_guia_migracion.md](04_guia_migracion.md)
2. **Actualiza bookmarks** a los nuevos documentos
3. **Usa la guía rápida** para consultas diarias

---

## 📁 Estructura de Documentos

| Documento | Propósito | Cuándo Usar |
|---|---|---|
| **[00_NOMENCLATURA_GUIA_MAESTRA.md](00_NOMENCLATURA_GUIA_MAESTRA.md)** | 🎯 **Documento central** con todas las convenciones | Onboarding, referencia completa |
| **[01_guia_rapida_desarrollador.md](01_guia_rapida_desarrollador.md)** | ⚡ **Cheat sheet** para consulta diaria | Desarrollo día a día, consultas rápidas |
| **[02_ejemplos_practicos.md](02_ejemplos_practicos.md)** | 💡 **Casos reales** con código completo | Implementar nuevas funcionalidades |
| **[03_validacion_y_herramientas.md](03_validacion_y_herramientas.md)** | 🔧 **Scripts y herramientas** de automatización | Setup de desarrollo, CI/CD |
| **[04_guia_migracion.md](04_guia_migracion.md)** | 📖 **Migración** desde estructura anterior | Transición, actualización de referencias |

---

## 🎯 Regla de Oro

```
📝 Documentación y comunicación → ESPAÑOL
💻 Código fuente → INGLÉS (Laravel/PSR)
🗃️ Base de datos → INGLÉS, snake_case
🎨 UI Labels → ESPAÑOL
```

---

## ⚡ Consulta Súper Rápida

### Top 5 Conversiones
| Español | Inglés | Tabla | Uso |
|---|---|---|---|
| Proyecto | `Project` | `projects` | `Project::find(1)` |
| Ítem de Producto | `ProductItem` | `product_items` | `ProductItem::draft()` |
| Cliente | `Customer` | `customers` | `Customer::active()` |
| Proveedor | `Supplier` | `suppliers` | `Supplier::verified()` |
| Usuario | `User` | `users` | `User::role('analyst')` |

### Patrones Más Usados (ADR-009)
```php
// ✅ Action (por dominio)
// app/Actions/ProductItem/CreateProductItemAction.php
class CreateProductItemAction { }

// ✅ DTO (por dominio)
// app/Data/ProductItem/CreateProductItemData.php
class CreateProductItemData extends Data { }

// ✅ Enum (estructura plana - compartido)
// app/Enums/ProductItemStatus.php
enum ProductItemStatus: string {
    case Draft = 'draft';
    public function getLabel(): string { return 'Borrador'; }
}

// ✅ Test (por dominio)
// tests/Feature/Actions/ProductItem/CreateProductItemActionTest.php
class CreateProductItemActionTest extends TestCase { }
```

---

## 🔧 Herramientas Disponibles

### Scripts de Validación
```bash
# Validar nombres de clases
./scripts/validate-class-names.sh

# Validar estructura de Actions
./scripts/validate-actions.sh

# Validar organización por dominios (ADR-009)
./scripts/validate-domain-organization.sh

# Validación completa
./scripts/validate-all.sh
```

### Templates para PhpStorm
- **psaction** → Crear nuevo Action
- **psdto** → Crear nuevo DTO
- **psenum** → Crear nuevo Enum
- **pstest** → Crear nuevo Test

---

## 📈 Beneficios de la Nueva Estructura

| Antes | Después |
|---|---|
| 📚 Información en 5+ documentos | 🎯 Un documento central |
| ⏱️ Consulta en 5-10 minutos | ⚡ Consulta en 30 segundos |
| ❓ Sin ejemplos prácticos | 💡 15+ casos con código real |
| 🔍 Sin herramientas de validación | 🔧 8 scripts automáticos |
| 📖 Onboarding lento | 🚀 Productivo en 15 minutos |

---

## 🆘 Ayuda y Soporte

### ❓ Preguntas Frecuentes
- **¿Cómo nombro un nuevo enum?** → Ver [Guía Maestra](00_NOMENCLATURA_GUIA_MAESTRA.md#-estados-y-enums)
- **¿Cuándo usar español vs inglés?** → Ver [FAQ](00_NOMENCLATURA_GUIA_MAESTRA.md#-faq---preguntas-frecuentes)
- **¿Cómo valido mi código?** → Ver [Herramientas](03_validacion_y_herramientas.md#-scripts-de-validación)

### 🔍 Búsqueda Rápida
- **Creando ProductItem** → [Guía Rápida](01_guia_rapida_desarrollador.md#-crear-nueva-entidad)
- **Estados de ProductItem** → [Guía Rápida](01_guia_rapida_desarrollador.md#-estados-de-productitem---referencia-rápida)
- **Ejemplos de Actions** → [Ejemplos Prácticos](02_ejemplos_practicos.md#-escenario-1-crear-nueva-funcionalidad-requote-productitem)

### 🐛 Problemas Comunes
- **Clase con nombre en español** → Ver [Errores Comunes](01_guia_rapida_desarrollador.md#-errores-comunes-y-correcciones)
- **Método con nombre incorrecto** → Ver [Checklist](01_guia_rapida_desarrollador.md#-checklist-de-revisión-rápida)
- **Migración desde docs anteriores** → Ver [Guía de Migración](04_guia_migracion.md)

---

## 📦 Documentos Legacy

Los documentos originales se mantienen en `legacy/` durante el período de transición:

- `legacy/adr003_politica_idioma.md` - ADR original
- `legacy/glosario_actual.md` - Glosario anterior  
- `legacy/domain_to_code_mapping.md` - Mapeo técnico anterior
- `legacy/conceptos_transversales.md` - Convenciones anteriores
- `legacy/diccionario_negocio_sistema.md` - Diccionario de negocio anterior

---

## 🎉 ¡Empezar Ahora!

1. **📖 Lee**: [00_NOMENCLATURA_GUIA_MAESTRA.md](00_NOMENCLATURA_GUIA_MAESTRA.md)
2. **⚡ Guarda**: [01_guia_rapida_desarrollador.md](01_guia_rapida_desarrollador.md) en bookmarks
3. **🔧 Instala**: Scripts de validación
4. **💻 Desarrolla**: Con confianza y consistencia

**¿Dudas?** Revisa el [FAQ](00_NOMENCLATURA_GUIA_MAESTRA.md#-faq---preguntas-frecuentes) o consulta los [ejemplos prácticos](02_ejemplos_practicos.md).
