# ADR-003: Política de Idioma para Documentación y Código

- Estado: Aceptada
- Fecha: 2025-08-17
- Deciders: <PERSON>
- Authors: <AUTHORS>

## Contexto
Es necesario establecer una política de idioma clara y consistente para todo el proyecto PromoSmart, abarcando tanto la documentación como el código fuente.

**Fuerzas impulsoras:**
- El equipo de desarrollo principal es hispanohablante, facilitando comunicación en español
- Laravel y el ecosistema PHP establecen convenciones en inglés como estándar
- Necesidad de mantener compatibilidad con librerías, frameworks y herramientas de terceros
- Facilitar onboarding de desarrolladores con experiencia en Laravel/PHP
- Evitar inconsistencias que dificulten mantenimiento y búsqueda de código
- Preparar el proyecto para posible incorporación de talento internacional

**Problema específico:**
Sin una política clara, se produce mezcla inconsistente de idiomas que genera confusión, reduce legibilidad del código y dificulta el mantenimiento.

## Decisión
Adoptar una **política bilingüe estructurada** que separe claramente documentación conceptual (español) de implementación técnica (inglés), siguiendo convenciones de Laravel y mejores prácticas de desarrollo PHP.

**Implementación específica:**

### Español para documentación y comunicación:
- **Documentación arquitectónica:** ADRs, especificaciones, diagramas arc42
- **Comentarios en código:** Explicaciones de lógica de negocio, algoritmos complejos
- **Commits y PRs:** Mensajes descriptivos en español
- **Documentación de usuario:** Manuales, guías, README
- **Comunicación del equipo:** Issues, discusiones técnicas

### Inglés para implementación y código:
- **Nombres de entidades:** Clases, interfaces, traits, enums siguiendo PSR y Laravel
- **Variables y métodos:** CamelCase según convenciones PHP/Laravel
- **Base de datos:** Tablas, columnas, índices siguiendo nomenclatura Laravel
- **APIs y DTOs:** Endpoints, payloads, responses en inglés
- **Configuración:** Archivos config, env, rutas siguiendo Laravel
- **Tests:** Nombres de tests y assertions

### Casos especiales:
- **Términos de dominio:** Mantener términos de negocio en español cuando sean únicos del contexto chileno/PromoSmart
- **Traducciones de UI:** Archivos lang/ en español para interfaz de usuario
- **Validaciones:** Mensajes de error en español para usuarios finales

## Alternativas consideradas

### 1) Todo en español
**Descripción:** Usar español tanto en documentación como en código.
- **Pros:** 
  - Consistencia total de idioma
  - Comprensión inmediata para equipo hispanohablante
  - Términos de dominio naturales
- **Contras:** 
  - Conflicto con convenciones Laravel/PHP
  - Dificultad con librerías de terceros
  - Barrera para desarrolladores internacionales
  - Problemas con herramientas de desarrollo

### 2) Todo en inglés
**Descripción:** Usar inglés tanto en documentación como en código.
- **Pros:**
  - Consistencia con estándares internacionales
  - Compatibilidad total con Laravel/PHP
  - Facilita incorporación de desarrolladores internacionales
  - Mejor integración con herramientas
- **Contras:**
  - Barrera inicial para equipo hispanohablante
  - Pérdida de matices en términos de dominio específicos
  - Comunicación menos natural en el equipo actual
  - Documentación menos accesible para stakeholders de negocio

### 3) Mezcla ad-hoc sin política
**Descripción:** Permitir uso libre de ambos idiomas según preferencia del desarrollador.
- **Pros:**
  - Flexibilidad total
  - Sin restricciones para desarrolladores
- **Contras:**
  - Inconsistencia y confusión
  - Dificultad de mantenimiento
  - Problemas de comunicación
  - Código difícil de entender para nuevos miembros

## Consecuencias

**Positivas:**
- (+) **Separación clara:** Documentación conceptual en español vs implementación técnica en inglés mejora claridad y mantenibilidad
- (+) **Compatibilidad Laravel:** Adherencia a convenciones PSR y Laravel facilita desarrollo y debugging
- (+) **Comunicación eficiente:** Equipo actual puede comunicarse naturalmente en español
- (+) **Estándares industriales:** Código en inglés facilita integración con librerías, herramientas y Stack Overflow
- (+) **Escalabilidad del equipo:** Preparación para incorporar desarrolladores con experiencia Laravel
- (+) **Legibilidad:** Evita "Spanglish" y traducciones forzadas que confunden

**Negativas:**
- (-) **Disciplina requerida:** Necesidad de mantener consistencia en la separación de idiomas
- (-) **Curva de aprendizaje:** Nuevos miembros deben familiarizarse con política bilingüe
- (-) **Overhead inicial:** Tiempo de establecimiento de convenciones y revisión de código existente
- (-) **Traducción conceptual:** Necesidad de traducir entre términos de dominio y implementación técnica

## Historial de estado
- 2025-08-17: Propuesta (Javier Errazuriz)
- 2025-08-17: Aceptada (Javier Errazuriz)

## Referencias
- ADR-001: Adopción de ADRs (Documenting Architecture Decisions, Nygard): `adrs/adr001_adopcion_ADR.md`
- ADR-002: Uso de arc42 como marco y estructura de documentación: `adrs/adr002_adopcion_arc42.md`
- PSR Standards: https://www.php-fig.org/psr/
- Laravel Naming Conventions: https://laravel.com/docs/contributions#coding-style
- Eloquent Model Conventions: https://laravel.com/docs/eloquent#eloquent-model-conventions
