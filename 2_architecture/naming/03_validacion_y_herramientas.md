# Herramientas de Validación de Nomenclatura

> **🔧 Automatización**: Scripts, herramientas y templates para mantener consistencia en la nomenclatura del proyecto.

---

## 🔍 Scripts de Validación

### Validar nombres de clases
```bash
#!/bin/bash
# scripts/validate-class-names.sh

echo "🔍 Buscando clases con nombres en español..."

# Buscar clases con acentos o ñ
SPANISH_CLASSES=$(find app/ -name "*.php" -exec grep -l "class.*[ÁÉÍÓÚáéíóúñÑ]" {} \;)

if [ -n "$SPANISH_CLASSES" ]; then
    echo "❌ Clases con nombres en español encontradas:"
    echo "$SPANISH_CLASSES"
    exit 1
else
    echo "✅ Todas las clases tienen nombres en inglés"
fi

# Buscar patrones comunes incorrectos
INCORRECT_PATTERNS=$(find app/ -name "*.php" -exec grep -l "class.*\(Producto\|Proyecto\|Cliente\|Proveedor\|Usuario\)" {} \;)

if [ -n "$INCORRECT_PATTERNS" ]; then
    echo "❌ Clases con patrones incorrectos encontradas:"
    echo "$INCORRECT_PATTERNS"
    exit 1
else
    echo "✅ No se encontraron patrones incorrectos en nombres de clases"
fi
```

### Validar nombres de métodos
```bash
#!/bin/bash
# scripts/validate-method-names.sh

echo "🔍 Validando nombres de métodos..."

# Buscar métodos con nombres en español
SPANISH_METHODS=$(find app/ -name "*.php" -exec grep -l "function.*[ÁÉÍÓÚáéíóúñÑ]" {} \;)

if [ -n "$SPANISH_METHODS" ]; then
    echo "❌ Métodos con nombres en español:"
    find app/ -name "*.php" -exec grep -n "function.*[ÁÉÍÓÚáéíóúñÑ]" {} \;
    exit 1
fi

# Buscar patrones comunes incorrectos en métodos
INCORRECT_METHOD_PATTERNS=$(find app/ -name "*.php" -exec grep -l "function.*\(crear\|obtener\|actualizar\|eliminar\|validar\)" {} \;)

if [ -n "$INCORRECT_METHOD_PATTERNS" ]; then
    echo "❌ Métodos con patrones incorrectos:"
    find app/ -name "*.php" -exec grep -n "function.*\(crear\|obtener\|actualizar\|eliminar\|validar\)" {} \;
    exit 1
fi

echo "✅ Nombres de métodos validados correctamente"
```

### Validar estructura de Actions
```bash
#!/bin/bash
# scripts/validate-actions.sh

echo "🔍 Validando estructura de Actions..."

# Verificar que Actions sigan el patrón {Verb}{Entity}Action
INVALID_ACTIONS=$(find app/Actions/ -name "*.php" -exec basename {} \; | grep -v "Action\.php$")

if [ -n "$INVALID_ACTIONS" ]; then
    echo "❌ Actions que no siguen el patrón {Verb}{Entity}Action:"
    echo "$INVALID_ACTIONS"
    exit 1
fi

# Verificar que Actions tengan método execute
ACTIONS_WITHOUT_EXECUTE=$(find app/Actions/ -name "*.php" -exec grep -L "function execute" {} \;)

if [ -n "$ACTIONS_WITHOUT_EXECUTE" ]; then
    echo "❌ Actions sin método execute:"
    echo "$ACTIONS_WITHOUT_EXECUTE"
    exit 1
fi

echo "✅ Estructura de Actions validada correctamente"
```

### Validar nombres de tablas en migraciones
```bash
#!/bin/bash
# scripts/validate-migrations.sh

echo "🔍 Validando nombres de tablas en migraciones..."

# Buscar tablas con nombres en español
SPANISH_TABLES=$(find database/migrations/ -name "*.php" -exec grep -l "create.*\(productos\|proyectos\|clientes\|proveedores\|usuarios\)" {} \;)

if [ -n "$SPANISH_TABLES" ]; then
    echo "❌ Migraciones con nombres de tabla en español:"
    echo "$SPANISH_TABLES"
    exit 1
fi

# Verificar que las tablas estén en plural
SINGULAR_TABLES=$(find database/migrations/ -name "*.php" -exec grep -l "Schema::create.*\(product[^s]\|project[^s]\|customer[^s]\|supplier[^s]\|user[^s]\)" {} \;)

if [ -n "$SINGULAR_TABLES" ]; then
    echo "⚠️  Posibles tablas en singular (revisar manualmente):"
    echo "$SINGULAR_TABLES"
fi

echo "✅ Nombres de tablas validados"
```

---

## 📝 Templates para IDEs

### PhpStorm Live Templates

#### Template: Action
```xml
<!-- Configuración: Settings > Editor > Live Templates -->
<template name="psaction" value="&lt;?php

namespace App\Actions\$DOMAIN$;

use App\Data\$DOMAIN$\$DATA$;
use App\Models\$ENTITY$;

class $NAME$Action
{
    public function execute($DATA$ $data): $RETURN$
    {
        // TODO: Implementar lógica de $DESCRIPTION$
        $END$
    }
}
" description="PromoSmart Action Template" toReformat="true" toShortenFQNames="true">
  <variable name="DOMAIN" expression="" defaultValue="&quot;ProductItem&quot;" alwaysStopAt="true" />
  <variable name="DATA" expression="" defaultValue="&quot;CreateProductItemData&quot;" alwaysStopAt="true" />
  <variable name="ENTITY" expression="" defaultValue="&quot;ProductItem&quot;" alwaysStopAt="true" />
  <variable name="NAME" expression="" defaultValue="&quot;CreateProductItem&quot;" alwaysStopAt="true" />
  <variable name="RETURN" expression="" defaultValue="&quot;ProductItem&quot;" alwaysStopAt="true" />
  <variable name="DESCRIPTION" expression="" defaultValue="&quot;crear ítem de producto&quot;" alwaysStopAt="true" />
  <context>
    <option name="PHP" value="true" />
  </context>
</template>
```

#### Template: DTO
```xml
<template name="psdto" value="&lt;?php

namespace App\Data\$DOMAIN$;

use Spatie\LaravelData\Data;

class $NAME$Data extends Data
{
    public function __construct(
        $PROPERTIES$
    ) {}
    
    public static function rules(): array
    {
        return [
            $RULES$
        ];
    }
}
" description="PromoSmart DTO Template" toReformat="true" toShortenFQNames="true">
  <variable name="DOMAIN" expression="" defaultValue="&quot;ProductItem&quot;" alwaysStopAt="true" />
  <variable name="NAME" expression="" defaultValue="&quot;CreateProductItem&quot;" alwaysStopAt="true" />
  <variable name="PROPERTIES" expression="" defaultValue="&quot;public string $name,&quot;" alwaysStopAt="true" />
  <variable name="RULES" expression="" defaultValue="&quot;'name' =&gt; ['required', 'string'],&quot;" alwaysStopAt="true" />
  <context>
    <option name="PHP" value="true" />
  </context>
</template>
```

#### Template: Enum
```xml
<template name="psenum" value="&lt;?php

namespace App\Enums;

enum $NAME$: string
{
    case $CASE$ = '$VALUE$';
    
    public function getLabel(): string
    {
        return match($this) {
            self::$CASE$ => '$LABEL$',
        };
    }
    
    public function getColor(): string
    {
        return match($this) {
            self::$CASE$ => '$COLOR$',
        };
    }
}
" description="PromoSmart Enum Template" toReformat="true" toShortenFQNames="true">
  <variable name="NAME" expression="" defaultValue="&quot;ProductItemStatus&quot;" alwaysStopAt="true" />
  <variable name="CASE" expression="" defaultValue="&quot;Draft&quot;" alwaysStopAt="true" />
  <variable name="VALUE" expression="" defaultValue="&quot;draft&quot;" alwaysStopAt="true" />
  <variable name="LABEL" expression="" defaultValue="&quot;Borrador&quot;" alwaysStopAt="true" />
  <variable name="COLOR" expression="" defaultValue="&quot;gray&quot;" alwaysStopAt="true" />
  <context>
    <option name="PHP" value="true" />
  </context>
</template>
```

#### Template: Test
```xml
<template name="pstest" value="&lt;?php

namespace Tests\$TYPE$\$PATH$;

use Tests\TestCase;
use $IMPORTS$;

class $NAME$Test extends TestCase
{
    /** @test */
    public function it_can_$ACTION$_successfully()
    {
        // Arrange
        $ARRANGE$
        
        // Act
        $ACT$
        
        // Assert
        $ASSERT$
    }
    
    /** @test */
    public function it_throws_exception_when_$ERROR_CONDITION$()
    {
        // Arrange
        $ERROR_ARRANGE$
        
        // Act &amp; Assert
        $this->expectException($EXCEPTION$::class);
        $ERROR_ACT$
    }
}
" description="PromoSmart Test Template" toReformat="true" toShortenFQNames="true">
  <variable name="TYPE" expression="" defaultValue="&quot;Feature&quot;" alwaysStopAt="true" />
  <variable name="PATH" expression="" defaultValue="&quot;Actions\\ProductItem&quot;" alwaysStopAt="true" />
  <variable name="NAME" expression="" defaultValue="&quot;CreateProductItemAction&quot;" alwaysStopAt="true" />
  <variable name="IMPORTS" expression="" defaultValue="&quot;App\\Actions\\ProductItem\\CreateProductItemAction&quot;" alwaysStopAt="true" />
  <variable name="ACTION" expression="" defaultValue="&quot;create_product_item&quot;" alwaysStopAt="true" />
  <variable name="ARRANGE" expression="" defaultValue="&quot;// Setup test data&quot;" alwaysStopAt="true" />
  <variable name="ACT" expression="" defaultValue="&quot;// Execute action&quot;" alwaysStopAt="true" />
  <variable name="ASSERT" expression="" defaultValue="&quot;// Verify results&quot;" alwaysStopAt="true" />
  <variable name="ERROR_CONDITION" expression="" defaultValue="&quot;invalid_data_provided&quot;" alwaysStopAt="true" />
  <variable name="ERROR_ARRANGE" expression="" defaultValue="&quot;// Setup invalid data&quot;" alwaysStopAt="true" />
  <variable name="EXCEPTION" expression="" defaultValue="&quot;\\InvalidArgumentException&quot;" alwaysStopAt="true" />
  <variable name="ERROR_ACT" expression="" defaultValue="&quot;// Execute action with invalid data&quot;" alwaysStopAt="true" />
  <context>
    <option name="PHP" value="true" />
  </context>
</template>
```

---

## 🎯 Checklist de Revisión de Código

### ✅ Checklist para Pull Requests

```markdown
## 📋 Checklist de Nomenclatura

### Nombres de Clases
- [ ] Clases en inglés, PascalCase
- [ ] Actions siguen patrón `{Verb}{Entity}Action`
- [ ] DTOs siguen patrón `{Entity}Data`
- [ ] Enums siguen patrón `{Entity}{Property}`
- [ ] Services siguen patrón `{Entity}{Purpose}Service`

### Nombres de Métodos y Variables
- [ ] Métodos en inglés, camelCase
- [ ] Variables en inglés, camelCase
- [ ] Parámetros descriptivos y en inglés
- [ ] Constantes en UPPER_SNAKE_CASE

### Base de Datos
- [ ] Tablas en inglés, snake_case, plural
- [ ] Columnas en inglés, snake_case
- [ ] Foreign keys siguen patrón `{table_singular}_id`
- [ ] Índices siguen patrón `{table}_{columns}_index`

### Enums
- [ ] Cases en PascalCase
- [ ] Valores backed en snake_case
- [ ] Métodos `getLabel()` y `getColor()` implementados
- [ ] Labels en español para UI

### Comentarios y Documentación
- [ ] Comentarios de lógica de negocio en español
- [ ] Comentarios técnicos en inglés
- [ ] DocBlocks en inglés
- [ ] Mensajes de excepción en español (para usuarios finales)

### Tests
- [ ] Nombres de tests en inglés
- [ ] Descripciones claras y descriptivas
- [ ] Métodos de test siguen patrón `it_{action}_{condition}`
```

---

## 🔧 Herramientas de Desarrollo

### Configuración de EditorConfig
```ini
# .editorconfig
root = true

[*.php]
charset = utf-8
end_of_line = lf
insert_final_newline = true
indent_style = space
indent_size = 4
trim_trailing_whitespace = true

[*.{js,json,yml,yaml}]
indent_size = 2
```

### Configuración de PHP CS Fixer
```php
// .php-cs-fixer.php
<?php

$finder = PhpCsFixer\Finder::create()
    ->in(__DIR__)
    ->exclude(['bootstrap', 'storage', 'vendor'])
    ->name('*.php')
    ->notName('*.blade.php')
    ->ignoreDotFiles(true)
    ->ignoreVCS(true);

return (new PhpCsFixer\Config())
    ->setRules([
        '@PSR12' => true,
        'array_syntax' => ['syntax' => 'short'],
        'ordered_imports' => ['sort_algorithm' => 'alpha'],
        'no_unused_imports' => true,
        'not_operator_with_successor_space' => true,
        'trailing_comma_in_multiline' => true,
        'phpdoc_scalar' => true,
        'unary_operator_spaces' => true,
        'binary_operator_spaces' => true,
        'blank_line_before_statement' => [
            'statements' => ['break', 'continue', 'declare', 'return', 'throw', 'try'],
        ],
        'phpdoc_single_line_var_spacing' => true,
        'phpdoc_var_without_name' => true,
    ])
    ->setFinder($finder);
```

### Script de validación completa
```bash
#!/bin/bash
# scripts/validate-all.sh

echo "🚀 Ejecutando validación completa de nomenclatura..."

# Ejecutar todos los scripts de validación
./scripts/validate-class-names.sh
./scripts/validate-method-names.sh
./scripts/validate-actions.sh
./scripts/validate-migrations.sh

# Ejecutar PHP CS Fixer
echo "🔧 Ejecutando PHP CS Fixer..."
./vendor/bin/php-cs-fixer fix --dry-run --diff

# Ejecutar PHPStan
echo "🔍 Ejecutando PHPStan..."
./vendor/bin/phpstan analyse

# Ejecutar tests
echo "🧪 Ejecutando tests..."
php artisan test

echo "✅ Validación completa finalizada"
```

---

## 🎮 Comandos Artisan Personalizados

### Comando para crear Action con nomenclatura correcta
```php
// app/Console/Commands/MakePromoSmartAction.php
<?php

namespace App\Console\Commands;

use Illuminate\Console\GeneratorCommand;

class MakePromoSmartAction extends GeneratorCommand
{
    protected $signature = 'make:ps-action {name} {--domain=}';
    protected $description = 'Create a new PromoSmart Action with correct naming conventions';
    
    protected function getStub()
    {
        return __DIR__.'/stubs/action.stub';
    }
    
    protected function getDefaultNamespace($rootNamespace)
    {
        $domain = $this->option('domain') ?: 'General';
        return $rootNamespace.'\\Actions\\'.$domain;
    }
    
    protected function buildClass($name)
    {
        $stub = parent::buildClass($name);
        
        // Reemplazar placeholders específicos
        $domain = $this->option('domain') ?: 'General';
        $stub = str_replace('{{domain}}', $domain, $stub);
        
        return $stub;
    }
}
```

### Stub para Actions
```php
// app/Console/Commands/stubs/action.stub
<?php

namespace {{namespace}};

use App\Data\{{domain}}\{{class}}Data;

class {{class}} 
{
    public function execute({{class}}Data $data): mixed
    {
        // TODO: Implementar lógica de negocio
    }
}
```

---

## 📊 Métricas de Calidad

### Script para generar reporte de nomenclatura
```bash
#!/bin/bash
# scripts/nomenclature-report.sh

echo "📊 Generando reporte de nomenclatura..."

echo "## Estadísticas de Clases"
echo "Total de clases: $(find app/ -name "*.php" -exec grep -l "^class " {} \; | wc -l)"
echo "Actions: $(find app/Actions/ -name "*.php" | wc -l)"
echo "DTOs: $(find app/Data/ -name "*Data.php" | wc -l)"
echo "Enums: $(find app/Enums/ -name "*.php" | wc -l)"
echo "Services: $(find app/Services/ -name "*Service.php" | wc -l)"

echo -e "\n## Validaciones"
echo "Clases con nombres incorrectos: $(find app/ -name "*.php" -exec grep -l "class.*[ÁÉÍÓÚáéíóúñÑ]" {} \; | wc -l)"
echo "Métodos con nombres incorrectos: $(find app/ -name "*.php" -exec grep -l "function.*[ÁÉÍÓÚáéíóúñÑ]" {} \; | wc -l)"

echo -e "\n✅ Reporte generado"
```
