# Ejemplos Prácticos de Nomenclatura

> **💡 Casos de Uso Reales**: Ejemplos completos de cómo aplicar las convenciones de nomenclatura en escenarios típicos de desarrollo.

> **🏗️ Organización**: Todos los ejemplos siguen **ADR-009: Organización por Dominios Simplificada** donde Actions/Data se organizan por dominio, pero Models/Enums/Services mantienen estructura plana.

---

## 🎬 Escenario 1: Crear nueva funcionalidad "Requote ProductItem"

### Contexto
Un analista necesita re-cotizar un ProductItem cuando el proveedor cambia el precio o especificaciones.

### 1. Action - Lógica de negocio
```php
// app/Actions/ProductItem/RequoteProductItemAction.php
<?php

namespace App\Actions\ProductItem;

use App\Data\ProductItem\RequoteProductItemData;
use App\Models\ProductItem;
use App\Services\StateTransitionService;
use App\Enums\ProductItemStatus;

class RequoteProductItemAction
{
    public function __construct(
        private StateTransitionService $stateTransitionService
    ) {}

    public function execute(RequoteProductItemData $data): ProductItem
    {
        $productItem = ProductItem::findOrFail($data->product_item_id);
        
        // Validar que el ítem puede ser re-cotizado
        if (!$this->canBeRequoted($productItem)) {
            throw new \InvalidArgumentException('El ítem no puede ser re-cotizado en su estado actual');
        }
        
        // Actualizar información de cotización
        $productItem->update([
            'supplier_price' => $data->new_price,
            'requote_reason' => $data->reason,
            'requoted_at' => now(),
        ]);
        
        // Transicionar estado si es necesario
        if ($productItem->status === ProductItemStatus::QuotedToCustomer) {
            $this->stateTransitionService->transition(
                $productItem, 
                ProductItemStatus::SourcingInProgress
            );
        }
        
        return $productItem->fresh();
    }
    
    private function canBeRequoted(ProductItem $productItem): bool
    {
        // Lógica de negocio: solo ciertos estados permiten re-cotización
        return in_array($productItem->status, [
            ProductItemStatus::SourcingInProgress,
            ProductItemStatus::QuotedToCustomer,
            ProductItemStatus::CustomerRevisionRequested,
        ]);
    }
}
```

### 2. DTO - Estructura de datos
```php
// app/Data/ProductItem/RequoteProductItemData.php
<?php

namespace App\Data\ProductItem;

use Spatie\LaravelData\Data;
use App\ValueObjects\Money;

class RequoteProductItemData extends Data
{
    public function __construct(
        public int $product_item_id,
        public Money $new_price,
        public string $reason, // Razón en español para mostrar en UI
        public ?string $notes = null,
    ) {}
    
    public static function rules(): array
    {
        return [
            'product_item_id' => ['required', 'exists:product_items,id'],
            'new_price.amount' => ['required', 'numeric', 'min:0'],
            'new_price.currency' => ['required', 'string', 'size:3'],
            'reason' => ['required', 'string', 'max:255'],
            'notes' => ['nullable', 'string', 'max:1000'],
        ];
    }
}
```

### 3. Test - Verificación
```php
// tests/Feature/Actions/ProductItem/RequoteProductItemActionTest.php
<?php

namespace Tests\Feature\Actions\ProductItem;

use Tests\TestCase;
use App\Models\ProductItem;
use App\Models\Project;
use App\Actions\ProductItem\RequoteProductItemAction;
use App\Data\ProductItem\RequoteProductItemData;
use App\Enums\ProductItemStatus;
use App\ValueObjects\Money;

class RequoteProductItemActionTest extends TestCase
{
    /** @test */
    public function it_can_requote_a_product_item_successfully()
    {
        // Arrange
        $project = Project::factory()->create();
        $productItem = ProductItem::factory()->create([
            'project_id' => $project->id,
            'status' => ProductItemStatus::SourcingInProgress,
            'supplier_price' => new Money(100, 'USD'),
        ]);
        
        $data = new RequoteProductItemData(
            product_item_id: $productItem->id,
            new_price: new Money(120, 'USD'),
            reason: 'Aumento de precio del proveedor',
        );
        
        // Act
        $action = app(RequoteProductItemAction::class);
        $result = $action->execute($data);
        
        // Assert
        $this->assertEquals(new Money(120, 'USD'), $result->supplier_price);
        $this->assertEquals('Aumento de precio del proveedor', $result->requote_reason);
        $this->assertNotNull($result->requoted_at);
    }
    
    /** @test */
    public function it_throws_exception_when_product_item_cannot_be_requoted()
    {
        // Arrange
        $productItem = ProductItem::factory()->create([
            'status' => ProductItemStatus::Draft, // Estado que no permite re-cotización
        ]);
        
        $data = new RequoteProductItemData(
            product_item_id: $productItem->id,
            new_price: new Money(120, 'USD'),
            reason: 'Test reason',
        );
        
        // Act & Assert
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('El ítem no puede ser re-cotizado en su estado actual');
        
        $action = app(RequoteProductItemAction::class);
        $action->execute($data);
    }
}
```

### 4. Filament Resource - Interfaz de usuario
```php
// app/Filament/Resources/ProductItemResource.php (fragmento)
use App\Actions\ProductItem\RequoteProductItemAction;
use App\Data\ProductItem\RequoteProductItemData;

public static function getActions(): array
{
    return [
        Action::make('requote')
            ->label('Re-cotizar')
            ->icon('heroicon-o-currency-dollar')
            ->form([
                TextInput::make('new_price.amount')
                    ->label('Nuevo Precio')
                    ->numeric()
                    ->required(),
                Select::make('new_price.currency')
                    ->label('Moneda')
                    ->options(['USD' => 'USD', 'CLP' => 'CLP'])
                    ->default('USD'),
                Textarea::make('reason')
                    ->label('Razón de Re-cotización')
                    ->required(),
            ])
            ->action(function (ProductItem $record, array $data) {
                $actionData = new RequoteProductItemData(
                    product_item_id: $record->id,
                    new_price: new Money($data['new_price']['amount'], $data['new_price']['currency']),
                    reason: $data['reason'],
                );
                
                app(RequoteProductItemAction::class)->execute($actionData);
            })
            ->visible(fn (ProductItem $record) => $record->status->allowsRequoting()),
    ];
}
```

---

## 🎬 Escenario 2: Agregar nuevo estado al ProductItem

### Contexto
Se necesita agregar un nuevo estado "PendingClientApproval" al flujo de ProductItem.

### 1. Actualizar Enum
```php
// app/Enums/ProductItemStatus.php (fragmento)
enum ProductItemStatus: string
{
    // Estados existentes...
    case QuotedToCustomer = 'quoted_to_customer';
    case PendingClientApproval = 'pending_client_approval'; // ← NUEVO
    case CustomerRevisionRequested = 'customer_revision_requested';
    // Más estados...
    
    public function getLabel(): string
    {
        return match($this) {
            // Labels existentes...
            self::QuotedToCustomer => 'Cotizado al Cliente',
            self::PendingClientApproval => 'Pendiente Aprobación Cliente', // ← NUEVO
            self::CustomerRevisionRequested => 'Revisión Solicitada por Cliente',
            // Más labels...
        };
    }
    
    public function getColor(): string
    {
        return match($this) {
            // Colores existentes...
            self::QuotedToCustomer => 'green',
            self::PendingClientApproval => 'orange', // ← NUEVO
            self::CustomerRevisionRequested => 'yellow',
            // Más colores...
        };
    }
    
    public function getPhase(): string
    {
        return match($this) {
            // Fases existentes...
            self::QuotedToCustomer,
            self::PendingClientApproval, // ← NUEVO
            self::CustomerRevisionRequested => 'customer_interaction',
            // Más fases...
        };
    }
}
```

### 2. Migración de base de datos
```php
// database/migrations/2024_01_15_add_pending_client_approval_to_product_item_status.php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // No necesitamos modificar la estructura de la tabla
        // porque usamos enum backed con string
        
        // Pero podemos agregar un índice si es necesario
        Schema::table('product_items', function (Blueprint $table) {
            // Comentario: Optimizar consultas por el nuevo estado
            $table->index(['status', 'updated_at'], 'product_items_status_updated_index');
        });
    }
    
    public function down(): void
    {
        Schema::table('product_items', function (Blueprint $table) {
            $table->dropIndex('product_items_status_updated_index');
        });
    }
};
```

### 3. Actualizar StateTransitionService
```php
// app/Services/StateTransitionService.php (fragmento)
class StateTransitionService
{
    private function getValidTransitions(): array
    {
        return [
            // Transiciones existentes...
            ProductItemStatus::QuotedToCustomer->value => [
                ProductItemStatus::PendingClientApproval->value, // ← NUEVO
                ProductItemStatus::CustomerRevisionRequested->value,
                ProductItemStatus::PendingVmApproval->value,
            ],
            ProductItemStatus::PendingClientApproval->value => [ // ← NUEVO
                ProductItemStatus::PendingVmApproval->value,
                ProductItemStatus::CustomerRevisionRequested->value,
            ],
            // Más transiciones...
        ];
    }
}
```

### 4. Test para el nuevo estado
```php
// tests/Unit/Enums/ProductItemStatusTest.php (fragmento)
class ProductItemStatusTest extends TestCase
{
    /** @test */
    public function pending_client_approval_has_correct_properties()
    {
        $status = ProductItemStatus::PendingClientApproval;
        
        $this->assertEquals('pending_client_approval', $status->value);
        $this->assertEquals('Pendiente Aprobación Cliente', $status->getLabel());
        $this->assertEquals('orange', $status->getColor());
        $this->assertEquals('customer_interaction', $status->getPhase());
    }
    
    /** @test */
    public function can_transition_from_quoted_to_customer_to_pending_client_approval()
    {
        $productItem = ProductItem::factory()->create([
            'status' => ProductItemStatus::QuotedToCustomer,
        ]);
        
        $transitionService = app(StateTransitionService::class);
        
        $this->assertTrue(
            $transitionService->canTransition($productItem, ProductItemStatus::PendingClientApproval)
        );
    }
}
```

---

## 🎬 Escenario 3: Crear nueva entidad "ProductSpecification"

### Contexto
Se necesita una entidad separada para manejar las especificaciones técnicas de los productos.

### 1. Modelo
```php
// app/Models/ProductSpecification.php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProductSpecification extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'product_item_id',
        'specification_key',
        'specification_value',
        'is_required',
        'validation_rules',
    ];
    
    protected $casts = [
        'is_required' => 'boolean',
        'validation_rules' => 'array',
    ];
    
    // Relaciones
    public function productItem(): BelongsTo
    {
        return $this->belongsTo(ProductItem::class);
    }
    
    // Métodos de negocio
    public function isValid(): bool
    {
        // Validar especificación según reglas de negocio
        if ($this->is_required && empty($this->specification_value)) {
            return false;
        }
        
        return $this->validateAgainstRules();
    }
    
    private function validateAgainstRules(): bool
    {
        // Lógica de validación basada en validation_rules
        return true; // Simplificado para el ejemplo
    }
}
```

### 2. Migración
```php
// database/migrations/2024_01_15_create_product_specifications_table.php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('product_specifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_item_id')->constrained()->onDelete('cascade');
            $table->string('specification_key'); // ej: 'material', 'color', 'size'
            $table->text('specification_value'); // Valor de la especificación
            $table->boolean('is_required')->default(false);
            $table->json('validation_rules')->nullable(); // Reglas de validación
            $table->timestamps();
            
            // Índices
            $table->unique(['product_item_id', 'specification_key'], 'product_specs_unique');
            $table->index(['specification_key', 'specification_value'], 'product_specs_search');
        });
    }
    
    public function down(): void
    {
        Schema::dropIfExists('product_specifications');
    }
};
```

### 3. Factory
```php
// database/factories/ProductSpecificationFactory.php
<?php

namespace Database\Factories;

use App\Models\ProductItem;
use App\Models\ProductSpecification;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductSpecificationFactory extends Factory
{
    protected $model = ProductSpecification::class;
    
    public function definition(): array
    {
        return [
            'product_item_id' => ProductItem::factory(),
            'specification_key' => $this->faker->randomElement(['material', 'color', 'size', 'weight']),
            'specification_value' => $this->faker->word(),
            'is_required' => $this->faker->boolean(30), // 30% probabilidad de ser requerido
            'validation_rules' => null,
        ];
    }
    
    public function required(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_required' => true,
        ]);
    }
    
    public function withValidationRules(array $rules): static
    {
        return $this->state(fn (array $attributes) => [
            'validation_rules' => $rules,
        ]);
    }
}
```

---

## 📚 Patrones Identificados

### ✅ Consistencia en nomenclatura:
- **Entidades**: Siempre en inglés, PascalCase
- **Tablas**: Plural, snake_case, inglés
- **Relaciones**: Descriptivas en inglés
- **Métodos de negocio**: Pueden tener comentarios en español

### ✅ Estructura de archivos (ADR-009):
- **Actions**: `app/Actions/{Domain}/{Verb}{Entity}Action.php` (por dominio)
- **DTOs**: `app/Data/{Domain}/{Entity}Data.php` (por dominio)
- **Models**: `app/Models/{Entity}.php` (estructura plana - compartidos)
- **Enums**: `app/Enums/{Entity}{Property}.php` (estructura plana - compartidos)
- **Services**: `app/Services/{Purpose}Service.php` (estructura plana - cross-domain)
- **Tests**: `tests/{Type}/Actions/{Domain}/{Class}Test.php` (por dominio)
- **Migrations**: `{timestamp}_{action}_{table}_table.php`

### ✅ Convenciones de código:
- **Comentarios de lógica**: En español cuando explican reglas de negocio
- **Variables temporales**: En inglés
- **Mensajes de excepción**: En español (para usuarios finales)
- **Labels de UI**: En español
