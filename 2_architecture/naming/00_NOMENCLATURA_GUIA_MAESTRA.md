# Guía Maestra de Nomenclatura PromoSmart

> **🎯 Documento Central**: Todo lo que necesitas saber sobre nomenclatura en PromoSmart. Para consulta rápida diaria, ve a [01_guia_rapida_desarrollador.md](01_guia_rapida_desarrollador.md).

---

## 🚀 Inicio <PERSON> (para desarrolladores nuevos)

### Regla de Oro
- **Documentación y comunicación**: Español
- **Código fuente**: Inglés (siguiendo Laravel/PSR)
- **Base de datos**: Inglés, `snake_case`
- **Comentarios de lógica de negocio**: Español

### Conversión Rápida - Top 10 Conceptos

| Concepto Negocio (Español) | Modelo (Inglés) | Tabla BD | Ejemplo Uso |
|---|---|---|---|
| Proyecto | `Project` | `projects` | `Project::find(1)->name` |
| Ítem de Producto | `ProductItem` | `product_items` | `ProductItem::where('status', 'draft')` |
| Cliente | `Customer` | `customers` | `Customer::create(['name' => 'BeerCO'])` |
| Proveedor | `Supplier` | `suppliers` | `Supplier::active()->get()` |
| Registro de Envío | `ImportShipmentRecord` | `import_shipment_records` | `ImportShipmentRecord::pending()` |
| Usuario | `User` | `users` | `User::role('analyst')->get()` |
| Cotización de Proveedor | `SupplierQuotationItem` | `supplier_quotation_items` | `SupplierQuotationItem::latest()` |
| Cotización de Cliente | `CustomerQuotationItem` | `customer_quotation_items` | `CustomerQuotationItem::approved()` |
| Maqueta Virtual | `VirtualMockup` | `virtual_mockups` | `VirtualMockup::pending()` |
| Muestra de Preproducción | `PreProductionSample` | `pre_production_samples` | `PreProductionSample::approved()` |

---

## 🏗️ Organización por Dominios (ADR-009)

PromoSmart usa **organización por dominios de negocio simplificada** según ADR-009:

### Estructura por Dominio vs Estructura Plana

```
app/
├── Actions/            # 📁 POR DOMINIO - Casos de uso
│   ├── ProductItem/    # Domain: Product Item Lifecycle
│   │   ├── CreateProductItemAction.php
│   │   ├── UpdateProductItemAction.php
│   │   └── RequoteProductItemAction.php
│   ├── Project/        # Domain: Project Management
│   │   ├── CreateProjectAction.php
│   │   └── CalculateProjectStatusAction.php
│   └── Financial/      # Domain: Financial Operations
│       └── CalculateCostingAction.php
├── Data/              # 📁 POR DOMINIO - DTOs
│   ├── ProductItem/
│   │   ├── CreateProductItemData.php
│   │   └── RequoteProductItemData.php
│   └── Project/
│       └── CreateProjectData.php
├── Services/          # 📄 PLANA - Lógica cross-domain
│   ├── StateTransitionService.php
│   └── ProjectStatusCalculatorService.php
├── Models/            # 📄 PLANA - Compartidos
│   ├── ProductItem.php
│   ├── Project.php
│   └── Customer.php
├── Enums/             # 📄 PLANA - Business rules compartidas
│   ├── ProductItemStatus.php
│   └── ProjectStatus.php
└── Http/              # 📄 LARAVEL ESTÁNDAR
    ├── Controllers/
    └── Resources/      # Filament resources
```

### Principios de Organización

1. **📁 Por Dominio**: Actions y Data se organizan por contexto de negocio
2. **📄 Estructura Plana**: Models, Enums, Services son compartidos cross-domain
3. **📄 Laravel Estándar**: Http mantiene estructura familiar del framework

---

## 📋 Convenciones por Tipo de Artefacto

### 🏗️ Entidades y Modelos

**Pattern**: `PascalCase` en inglés, siguiendo convenciones Laravel

```php
// ✅ Correcto
class ProductItem extends Model
{
    protected $table = 'product_items'; // snake_case
    
    // Relaciones en inglés
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }
    
    // Métodos de negocio pueden tener comentarios en español
    public function canBeRequoted(): bool
    {
        // Verificar si el ítem puede ser re-cotizado según reglas de negocio
        return $this->status->allowsRequoting();
    }
}

// ❌ Incorrecto
class ItemProducto extends Model { }
class ProductoItem extends Model { }
```

### 🎛️ Estados y Enums

**Pattern**: Cases en `PascalCase`, valores backed en `snake_case`

```php
enum ProductItemStatus: string
{
    case Draft = 'draft';
    case ReadyForSourcing = 'ready_for_sourcing';
    case SourcingInProgress = 'sourcing_in_progress';
    case QuotedToCustomer = 'quoted_to_customer';
    
    // Métodos UI obligatorios (en español para interfaz)
    public function getLabel(): string
    {
        return match($this) {
            self::Draft => 'Borrador',
            self::ReadyForSourcing => 'Listo para Sourcing',
            self::SourcingInProgress => 'Sourcing en Progreso',
            self::QuotedToCustomer => 'Cotizado al Cliente',
        };
    }
    
    public function getColor(): string
    {
        return match($this) {
            self::Draft => 'gray',
            self::ReadyForSourcing => 'blue',
            self::SourcingInProgress => 'yellow',
            self::QuotedToCustomer => 'green',
        };
    }
    
    // Métodos de lógica de negocio
    public function allowsRequoting(): bool
    {
        return in_array($this, [
            self::SourcingInProgress,
            self::QuotedToCustomer,
        ]);
    }
}
```

### ⚡ Actions y Servicios

**Pattern Actions**: `{Verb}{Entity}Action`
**Ubicación**: `app/Actions/{Domain}/` (organización por dominio según ADR-009)

```php
// app/Actions/ProductItem/CreateProductItemAction.php
class CreateProductItemAction
{
    public function execute(CreateProductItemData $data): ProductItem
    {
        // Validar datos de entrada según reglas de negocio
        $this->validateBusinessRules($data);

        return ProductItem::create([
            'project_id' => $data->project_id,
            'name' => $data->name,
            'status' => ProductItemStatus::Draft,
        ]);
    }

    private function validateBusinessRules(CreateProductItemData $data): void
    {
        // Lógica de validación con comentarios en español
    }
}
```

**Pattern Services**: `{Entity}{Purpose}Service`
**Ubicación**: `app/Services/` (estructura plana para componentes compartidos)

```php
// app/Services/StateTransitionService.php
class StateTransitionService
{
    public function canTransition(ProductItem $item, ProductItemStatus $newStatus): bool
    {
        // Lógica de transición de estados
        return $this->getValidTransitions($item->status)->contains($newStatus);
    }
}
```

**Nota**: Seguimos **ADR-009: Organización por Dominios Simplificada** donde Actions/Data se organizan por dominio, pero Services/Models/Enums mantienen estructura plana por ser compartidos cross-domain.

### 🗃️ Base de Datos

**Tablas**: `snake_case` en inglés, plural
**Columnas**: `snake_case` en inglés
**Índices**: `{table}_{column(s)}_index`
**Foreign Keys**: `{referenced_table_singular}_id`

```php
// Migration example
Schema::create('product_items', function (Blueprint $table) {
    $table->id();
    $table->foreignId('project_id')->constrained();
    $table->string('name');
    $table->string('status')->default('draft');
    $table->json('specifications')->nullable();
    $table->timestamps();
    
    // Índices
    $table->index(['project_id', 'status'], 'product_items_project_status_index');
});
```

### 🎨 Recursos Filament

**Pattern**: `{Entity}Resource`
**Ubicación**: `app/Filament/Resources/`

```php
// app/Filament/Resources/ProductItemResource.php
class ProductItemResource extends Resource
{
    protected static ?string $model = ProductItem::class;
    
    // Labels en español para interfaz de usuario
    protected static ?string $modelLabel = 'Ítem de Producto';
    protected static ?string $pluralModelLabel = 'Ítems de Producto';
    
    public static function form(Form $form): Form
    {
        return $form->schema([
            TextInput::make('name')
                ->label('Nombre del Producto')
                ->required(),
            Select::make('status')
                ->label('Estado')
                ->options(ProductItemStatus::class)
                ->required(),
        ]);
    }
}
```

---

## 🔍 Búsqueda Rápida por Contexto

### "Estoy creando un ProductItem..."
- **Modelo**: `ProductItem` (app/Models/ProductItem.php) - *estructura plana*
- **Action**: `CreateProductItemAction` (app/Actions/ProductItem/) - *por dominio*
- **DTO**: `CreateProductItemData` (app/Data/ProductItem/) - *por dominio*
- **Resource**: `ProductItemResource` (app/Http/Resources/) - *Laravel estándar*
- **Test**: `CreateProductItemActionTest` (tests/Feature/Actions/ProductItem/) - *por dominio*

### "Estoy trabajando con estados..."
- **Enum**: `ProductItemStatus` (app/Enums/ProductItemStatus.php) - *estructura plana*
- **Service**: `StateTransitionService` (app/Services/) - *estructura plana*
- **Métodos obligatorios**: `getLabel()`, `getColor()`
- **Lógica de transición**: NO en el enum, usar `StateTransitionService`

### "Estoy creando una migración..."
- **Nombre**: `{timestamp}_create_{table}_table.php`
- **Tabla**: `snake_case`, plural, inglés
- **Columnas**: `snake_case`, inglés
- **Foreign keys**: `{table_singular}_id`

### "Estoy organizando código nuevo..."
- **Por dominio**: Actions, Data (DTOs)
- **Estructura plana**: Models, Enums, Services (compartidos cross-domain)
- **Laravel estándar**: Http (Controllers, Resources), Tests

---

## ❓ FAQ - Preguntas Frecuentes

### ¿Cuándo usar español vs inglés?
- **Español**: Documentación, comentarios de lógica de negocio, labels de UI, mensajes de error
- **Inglés**: Nombres de clases, métodos, variables, tablas, columnas

### ¿Cómo nombrar un nuevo enum?
1. Nombre del enum: `{Entity}{Property}` (ej. `ProductItemStatus`)
2. Cases: `PascalCase` (ej. `ReadyForSourcing`)
3. Valores: `snake_case` (ej. `'ready_for_sourcing'`)
4. Métodos obligatorios: `getLabel()`, `getColor()`

### ¿Qué hacer con términos únicos del negocio?
- **En código**: Traducir al inglés más cercano (ej. "Sourcing" se mantiene)
- **En UI**: Mantener término original en español
- **En documentación**: Explicar el término y su traducción

### ¿Cómo manejar acrónimos?
- **En código**: Expandir si es posible (ej. `ImportShipmentRecord` no `ISR`)
- **En UI**: Usar acrónimo si es conocido (ej. "PDV" para "Punto de Venta")

---

## 📚 Referencias

- **Guía rápida**: [01_guia_rapida_desarrollador.md](01_guia_rapida_desarrollador.md)
- **Ejemplos prácticos**: [02_ejemplos_practicos.md](02_ejemplos_practicos.md)
- **Herramientas**: [03_validacion_y_herramientas.md](03_validacion_y_herramientas.md)
- **Sincronización de estados**: [../10_especificacion_sincronizacion_estados.md](../10_especificacion_sincronizacion_estados.md)
- **ADR original**: [../adrs/adr003_politica_idioma.md](../adrs/adr003_politica_idioma.md)
- **Glosario técnico**: [../12_glosario.md](../12_glosario.md)
