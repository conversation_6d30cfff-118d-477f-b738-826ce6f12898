# Arquitectura Integrada PromoSmart

> **Especificación Arquitectónica Simplificada**  
> Arquitectura CRUD + Version Tracking enfocada en las necesidades reales de negocio.

---

## 1. Visión Arquitectónica

PromoSmart implementa una **arquitectura CRUD simplificada** con **version tracking selectivo** para cumplir el verdadero requisito de negocio: **"lista de versiones de productos"** derivados de re-cotizaciones y cambios significativos.

### Estrategia Simplificada

**CRUD + Version Tracking**
- **ProductItem lifecycle**: Estados con enum methods + version tracking para re-cotizaciones
- **Project operations**: CRUD simple con estado derivado calculado
- **User management**: CRUD estándar
- **Master data**: CRUD tradicional (Customers, Suppliers, Catalogs)
- **Configuration**: Settings y preferences simples
- **Reporting**: Queries directos optimizados

### Patrones Adoptados
- **Actions + DTOs**: Orquestación y validación tipada
- **Enums con métodos**: Business rules + UI logic
- **Services**: Lógica compleja cross-domain
- **Version Tracking**: Para cambios significativos solamente

### Principios Fundamentales

1. **Simplicidad Primero**: Implementación directa sin over-engineering
2. **Tipado Fuerte**: DTOs y Enums para contratos explícitos
3. **Version Tracking Selectivo**: Solo para cambios derivados de re-cotizaciones
4. **Performance Optimizada**: Queries directos, sin overhead de Event Sourcing

### Decisión Arquitectónica

**Requisito Real Clarificado**: "Solo necesito lista de versiones"

**Por lo tanto:**
- ❌ **NO Event Sourcing** - Overkill para el requisito real
- ❌ **NO Reconstrucción histórica completa** - No necesario
- ✅ **SÍ Version tracking simple** - Exactamente lo que se necesita
- ✅ **SÍ CRUD + tabla adicional** - ~200 líneas vs ~2000 líneas

---

## 2. Arquitectura CRUD + Version Tracking

```
┌─────────────────────────────────────────────────────────┐
│                    UI Layer (Filament)                  │
│  Admin Panels | Forms | Tables | Charts | Dashboards   │
└─────────────────┬───────────────────────────────────────┘
                  │ HTTP Requests + Form Data
┌─────────────────▼───────────────────────────────────────┐
│             Application Layer (Actions + DTOs)          │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                CRUD Actions                         │ │  
│  │ ┌───────────────────────────────────────────────────┐ │ │
│  │ │ ProductItem Actions + Version Tracking          │ │ │
│  │ │ ├ CreateProductItemAction                        │ │ │
│  │ │ ├ UpdateProductItemAction (+ version tracking)  │ │ │
│  │ │ └ RequoteProductItemAction (+ version tracking) │ │ │
│  │ └───────────────────────────────────────────────────┘ │ │
│  │ ┌───────────────────────────────────────────────────┐ │ │
│  │ │ Project & Other Actions                         │ │ │
│  │ │ ├ CreateProjectAction                            │ │ │
│  │ │ ├ UpdateProjectAction                            │ │ │
│  │ │ ├ CreateProjectBaselineAction                  │ │ │
│  │ │ ├ User/Customer/Supplier Actions                 │ │ │
│  │ │ └ CalculateProjectStatusAction                   │ │ │
│  │ └───────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────┬───────────────────────────────────────────────┘
          │ Validated DTOs
┌─────────▼───────────────────────────────────────────────┐
│                    Domain Layer                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Standard Eloquent Models               │ │
│  │ ┌───────────────────────────────────────────────────┐ │ │
│  │ │ ProductItem + ProductItemVersion + Project      │ │ │
│  │ │ User + Customer + Supplier + Others             │ │ │
│  │ └───────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────┬───────────────────────────────────────────────┘
          │ Direct DB Operations + Version Service
┌─────────▼───────────────────────────────────────────────┐
│              Infrastructure Layer                       │
│  ┌─────────────────────────────────────────────────────┐ │
│  │              Standard Laravel Infrastructure        │ │
│  │ ┌───────────────────────────────────────────────────┐ │ │
│  │ │ ProductItemVersionService                       │ │ │
│  │ │ ProjectStatusCalculatorService                  │ │ │
│  │ │ ProjectProfitabilityService                     │ │ │
│  │ │ StateTransitionService                          │ │ │
│  │ │ ReportGenerationService                         │ │ │
│  │ │ Standard Logging + Cache                        │ │ │
│  │ └───────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
│                                                         │
│  ┌─────────────────────────────────────────────────────┐ │
│  │                 Database (PostgreSQL)              │ │
│  │ ┌───────────────────────────────────────────────────┐ │ │
│  │ │ Standard Tables + product_item_versions         │ │ │
│  │ │ (users, projects, product_items, customers...)  │ │ │
│  │ └───────────────────────────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

---

## 3. Version Tracking Específico

### 3.1 Schema de Base de Datos

```sql
-- Tabla principal sin cambios
CREATE TABLE product_items (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    name VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL,
    specifications JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- Nueva tabla para tracking de versiones y hitos de negocio
CREATE TABLE product_item_versions (
    id BIGINT PRIMARY KEY,
    product_item_id BIGINT NOT NULL,
    version_number INT NOT NULL,
    change_type VARCHAR(50) NOT NULL, -- 'CREATION', 'REQUOTE', 'SPEC_UPDATE', 'STATUS_CHANGE', 'MILESTONE'
    business_milestone VARCHAR(50) NULL, -- 'VM_APPROVED', 'PPS_REJECTED', etc.
    changes_summary TEXT,
    previous_data JSON,              -- Snapshot de lo que cambió (antes)
    new_data JSON,                   -- Snapshot de lo que cambió (después)  
    changed_by BIGINT,
    changed_at TIMESTAMP,
    created_at TIMESTAMP,
    
    FOREIGN KEY (product_item_id) REFERENCES product_items(id),
    FOREIGN KEY (changed_by) REFERENCES users(id)
);
```

### 3.2 Ejemplo Práctico - RequoteProductItemAction

```php
// app/Actions/ProductItem/RequoteProductItemAction.php  
class RequoteProductItemAction
{
    public function __construct(
        private ProductItemVersionService $versionService
    ) {}
    
    public function execute(RequoteProductItemData $data): ProductItem
    {
        $item = $data->productItem;
        $previousData = [
            'specifications' => $item->specifications,
            'status' => $item->status->value,
            'price' => $data->previousPrice,
        ];
        
        // Update item con CRUD simple
        $item->update([
            'specifications' => $data->newSpecifications,
            'status' => $data->newStatus,
        ]);
        
        $newData = [
            'specifications' => $data->newSpecifications,
            'status' => $data->newStatus->value,
            'price' => $data->newPrice,
        ];
        
        // Siempre crear version para re-quotes
        $this->versionService->createVersion(
            item: $item,
            changeType: 'requote',
            previousData: $previousData,
            newData: $newData,
            summary: $data->reason
        );
        
        // Logging estándar
        Log::info('ProductItem requoted', [
            'product_item_id' => $item->id,
            'previous_price' => $data->previousPrice,
            'new_price' => $data->newPrice,
            'reason' => $data->reason,
            'user_id' => auth()->id(),
        ]);
        
        return $item;
    }
}
```

### 3.3 ProductItemVersionService (Log de Hitos de Negocio)

Este servicio implementa el **Log de Hitos de Negocio** requerido por `RF-18`, registrando todos los cambios significativos y hitos del ciclo de vida del ProductItem.

```php
// app/Services/ProductItemVersionService.php
class ProductItemVersionService
{
    public function createVersion(
        ProductItem $item,
        string $changeType,
        array $previousData,
        array $newData,
        ?string $summary = null,
        ?string $milestone = null
    ): ProductItemVersion {
        return ProductItemVersion::create([
            'product_item_id' => $item->id,
            'version_number' => $this->getNextVersionNumber($item),
            'change_type' => $changeType,
            'business_milestone' => $milestone,
            'changes_summary' => $summary ?? $this->generateSummary($changeType, $previousData, $newData, $milestone),
            'previous_data' => $previousData,
            'new_data' => $newData,
            'changed_by' => auth()->id(),
            'changed_at' => now(),
        ]);
    }
    
    public function getVersionHistory(ProductItem $item): Collection
    {
        return ProductItemVersion::where('product_item_id', $item->id)
            ->with('changedBy:id,name')
            ->orderBy('version_number', 'desc')
            ->get()
            ->map(fn($version) => [
                'version' => $version->version_number,
                'type' => $version->change_type,
                'summary' => $version->changes_summary,
                'changed_by' => $version->changedBy->name,
                'changed_at' => $version->changed_at->format('d/m/Y H:i'),
                'previous_data' => $version->previous_data,
                'new_data' => $version->new_data,
            ]);
    }
    
    private function generateSummary(string $changeType, array $previous, array $new, ?string $milestone = null): string
    {
        if ($milestone) {
            return $this->generateMilestoneSummary($milestone, $new);
        }

        return match($changeType) {
            'CREATION' => 'Creación inicial',
            'REQUOTE' => $this->generateRequoteSummary($previous, $new),
            'SPEC_UPDATE' => $this->generateSpecUpdateSummary($previous, $new),
            'STATUS_CHANGE' => $this->generateStatusChangeSummary($previous, $new),
            'MANUAL_CORRECTION' => $this->generateManualCorrectionSummary($previous, $new),
            default => 'Cambios realizados'
        };
    }

    private function generateMilestoneSummary(string $milestone, array $data): string
    {
        $approver = $data['approved_by'] ?? 'Usuario';
        $reason = isset($data['rejection_reason']) ? ": {$data['rejection_reason']}" : '';

        return match($milestone) {
            'VM_APPROVED' => "Maqueta Virtual aprobada por {$approver}",
            'VM_REJECTED' => "Maqueta Virtual rechazada por {$approver}{$reason}",
            'PPS_APPROVED' => "Muestra de Pre-Producción aprobada por {$approver}",
            'PPS_REJECTED' => "Muestra de Pre-Producción rechazada por {$approver}{$reason}",
            'PI_APPROVED' => "Factura Proforma aprobada por {$approver}",
            'PI_REJECTED' => "Factura Proforma rechazada por {$approver}{$reason}",
            default => "Hito de negocio registrado: {$milestone}"
        };
    }
    
    private function generateRequoteSummary(array $previous, array $new): string
    {
        $changes = [];
        
        if (isset($previous['price'], $new['price']) && $previous['price'] !== $new['price']) {
            $changes[] = "Precio: {$previous['price']} → {$new['price']}";
        }
        
        if (isset($previous['status'], $new['status']) && $previous['status'] !== $new['status']) {
            $changes[] = "Estado: {$previous['status']} → {$new['status']}";
        }
        
        return empty($changes) ? 'Re-cotización procesada' : implode(', ', $changes);
    }
    
    private function generateVmApprovalSummary(array $previous, array $new): string
    {
        $result = $new['approval_result'] ?? 'unknown';
        $approver = $new['approved_by'] ?? 'Usuario';
        
        return match($result) {
            'approved' => "Maqueta Virtual aprobada por {$approver}",
            'rejected' => "Maqueta Virtual rechazada por {$approver}: {$new['rejection_reason'] ?? 'Sin razón especificada'}",
            default => 'Resultado de aprobación de Maqueta Virtual procesado'
        };
    }
    
    private function generatePpsApprovalSummary(array $previous, array $new): string
    {
        $result = $new['approval_result'] ?? 'unknown';
        $approver = $new['approved_by'] ?? 'Usuario';
        
        return match($result) {
            'approved' => "Muestra Pre-producción aprobada por {$approver}",
            'rejected' => "Muestra Pre-producción rechazada por {$approver}: {$new['rejection_reason'] ?? 'Sin razón especificada'}",
            default => 'Resultado de aprobación de Muestra Pre-producción procesado'
        };
    }
    
    private function generatePiApprovalSummary(array $previous, array $new): string
    {
        $result = $new['approval_result'] ?? 'unknown';
        $approver = $new['approved_by'] ?? 'Usuario';
        
        return match($result) {
            'approved' => "Inspección Pre-producción aprobada por {$approver}",
            'rejected' => "Inspección Pre-producción rechazada por {$approver}: {$new['rejection_reason'] ?? 'Sin razón especificada'}",
            default => 'Resultado de Inspección Pre-producción procesado'
        };
    }
    
    private function generateCustomerRejectionSummary(array $previous, array $new): string
    {
        $reason = $new['rejection_reason'] ?? 'Sin razón especificada';
        $customer = $new['customer_name'] ?? 'Cliente';
        
        return "Cliente {$customer} rechazó la propuesta: {$reason}";
    }
    
    private function generateManualCorrectionSummary(array $previous, array $new): string
    {
        $reason = $new['correction_reason'] ?? 'Corrección administrativa';
        $admin = $new['corrected_by'] ?? 'Administrador';
        
        $statusChange = '';
        if (isset($previous['status'], $new['status']) && $previous['status'] !== $new['status']) {
            $statusChange = " (Estado: {$previous['status']} → {$new['status']})";
        }
        
        return "Corrección manual por {$admin}: {$reason}{$statusChange}";
    }
}
```

---

## 4. Estructura de Dominios Simplificada

### 4.1 Organización de Código

```
app/
├── Actions/            # Orquestación de casos de uso
│   ├── Project/
│   │   ├── CreateProjectAction.php      # CRUD simple
│   │   ├── UpdateProjectAction.php      # CRUD simple
│   │   └── CalculateProjectStatusAction.php # Service call
│   ├── ProductItem/
│   │   ├── CreateProductItemAction.php  # CRUD simple
│   │   ├── UpdateProductItemAction.php  # CRUD + version tracking
│   │   └── RequoteProductItemAction.php # CRUD + version tracking
│   └── User/
│       ├── CreateUserAction.php         # CRUD simple
│       ├── UpdateUserAction.php         # CRUD simple
│       └── ManageUserRolesAction.php    # Role management
├── Data/               # DTOs con validación
│   ├── Project/
│   │   ├── CreateProjectData.php        # Validación standard
│   │   └── UpdateProjectData.php        # Validación standard
│   ├── ProductItem/
│   │   ├── CreateProductItemData.php    # Validación standard
│   │   ├── UpdateProductItemData.php    # Validación standard
│   │   └── RequoteProductItemData.php   # Validación + change tracking
│   └── User/
│       ├── CreateUserData.php           # Validación standard
│       └── UpdateUserData.php           # Validación standard
├── Services/           # Lógica compleja cross-domain
│   ├── ProductItemVersionService.php    # Version management
│   ├── ProjectStatusCalculatorService.php # Status calculation
│   └── ReportGenerationService.php      # Cross-domain reporting
├── Models/             # Standard Eloquent
│   ├── ProductItem.php                  # Standard model
│   ├── ProductItemVersion.php           # Version tracking model
│   ├── Project.php                      # Standard model
│   ├── User.php                         # Standard model
│   └── Customer.php                     # Standard model
├── Enums/              # Business rules + UI logic (mantener)
│   ├── ProductItemStatus.php            # Con métodos UI + business logic
│   ├── ProjectStatus.php                # Con métodos de cálculo
│   └── UserRole.php                     # Role management
└── Http/               # Standard Laravel
    ├── Controllers/     # Standard controllers
    └── Resources/       # Filament resources
```

### 4.2 Patrones Eliminados

**❌ NO necesitamos:**
- `Aggregates/` (Event Sourcing overkill)
- `Events/` (Event Sourcing overkill) 
- `Projectors/` (Event Sourcing overkill)
- `Reactors/` (Event Sourcing overkill)
- `States/` (Spatie Model States - reemplazado por Enum methods)
- `StateTransitions/` (Spatie Model States - reemplazado por Enum methods)

**✅ SÍ mantenemos:**
- `Actions/` (orquestación útil)
- `Data/DTOs` (tipado fuerte útil)
- `Services/` (lógica compleja cross-domain)
- `Enums/` con métodos (business rules + UI logic)

---

## 5. Flujo Operacional Simplificado

### 5.1 Requote de ProductItem

```mermaid
sequenceDiagram
    participant User as Analista Adquisiciones
    participant UI as Filament UI
    participant Action as RequoteProductItemAction
    participant Version as ProductItemVersionService
    participant Model as ProductItem Model
    participant DB as Database

    User->>UI: "Re-quote con nuevas specs"
    UI->>Action: execute(RequoteProductItemData)
    Action->>Version: Capturar datos previos
    Action->>Model: update(specs, status)
    Model->>DB: UPDATE product_items
    DB-->>Model: OK
    Action->>Version: createVersion(previous, new, reason)
    Version->>DB: INSERT into product_item_versions
    DB-->>Version: OK
    Action-->>UI: return updated item
    UI-->>User: "Item re-cotizado exitosamente + versión creada"
    
    Note over Version: Audit trail simple para re-quotes
```

### 5.2 Visualización de Historial

```php
// Ejemplo de UI para mostrar versiones
public function getVersionHistoryAction(): Action
{
    return Action::make('version_history')
        ->label('Ver Historial')
        ->modalContent(fn (ProductItem $record) => view('admin.product-item-versions', [
            'versions' => app(ProductItemVersionService::class)->getVersionHistory($record)
        ]))
        ->modalWidth('6xl');
}

// Resultado esperado en UI:
/*
Version 3 (15/03/2024 15:30): Re-cotización
├── Por: Ana García (Procurement Analyst)
├── Cambios: Precio: $100 → $120, Estado: sourcing → quoted
└── Razón: "Cliente aprobó especificaciones premium"

Version 2 (10/03/2024 09:15): Actualización especificaciones  
├── Por: Juan Pérez (Sales Analyst)
├── Cambios: Color: rojo → azul, Material: plástico → cerámica
└── Razón: "Cambio solicitado por cliente"

Version 1 (01/03/2024 14:00): Creación inicial
├── Por: María López (Sales Analyst)
└── Estado: DRAFT
*/
```

---

## 6. Beneficios de la Arquitectura Simplificada

### 6.1 Ventajas Operacionales

**Development Speed**
- ⚡ 10x más rápido que Event Sourcing
- 🛠️ ~200 líneas de código vs ~2000 líneas  
- 📅 3-4 días de desarrollo vs 3-4 semanas

**Team Productivity**
- 👥 Cualquier dev Laravel puede trabajar inmediatamente
- 🔍 Debugging simple y directo
- 📚 Curva de aprendizaje mínima

**Performance**
- 🚀 Sin overhead de Event Sourcing
- 💾 Queries directos optimizados
- ⚡ Caching simple con Eloquent

### 6.2 Mantenibilidad

**Simplicidad**
- 🧩 Pocas moving parts
- 🔧 Troubleshooting straightforward
- 📊 Menos superficie para bugs

**Flexibility**
- 🎛️ Fácil ajustar qué se considera "significativo" para version tracking
- 📈 Path de upgrade claro si se necesitan capacidades avanzadas
- 🔄 Compatible con herramientas estándar de Laravel

### 6.3 Satisface los Requisitos

**✅ Lista de versiones**: Exactamente lo que se necesita
**✅ Cambios por re-cotizaciones**: Tracked automáticamente  
**✅ Audit trail**: Para cambios significativos
**✅ Quién cambió qué y cuándo**: Información completa

**❌ NO ofrece**:
- Reconstrucción histórica completa (no necesario)
- Business rules enforcement automático (validación manual en Actions)
- Side effects automáticos (notificaciones manuales)

---

## 7. Plan de Implementación

### Fase 1: Fundación (1-2 días)
1. ✅ Crear migration para `product_item_versions`
2. ✅ Crear `ProductItemVersion` model  
3. ✅ Crear `ProductItemVersionService`

### Fase 2: Actions con Version Tracking (2-3 días)
1. ✅ Actualizar Actions existentes con version tracking
2. ✅ Crear `RequoteProductItemAction` específico
3. ✅ Añadir UI components para mostrar versiones

### Fase 3: Testing y Refinement (1 día)
1. ✅ Testing de versioning
2. ✅ Refinements en UI
3. ✅ Documentation

**Total: ~1 semana** de implementación para satisfacer completamente el requisito de negocio.

---

## 8. Conclusión

Esta arquitectura simplificada proporciona **exactamente lo que el negocio necesita** sin la complejidad innecesaria de Event Sourcing para un requisito que no lo justifica.

**Resultado**: Lista de versiones de productos con tracking de cambios significativos, implementación rápida, mantenimiento simple, y team productivity optimizada.

**Recomendación**: Implementar esta versión simple primero. Si en el futuro se necesitan capacidades más avanzadas, siempre se puede evolucionar incrementalmente.