# Plan de Migración de Datos - PromoSmart

> **Propósito:** Define la estrategia completa para migrar datos existentes desde hojas de cálculo Excel hacia el sistema PromoSmart con arquitectura CRUD + Version Tracking.

---

## 1. Resumen Ejecutivo

### 1.1 Alcance de la Migración

**Fuentes de Datos Actuales:**
- Hojas de cálculo Excel distribuidas por analistas
- Archivos PDF de cotizaciones y documentos
- Datos en emails y sistemas de archivos locales
- Información dispersa en documentos Word

**Destino de Migración:**
- Base de datos PostgreSQL centralizada
- Sistema de archivos estructurado
- Histórico de versiones retroactivo

**Estimación de Volumen:**
- **Proyectos históricos:** ~500-1000 proyectos
- **ProductItems:** ~5000-15000 items de producto
- **Documentos:** ~2000-5000 archivos (PDFs, imágenes)
- **Datos financieros:** ~1000-3000 registros de costos

### 1.2 Objetivos Críticos

1. **Preservación de Datos:** 0% pérdida de información crítica
2. **Integridad Histórica:** Mantener relaciones y cronología
3. **Validación Completa:** 100% de datos validados pre-migración
4. **Rollback Capability:** Plan de reversión completo
5. **Downtime Mínimo:** < 4 horas de interrupción operacional

---

## 2. Análisis de Fuentes de Datos

### 2.1 Inventario de Archivos Excel

```mermaid
graph TD
    A[Excel Files Inventory] --> B[Projects Master File]
    A --> C[Product Items Catalog]
    A --> D[Supplier Database]
    A --> E[Cost Tracking Sheets]
    A --> F[Customer Information]
    
    B --> G[Data Profiling Analysis]
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> H[Migration Mapping Rules]
    G --> I[Data Quality Assessment]
    G --> J[Transformation Requirements]
```

### 2.2 Estructura de Datos Identificada

**Projects Master File (estimado)**
```
Columns Expected:
- project_name, customer_name, creation_date, status
- total_value, currency, responsible_analyst
- notes, last_modified, completion_date
```

**Product Items Catalog**
```
Columns Expected:
- project_reference, item_name, specifications
- supplier_name, quoted_price, status
- creation_date, last_update, version_notes
```

**Cost Tracking Sheets**
```
Columns Expected:  
- project_ref, item_ref, cost_category
- amount, currency, incurred_date
- supplier_invoice, notes
```

### 2.3 Retos de Calidad Identificados

| Riesgo | Probabilidad | Impacto | Estrategia de Mitigación |
|--------|-------------|---------|-------------------------|
| **Inconsistencia en nombres** | Alta (85%) | Alto | Diccionario de normalización + limpieza automática |
| **Formatos de fecha variables** | Alta (90%) | Medio | Parser inteligente con múltiples formatos |
| **Monedas mezcladas** | Media (60%) | Alto | Conversión automática con tasas históricas |
| **Datos duplicados** | Media (50%) | Medio | Algoritmos de deduplicación |
| **Relaciones rotas** | Alta (70%) | Alto | Reconstrucción de relaciones por heurísticas |

---

## 3. Estrategia de Migración ETL

### 3.1 Arquitectura del Pipeline ETL

```mermaid
flowchart TD
    subgraph "Extract Phase"
        A[Excel Files] --> B[File Parser Service]
        C[PDF Documents] --> D[Document OCR Service]
        E[Email Archives] --> F[Email Parser Service]
    end
    
    subgraph "Transform Phase"
        B --> G[Data Normalizer]
        D --> G
        F --> G
        G --> H[Validation Engine]
        H --> I[Relationship Builder]
        I --> J[Version Reconstructor]
    end
    
    subgraph "Load Phase"
        J --> K[Staging Database]
        K --> L[Data Quality Checker]
        L --> M{Quality Pass?}
        M -->|Yes| N[Production Load]
        M -->|No| O[Error Handler]
        O --> G
    end
    
    subgraph "Verification Phase"
        N --> P[Post-Migration Validator]
        P --> Q[User Acceptance Testing]
        Q --> R[Go-Live Decision]
    end
```

### 3.2 Fases de Implementación

#### Fase 1: Discovery y Preparación (2-3 semanas)
```bash
# Scripts de análisis automatizado
php artisan migrate:discover-excel-files --path=/data/migration/
php artisan migrate:analyze-structures --output=analysis.json
php artisan migrate:profile-quality --threshold=95%
```

**Entregables Fase 1:**
- Inventario completo de archivos fuente
- Análisis de calidad de datos
- Mapping definitivo source-to-target
- Plan detallado de transformaciones

#### Fase 2: Pipeline ETL Development (3-4 semanas)  
```bash
# Development y testing de pipeline
php artisan migrate:build-pipeline --config=migration_config.json
php artisan migrate:test-pipeline --sample-data=10%
php artisan migrate:validate-pipeline --full-check
```

**Entregables Fase 2:**
- Pipeline ETL completamente funcional
- Batería de tests automatizados
- Scripts de rollback y recovery
- Documentación técnica completa

#### Fase 3: Migración Piloto (1 semana)
```bash
# Migración de datos de prueba
php artisan migrate:execute --mode=pilot --projects=50
php artisan migrate:validate --mode=deep-check
php artisan migrate:user-test --duration=3-days
```

**Entregables Fase 3:**
- 50-100 proyectos migrados exitosamente
- Validación de integridad completada
- User acceptance testing aprobado
- Ajustes finales implementados

#### Fase 4: Migración Productiva (1 semana)
```bash
# Migración completa con monitoreo
php artisan migrate:execute --mode=production --full-dataset
php artisan migrate:monitor --real-time
php artisan migrate:verify --complete-check
```

---

## 4. Implementación Técnica del Pipeline

### 4.1 Servicio de Descubrimiento de Archivos

```php
// app/Services/Migration/FileDiscoveryService.php
class FileDiscoveryService
{
    public function discoverExcelFiles(string $basePath): Collection
    {
        return collect(glob($basePath . '/**/*.{xlsx,xls}', GLOB_BRACE))
            ->map(function ($filePath) {
                return [
                    'path' => $filePath,
                    'name' => basename($filePath),
                    'size' => filesize($filePath),
                    'modified' => filemtime($filePath),
                    'structure' => $this->analyzeStructure($filePath),
                    'quality_score' => $this->assessQuality($filePath),
                ];
            })
            ->sortByDesc('quality_score');
    }

    private function analyzeStructure(string $filePath): array
    {
        $reader = IOFactory::createReader('Xlsx');
        $spreadsheet = $reader->load($filePath);
        
        $analysis = [];
        foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
            $analysis[] = [
                'sheet_name' => $worksheet->getTitle(),
                'row_count' => $worksheet->getHighestRow(),
                'column_count' => Coordinate::columnIndexFromString($worksheet->getHighestColumn()),
                'has_headers' => $this->detectHeaders($worksheet),
                'data_types' => $this->analyzeDataTypes($worksheet),
            ];
        }
        
        return $analysis;
    }

    private function assessQuality(string $filePath): int
    {
        // Algoritmo de scoring de calidad (0-100)
        $score = 100;
        
        // Penalizar archivos muy antiguos
        $daysOld = (time() - filemtime($filePath)) / (60 * 60 * 24);
        if ($daysOld > 365) $score -= 20;
        
        // Penalizar archivos muy pequeños o muy grandes
        $sizeKb = filesize($filePath) / 1024;
        if ($sizeKb < 10 || $sizeKb > 10000) $score -= 15;
        
        // Bonus para archivos con nombres descriptivos
        if (preg_match('/project|item|cost|supplier/i', basename($filePath))) {
            $score += 10;
        }
        
        return max(0, min(100, $score));
    }
}
```

### 4.2 Motor de Transformación de Datos

```php
// app/Services/Migration/DataTransformationEngine.php
class DataTransformationEngine
{
    private array $transformationRules;
    private DataNormalizationService $normalizer;
    
    public function __construct(
        DataNormalizationService $normalizer,
        array $transformationRules = []
    ) {
        $this->normalizer = $normalizer;
        $this->transformationRules = $transformationRules ?: $this->getDefaultRules();
    }
    
    public function transformProjectData(array $rawData): array
    {
        $transformed = [];
        
        // Normalizar nombres de proyecto
        $transformed['name'] = $this->normalizer->normalizeProjectName($rawData['project_name'] ?? '');
        
        // Convertir fechas con múltiples formatos
        $transformed['created_at'] = $this->normalizer->parseDate($rawData['creation_date'] ?? null);
        
        // Normalizar estados
        $transformed['status'] = $this->normalizer->mapStatus($rawData['status'] ?? 'DRAFT');
        
        // Buscar y linkear customer
        $transformed['customer_id'] = $this->findOrCreateCustomer($rawData['customer_name'] ?? null);
        
        return $transformed;
    }
    
    public function transformProductItemData(array $rawData): array
    {
        $transformed = [];
        
        // Especificaciones desde múltiples campos
        $transformed['specifications'] = $this->consolidateSpecifications([
            'description' => $rawData['description'] ?? '',
            'material' => $rawData['material'] ?? '',
            'dimensions' => $rawData['dimensions'] ?? '',
            'color' => $rawData['color'] ?? '',
        ]);
        
        // Status mapping con historical logic
        $transformed['status'] = $this->inferHistoricalStatus($rawData);
        
        // Price normalization
        $transformed['quoted_price_usd'] = $this->normalizer->convertToUSD(
            amount: $rawData['price'] ?? 0,
            currency: $rawData['currency'] ?? 'USD',
            date: $rawData['quote_date'] ?? now()
        );
        
        return $transformed;
    }
    
    private function inferHistoricalStatus(array $rawData): ProductItemStatus
    {
        // Lógica para inferir estado basado en datos disponibles
        if (!empty($rawData['delivery_date'])) {
            return ProductItemStatus::DELIVERED;
        }
        
        if (!empty($rawData['production_date'])) {
            return ProductItemStatus::IN_PRODUCTION;
        }
        
        if (!empty($rawData['po_number'])) {
            return ProductItemStatus::READY_FOR_PRODUCTION;
        }
        
        if (!empty($rawData['quote_date'])) {
            return ProductItemStatus::QUOTED_TO_CUSTOMER;
        }
        
        return ProductItemStatus::DRAFT;
    }
}
```

### 4.3 Reconstrucción de Versiones Históricas

```php
// app/Services/Migration/VersionReconstructorService.php
class VersionReconstructorService
{
    private ProductItemVersionService $versionService;
    
    public function reconstructVersionHistory(ProductItem $item, array $historicalData): void
    {
        // Ordenar datos históricos por fecha
        $sortedHistory = collect($historicalData)
            ->sortBy('date')
            ->values();
        
        $previousState = null;
        
        foreach ($sortedHistory as $index => $historyEntry) {
            $changeType = $this->determineChangeType($historyEntry, $previousState);
            
            $this->versionService->createVersion(
                item: $item,
                changeType: $changeType,
                previousData: $previousState ?: [],
                newData: $historyEntry,
                summary: $this->generateHistoricalSummary($historyEntry, $changeType)
            );
            
            $previousState = $historyEntry;
        }
    }
    
    private function determineChangeType(array $current, ?array $previous): string
    {
        if ($previous === null) {
            return 'creation';
        }
        
        // Detectar re-cotización por cambio de precio
        if (($current['price'] ?? 0) !== ($previous['price'] ?? 0)) {
            return 'requote';
        }
        
        // Detectar cambio de especificaciones
        if (($current['specifications'] ?? '') !== ($previous['specifications'] ?? '')) {
            return 'spec_update';
        }
        
        // Detectar cambio de estado
        if (($current['status'] ?? '') !== ($previous['status'] ?? '')) {
            return 'status_change';
        }
        
        return 'data_update';
    }
}
```

---

## 5. Validación y Control de Calidad

### 5.1 Framework de Validación Multi-Nivel

```php
// app/Services/Migration/ValidationFramework.php
class ValidationFramework
{
    private array $validators;
    
    public function __construct()
    {
        $this->validators = [
            'structural' => new StructuralValidator(),
            'business' => new BusinessRulesValidator(),
            'referential' => new ReferentialIntegrityValidator(),
            'historical' => new HistoricalConsistencyValidator(),
        ];
    }
    
    public function validateMigrationBatch(Collection $migratedData): ValidationReport
    {
        $report = new ValidationReport();
        
        foreach ($this->validators as $type => $validator) {
            $result = $validator->validate($migratedData);
            $report->addValidationResult($type, $result);
        }
        
        return $report;
    }
}

// app/Services/Migration/Validators/BusinessRulesValidator.php
class BusinessRulesValidator implements ValidatorInterface
{
    public function validate(Collection $data): ValidationResult
    {
        $errors = collect();
        $warnings = collect();
        
        foreach ($data as $record) {
            // Validar reglas de negocio críticas
            if ($this->hasInvalidProjectStatus($record)) {
                $errors->push("Invalid project status transition: {$record['id']}");
            }
            
            if ($this->hasPriceInconsistencies($record)) {
                $warnings->push("Price inconsistency detected: {$record['id']}");
            }
            
            if ($this->hasDateAnomalies($record)) {
                $errors->push("Date chronology violation: {$record['id']}");
            }
        }
        
        return new ValidationResult(
            passed: $errors->isEmpty(),
            errors: $errors->toArray(),
            warnings: $warnings->toArray()
        );
    }
}
```

### 5.2 Métricas de Validación Críticas

| Métrica | Threshold Aceptable | Acción si se Supera |
|---------|-------------------|-------------------|
| **Errores Estructurales** | < 1% | Fix automático o escalación |
| **Inconsistencias de Precio** | < 5% | Warning + revisión manual |
| **Relaciones Rotas** | < 2% | Reconstrucción automática |
| **Fechas Inválidas** | < 3% | Inferencia o default values |
| **Datos Duplicados** | < 1% | Deduplicación automática |

---

## 6. Plan de Rollback y Contingencia

### 6.1 Estrategia de Backup Pre-Migración

```bash
# Backup completo antes de iniciar migración
pg_dump promosmart_production > backup_pre_migration_$(date +%Y%m%d).sql

# Backup de archivos existentes
tar -czf excel_files_backup_$(date +%Y%m%d).tar.gz /data/excel_files/

# Snapshot de configuración actual
php artisan config:backup --timestamp
```

### 6.2 Procedimientos de Rollback

```php
// app/Console/Commands/MigrationRollbackCommand.php
class MigrationRollbackCommand extends Command
{
    protected $signature = 'migrate:rollback 
                          {--batch-id= : Specific batch to rollback}
                          {--full : Full rollback to pre-migration state}
                          {--verify : Verify rollback completion}';
    
    public function handle(): int
    {
        if ($this->option('full')) {
            return $this->performFullRollback();
        }
        
        $batchId = $this->option('batch-id');
        return $this->performBatchRollback($batchId);
    }
    
    private function performFullRollback(): int
    {
        $this->info('Initiating full migration rollback...');
        
        // 1. Stop all application services
        $this->callSilent('down');
        
        // 2. Restore database from backup
        $this->info('Restoring database from backup...');
        $backupFile = $this->findLatestBackup();
        $this->executeCommand("psql promosmart_production < {$backupFile}");
        
        // 3. Restore file system
        $this->info('Restoring file system...');
        $this->executeCommand('tar -xzf excel_files_backup_*.tar.gz');
        
        // 4. Verify rollback completion
        if ($this->option('verify')) {
            $this->verifyRollbackIntegrity();
        }
        
        // 5. Bring application back up
        $this->callSilent('up');
        
        $this->info('Full rollback completed successfully.');
        return Command::SUCCESS;
    }
}
```

---

## 7. Monitoreo y Reporting

### 7.1 Dashboard de Migración en Tiempo Real

```php
// app/Services/Migration/MigrationMonitoringService.php
class MigrationMonitoringService
{
    public function getMigrationProgress(): array
    {
        return [
            'overall_progress' => $this->calculateOverallProgress(),
            'current_phase' => $this->getCurrentPhase(),
            'records_processed' => $this->getRecordsProcessed(),
            'processing_rate' => $this->getProcessingRate(),
            'errors_encountered' => $this->getErrorCount(),
            'estimated_completion' => $this->estimateCompletion(),
            'quality_metrics' => $this->getQualityMetrics(),
        ];
    }
    
    public function generateExecutiveReport(): string
    {
        $progress = $this->getMigrationProgress();
        
        return "
        MIGRATION STATUS REPORT - " . now()->format('Y-m-d H:i:s') . "
        
        Overall Progress: {$progress['overall_progress']}%
        Current Phase: {$progress['current_phase']}
        Records Processed: {$progress['records_processed']}
        Processing Rate: {$progress['processing_rate']} records/hour
        
        Quality Metrics:
        - Data Integrity: {$progress['quality_metrics']['integrity']}%
        - Relationship Accuracy: {$progress['quality_metrics']['relationships']}%
        - Version Reconstruction: {$progress['quality_metrics']['versions']}%
        
        Estimated Completion: {$progress['estimated_completion']}
        ";
    }
}
```

### 7.2 Alertas y Notificaciones

```php
// Configuración de alertas críticas
$alertRules = [
    'error_rate_high' => [
        'condition' => 'error_rate > 5%',
        'action' => 'pause_migration',
        'notify' => ['tech-lead', 'project-manager'],
    ],
    'processing_stalled' => [
        'condition' => 'no_progress_for > 30 minutes',
        'action' => 'investigation_required',
        'notify' => ['tech-lead', 'database-admin'],
    ],
    'quality_degradation' => [
        'condition' => 'quality_score < 90%',
        'action' => 'escalate_review',
        'notify' => ['business-analyst', 'project-manager'],
    ],
];
```

---

## 8. Testing y User Acceptance

### 8.1 Plan de Testing Estructurado

```mermaid
graph TD
    A[Unit Tests - Components] --> B[Integration Tests - Pipeline]
    B --> C[Data Quality Tests]
    C --> D[User Acceptance Testing]
    D --> E[Performance Testing]
    E --> F[Go-Live Decision]
    
    subgraph "UAT Scenarios"
        G[Historical Data Accuracy]
        H[Version History Completeness] 
        I[Business Rule Compliance]
        J[UI/UX Validation]
    end
    
    D --> G
    D --> H
    D --> I
    D --> J
```

### 8.2 Criterios de Aceptación

| Criterio | Definición | Método de Verificación | Responsable |
|----------|-----------|----------------------|-------------|
| **Integridad de Datos** | 0% pérdida de datos críticos | Comparación automatizada pre/post | Technical Lead |
| **Precisión Histórica** | 95% de fechas y eventos correctos | Verificación manual de muestra | Business Analyst |
| **Usabilidad** | Sistema navegable sin entrenamiento adicional | User testing sessions | End Users |
| **Performance** | Tiempos de respuesta < 3 segundos | Load testing automatizado | DevOps Engineer |

---

## 9. Cronograma de Implementación

### 9.1 Timeline Detallado

```mermaid
gantt
    title Cronograma de Migración PromoSmart
    dateFormat  YYYY-MM-DD
    section Preparación
    Análisis de Fuentes        :prep1, 2024-02-01, 2w
    Pipeline Development       :prep2, after prep1, 3w
    Testing Pipeline          :prep3, after prep2, 1w
    
    section Piloto
    Migración Piloto          :pilot1, after prep3, 3d
    Validación y Ajustes      :pilot2, after pilot1, 4d
    
    section Producción
    Migración Full            :prod1, after pilot2, 2d
    Validación Post-Migración :prod2, after prod1, 1d
    Go-Live                   :prod3, after prod2, 1d
    
    section Estabilización
    Monitoreo Intensivo       :stab1, after prod3, 1w
    Optimizaciones            :stab2, after stab1, 1w
```

### 9.2 Recursos Requeridos

**Team Core:**
- **Technical Lead:** 100% dedicación (8 semanas)
- **Senior Developer:** 100% dedicación (6 semanas)
- **Database Administrator:** 50% dedicación (8 semanas)
- **Business Analyst:** 75% dedicación (4 semanas)
- **QA Tester:** 100% dedicación (3 semanas)

**Infrastructure:**
- Servidor staging dedicado
- Backup storage adicional (1TB)
- Monitoring tools temporales
- Testing environment isolado

---

## 10. Riesgos y Mitigaciones

### 10.1 Matriz de Riesgos Críticos

| Riesgo | Probabilidad | Impacto | Mitigación | Plan B |
|--------|-------------|---------|------------|---------|
| **Pérdida de datos** | Baja (15%) | Crítico | Triple backup + validación | Rollback inmediato |
| **Downtime extendido** | Media (40%) | Alto | Migración en paralelo | Extensión de ventana |
| **Calidad insuficiente** | Alta (60%) | Alto | Validación multi-nivel | Re-trabajo selectivo |
| **Resistencia de usuarios** | Media (45%) | Medio | Training + comunicación | Soporte intensivo |
| **Performance degradada** | Media (35%) | Alto | Load testing previo | Optimización post go-live |

### 10.2 Plan de Comunicación

**Stakeholders Clave:**
- **Executive Team:** Weekly status reports
- **End Users:** Training sessions + change management
- **IT Team:** Daily standups durante migración
- **External Partners:** Impact notifications

---

## 11. Medidas de Éxito

### 11.1 KPIs de Migración

| Métrica | Target | Medición |
|---------|--------|----------|
| **Data Integrity** | 100% | Automated validation |
| **User Adoption** | 90% in 2 weeks | Usage analytics |
| **System Performance** | < 3s response | APM monitoring |
| **Error Rate** | < 1% | Application logs |
| **Training Completion** | 95% | LMS tracking |

### 11.2 Post-Migration Assessment

- **Week 1:** Stability monitoring + hot fixes
- **Week 2:** User feedback collection + prioritization
- **Week 4:** Performance optimization + tuning
- **Month 3:** Full migration success assessment

Este plan de migración proporciona una estrategia integral y detallada para migrar exitosamente desde hojas de cálculo Excel hacia el sistema PromoSmart, manteniendo la integridad de datos y minimizando el impacto operacional.