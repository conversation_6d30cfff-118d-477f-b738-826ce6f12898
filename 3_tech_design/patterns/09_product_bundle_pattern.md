# 9. Patrón de Agrupación de Productos (Bundles/Kits)

> **Propósito:** Definir un modelo para agrupar múltiples `ProductItem` distintos que se venden como un único paquete comercial (bundle o kit), manteniendo la independencia operativa de cada componente.

---

## 1. Contexto y Problema

Un "bundle" representa un conjunto de productos diferentes que se venden a un precio de paquete. <PERSON>r eje<PERSON>lo, un "Kit de Terraza" que incluye 4 sillas, 1 mesa y 1 toldo. Cada uno de estos es un `ProductItem` independiente con su propio costo, proveedor y ciclo de vida.

Este escenario es fundamentalmente diferente al de las "variantes de diseño", que son variaciones cosméticas de un mismo producto base. El desafío es poder vender el "kit" como una unidad comercial, pero gestionar la compra y producción de sus componentes de forma individual.

## 2. Análisis de Alternativas

### Alternativa 1: Agrupación Informal por Proyecto (No Recomendado)

*   **Descripción:** Se crea un `Project` y dentro de él se añaden los `ProductItem` de las sillas, la mesa y el toldo. La agrupación es puramente conceptual, basada en el nombre del proyecto.
*   **Contras:** No permite asignar un precio único al paquete, dificulta el reporting sobre la venta de bundles y es una estructura frágil.

### Alternativa 2: El "Bundle" como una Entidad Agrupadora (Recomendado)

*   **Descripción:** Se introduce una nueva entidad, `ProductBundle`, que agrupa a los `ProductItem`.
*   **Modelo de Datos:**
    *   `Project` tiene uno o más `ProductBundle`.
    *   `ProductBundle` tiene un nombre, descripción y un **precio de venta total** para el paquete.
    *   `ProductBundle` agrupa a uno o más `ProductItem`.
    *   Cada `ProductItem` (silla, mesa, toldo) mantiene su propio costo, proveedor y ciclo de vida operativo.
*   **Ventajas:** Es el modelo más limpio y preciso. Separa la "unidad de venta" (el bundle) de las "unidades operativas" (los ítems), permitiendo una gestión clara tanto para el equipo de ventas como para el de operaciones.

### Alternativa 3: El "Bundle" como un Tipo Especial de `ProductItem`

*   **Descripción:** Se añade una columna `type` al modelo `ProductItem` (`SIMPLE` o `BUNDLE`). Un `ProductItem` tipo `BUNDLE` se relaciona con otros `ProductItem` tipo `SIMPLE`.
*   **Contras:** Este enfoque sobrecarga el modelo `ProductItem` con demasiada responsabilidad y complejidad, haciendo las consultas y la lógica de negocio difíciles de mantener. Es considerado un anti-patrón.

## 3. Solución Recomendada

Se adopta la **Alternativa 2**. El sistema implementará una entidad `ProductBundle` para representar los paquetes comerciales.

### Flujo de Trabajo

1.  **Ventas:** El equipo de ventas trabaja con la entidad `ProductBundle`. Crean el kit, le asignan un precio de paquete y lo presentan al cliente como una sola línea en la cotización.

2.  **Operaciones:** El equipo de adquisiciones e importaciones trabaja con las entidades `ProductItem`. Para ellos, el bundle es simplemente una lista de ítems individuales que deben ser cotizados, producidos y rastreados de forma independiente.

3.  **Finanzas:** El sistema calcula la rentabilidad comparando el precio de venta del `ProductBundle` contra la suma de los costos reales de todos los `ProductItem` que lo componen.

### Diagrama de Relación

```mermaid
erDiagram
    PROJECT ||--|{ PRODUCT_BUNDLE : contiene
    PRODUCT_BUNDLE ||--|{ PRODUCT_ITEM : agrupa
```

## 4. Conclusión

Este patrón permite una gestión de bundles robusta y escalable. Proporciona claridad en la fijación de precios y en el reporting comercial, sin interferir con la autonomía y el detalle requerido para la gestión operativa de cada producto individual dentro del paquete.
