# Patrón Soft Deletes

## Descripción

Este documento describe la implementación del patrón de Soft Deletes en el sistema PromoSmart, que permite eliminar registros de forma lógica sin eliminarlos físicamente de la base de datos.

## Implementación

### 1. Migración

Para agregar soft deletes a una tabla existente:

```php
Schema::table('table_name', function (Blueprint $table) {
    $table->softDeletes();
});
```

### 2. Modelo

Agregar el trait `SoftDeletes` al modelo:

```php
use Illuminate\Database\Eloquent\SoftDeletes;

class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes;
    
    // ... resto del código
}
```

### 3. Acciones

#### DeleteAction (Soft Delete)
```php
class DeleteUserAction
{
    public function execute(User $user): bool
    {
        // Validaciones de negocio
        if ($user->id === auth()->id()) {
            throw ValidationException::withMessages([
                'user' => 'No puedes eliminar tu propia cuenta.'
            ]);
        }

        // Realizar soft delete
        return $user->delete();
    }
}
```

#### ForceDeleteAction (Eliminación Permanente)
```php
class ForceDeleteUserAction
{
    public function execute(User $user): bool
    {
        // Verificar que el usuario esté soft deleted
        if (!$user->trashed()) {
            throw ValidationException::withMessages([
                'user' => 'El usuario debe estar eliminado antes de poder eliminarlo permanentemente.'
            ]);
        }

        // Realizar force delete
        return $user->forceDelete();
    }
}
```

#### RestoreAction (Restaurar)
```php
class RestoreUserAction
{
    public function execute(User $user): bool
    {
        // Verificar que el usuario esté soft deleted
        if (!$user->trashed()) {
            throw new \InvalidArgumentException('El usuario no está eliminado.');
        }

        // Restaurar el usuario
        return $user->restore();
    }
}
```

## Métodos Disponibles

### En el Modelo

- `$user->delete()` - Soft delete
- `$user->forceDelete()` - Eliminación permanente
- `$user->restore()` - Restaurar
- `$user->trashed()` - Verificar si está eliminado
- `$user->isDeleted()` - Alias para trashed()

### Scopes de Consulta

- `User::withTrashed()` - Incluir usuarios eliminados
- `User::onlyTrashed()` - Solo usuarios eliminados
- `User::withoutTrashed()` - Excluir usuarios eliminados (por defecto)

### Consultas de Ejemplo

```php
// Obtener todos los usuarios (activos)
$activeUsers = User::all();

// Obtener todos los usuarios incluyendo eliminados
$allUsers = User::withTrashed()->get();

// Obtener solo usuarios eliminados
$deletedUsers = User::onlyTrashed()->get();

// Verificar si un usuario está eliminado
if ($user->trashed()) {
    // Usuario está soft deleted
}

// Restaurar un usuario eliminado
if ($user->trashed()) {
    $user->restore();
}
```

## Validaciones de Negocio

### Reglas Implementadas

1. **No auto-eliminación**: Un usuario no puede eliminarse a sí mismo
2. **Protección de administradores**: No se puede eliminar el último administrador
3. **Force delete solo para eliminados**: Solo se puede hacer force delete de registros ya soft deleted
4. **Restauración solo para eliminados**: Solo se pueden restaurar registros soft deleted

### Ejemplo de Validaciones

```php
// Verificar que no se elimine el último administrador
if ($user->isAdmin() && User::admins()->count() <= 1) {
    throw ValidationException::withMessages([
        'user' => 'No se puede eliminar el último administrador del sistema.'
    ]);
}

// Verificar que el usuario esté soft deleted antes de force delete
if (!$user->trashed()) {
    throw ValidationException::withMessages([
        'user' => 'El usuario debe estar eliminado antes de poder eliminarlo permanentemente.'
    ]);
}
```

## Eventos Disponibles

Laravel proporciona eventos automáticos para soft deletes:

- `deleting` - Antes del soft delete
- `deleted` - Después del soft delete
- `restoring` - Antes de restaurar
- `restored` - Después de restaurar
- `forceDeleting` - Antes del force delete
- `forceDeleted` - Después del force delete

### Ejemplo de Eventos

```php
class User extends Authenticatable
{
    use SoftDeletes;

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($user) {
            // Lógica después del soft delete
            Log::info("Usuario {$user->email} fue soft deleted");
        });

        static::restored(function ($user) {
            // Lógica después de restaurar
            Log::info("Usuario {$user->email} fue restaurado");
        });
    }
}
```

## Consideraciones de Rendimiento

1. **Índices**: Agregar índice a la columna `deleted_at` para mejorar el rendimiento de las consultas
2. **Consultas**: Usar `withTrashed()` solo cuando sea necesario
3. **Limpieza**: Considerar implementar un job para limpiar registros soft deleted antiguos

### Índice Recomendado

```php
Schema::table('users', function (Blueprint $table) {
    $table->softDeletes();
    $table->index('deleted_at');
});
```

## Testing

### Tests Unitarios

```php
class UserSoftDeletesTest extends TestCase
{
    public function test_user_can_be_soft_deleted()
    {
        $user = User::factory()->create();
        
        $action = new DeleteUserAction();
        $result = $action->execute($user);
        
        $this->assertTrue($result);
        $this->assertTrue($user->trashed());
    }

    public function test_soft_deleted_user_can_be_restored()
    {
        $user = User::factory()->create();
        $user->delete();
        
        $action = new RestoreUserAction();
        $result = $action->execute($user);
        
        $this->assertTrue($result);
        $this->assertFalse($user->trashed());
    }
}
```

## Aplicación a Otros Modelos

Para aplicar soft deletes a otros modelos del sistema:

1. Crear migración para agregar `deleted_at`
2. Agregar trait `SoftDeletes` al modelo
3. Crear acciones específicas (Delete, ForceDelete, Restore)
4. Implementar validaciones de negocio específicas
5. Crear tests unitarios

### Ejemplo para Supplier

```php
// Migración
Schema::table('suppliers', function (Blueprint $table) {
    $table->softDeletes();
});

// Modelo
class Supplier extends Model
{
    use SoftDeletes;
    // ... resto del código
}

// Acciones
class DeleteSupplierAction
{
    public function execute(Supplier $supplier): bool
    {
        // Validaciones específicas de Supplier
        if ($supplier->hasActiveProjects()) {
            throw ValidationException::withMessages([
                'supplier' => 'No se puede eliminar un proveedor con proyectos activos.'
            ]);
        }
        
        return $supplier->delete();
    }
}
```

## Conclusión

El patrón de Soft Deletes proporciona una capa adicional de seguridad y auditoría al sistema, permitiendo recuperar datos eliminados y mantener la integridad referencial. Es especialmente importante para entidades de negocio críticas como usuarios, proveedores, clientes y proyectos.
