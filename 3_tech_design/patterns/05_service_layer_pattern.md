# 5. Service Layer Pattern

**Referencia ADR:** [ADR-006](../../2_architecture/adrs/adr006_crud_arquitectura_simplificada.md)

## Propósito

Encapsular lógica de negocio compleja que trasciende un solo modelo o requiere coordinación entre múltiples entidades. En la arquitectura simplificada, los servicios se usan específicamente para:

- **Version tracking** de ProductItems
- **Cálculos derivados** (project status, metrics)
- **Cross-domain logic** que no pertenece a un Action específico

## Estructura Simplificada

```php
abstract class BaseService
{
    protected function log(string $message, array $context = []): void
    {
        Log::info($message, array_merge($context, [
            'service' => static::class,
            'user_id' => auth()->id(),
        ]));
    }
}
```

## Ejemplo Principal: `ProductItemVersionService`

Este es el servicio core de la arquitectura simplificada para manejar version tracking.

```php
class ProductItemVersionService extends BaseService
{
    public function createVersion(
        ProductItem $item,
        string $changeType,
        array $previousData,
        array $newData,
        ?string $summary = null
    ): ProductItemVersion {
        return ProductItemVersion::create([
            'product_item_id' => $item->id,
            'version_number' => $this->getNextVersionNumber($item),
            'change_type' => $changeType,
            'changes_summary' => $summary ?? $this->generateSummary($changeType, $previousData, $newData),
            'previous_data' => $previousData,
            'new_data' => $newData,
            'changed_by' => auth()->id(),
            'changed_at' => now(),
        ]);
    }
    
    public function getVersionHistory(ProductItem $item): Collection
    {
        return ProductItemVersion::where('product_item_id', $item->id)
            ->with('changedBy:id,name')
            ->orderBy('version_number', 'desc')
            ->get()
            ->map(fn($version) => [
                'version' => $version->version_number,
                'type' => $version->change_type,
                'summary' => $version->changes_summary,
                'changed_by' => $version->changedBy->name,
                'changed_at' => $version->changed_at->format('d/m/Y H:i'),
                'previous_data' => $version->previous_data,
                'new_data' => $version->new_data,
            ]);
    }
    
    public function hasSignificantChanges(array $previous, array $new): bool
    {
        // Define qué constituye cambios "significativos"
        return $previous['specifications'] !== $new['specifications'] ||
               $previous['status'] !== $new['status'];
    }
    
    private function getNextVersionNumber(ProductItem $item): int
    {
        return ProductItemVersion::where('product_item_id', $item->id)
            ->max('version_number') + 1;
    }
    
    private function generateSummary(string $changeType, array $previous, array $new): string
    {
        return match($changeType) {
            'creation' => 'Creación inicial',
            'requote' => $this->generateRequoteSummary($previous, $new),
            'spec_update' => $this->generateSpecUpdateSummary($previous, $new),
            'status_change' => $this->generateStatusChangeSummary($previous, $new),
            default => 'Cambios realizados'
        };
    }
    
    private function generateRequoteSummary(array $previous, array $new): string
    {
        $changes = [];
        
        if (isset($previous['price'], $new['price']) && $previous['price'] !== $new['price']) {
            $changes[] = "Precio: {$previous['price']} → {$new['price']}";
        }
        
        if (isset($previous['status'], $new['status']) && $previous['status'] !== $new['status']) {
            $changes[] = "Estado: {$previous['status']} → {$new['status']}";
        }
        
        return empty($changes) ? 'Re-cotización procesada' : implode(', ', $changes);
    }
}
```

## Ejemplo: `ProjectStatusCalculatorService`

Para lógica de cálculo que trasciende un solo modelo.

```php
class ProjectStatusCalculatorService extends BaseService
{
    public function calculate(Project $project): ProjectStatus
    {
        $itemStatuses = $project->productItems()->pluck('status');
        
        // Lógica simple de derivación
        if ($itemStatuses->every(fn($status) => $status === ProductItemStatus::DELIVERED)) {
            return ProjectStatus::COMPLETED;
        }
        
        if ($itemStatuses->contains(ProductItemStatus::IN_PRODUCTION)) {
            return ProjectStatus::IN_PROGRESS;
        }
        
        if ($itemStatuses->every(fn($status) => $status === ProductItemStatus::DRAFT)) {
            return ProjectStatus::DRAFT;
        }
        
        return ProjectStatus::ACTIVE;
    }
    
    public function getStatusBreakdown(Project $project): array
    {
        $breakdown = $project->productItems()
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
        
        $this->log('Project status breakdown calculated', [
            'project_id' => $project->id,
            'breakdown' => $breakdown,
        ]);
        
        return $breakdown;
    }
}
```

## Ejemplo: `ReportGenerationService`

Para operaciones que requieren datos de múltiples dominios.

```php
class ReportGenerationService extends BaseService
{
    public function __construct(
        private ProductItemVersionService $versionService
    ) {}
    
    public function generateProjectSummary(Project $project): array
    {
        $items = $project->productItems()->with('versions')->get();
        
        return [
            'project' => [
                'id' => $project->id,
                'name' => $project->name,
                'status' => $project->status->getLabel(),
                'created_at' => $project->created_at->format('d/m/Y'),
            ],
            'items_summary' => [
                'total' => $items->count(),
                'by_status' => $items->groupBy('status')->map->count(),
                'with_versions' => $items->filter->versions->count(),
            ],
            'recent_changes' => $this->getRecentProjectChanges($project),
            'generated_at' => now()->format('d/m/Y H:i:s'),
        ];
    }
    
    public function generateVersionReport(ProductItem $item): array
    {
        $versions = $this->versionService->getVersionHistory($item);
        
        return [
            'item' => [
                'id' => $item->id,
                'name' => $item->name,
                'current_status' => $item->status->getLabel(),
            ],
            'version_history' => $versions,
            'total_versions' => $versions->count(),
            'last_change' => $versions->first(),
            'generated_at' => now()->format('d/m/Y H:i:s'),
        ];
    }
    
    private function getRecentProjectChanges(Project $project): Collection
    {
        return ProductItemVersion::whereHas('productItem', function($query) use ($project) {
                $query->where('project_id', $project->id);
            })
            ->with(['productItem:id,name', 'changedBy:id,name'])
            ->orderBy('changed_at', 'desc')
            ->limit(10)
            ->get()
            ->map(fn($version) => [
                'item_name' => $version->productItem->name,
                'change_type' => $version->change_type,
                'summary' => $version->changes_summary,
                'changed_by' => $version->changedBy->name,
                'changed_at' => $version->changed_at->format('d/m/Y H:i'),
            ]);
    }
}
```

## Principios de los Servicios Simplificados

1. **Single Responsibility**: Cada servicio tiene una responsabilidad clara
2. **Stateless**: No mantienen estado, solo procesan datos
3. **Dependency Injection**: Inyectan otras dependencias via constructor
4. **Logging**: Registran operaciones importantes para auditoría
5. **Return structured data**: Devuelven arrays/collections estructuradas

## Cuándo Crear un Servicio

✅ **SÍ crear servicio cuando**:
- Lógica trasciende múltiples modelos
- Cálculos complejos (status derivado, métricas)
- Operaciones de reporting/analytics
- Version tracking y audit trails

❌ **NO crear servicio cuando**:
- Simple CRUD operations (usar Actions directos)
- Lógica que pertenece a un solo modelo
- Validaciones simples (usar DTOs)
- UI logic (usar Enum methods)

## Testing de Servicios

```php
class ProductItemVersionServiceTest extends TestCase
{
    use RefreshDatabase;
    
    /** @test */
    public function it_creates_version_for_requote()
    {
        $item = ProductItem::factory()->create();
        $service = app(ProductItemVersionService::class);
        
        $version = $service->createVersion(
            item: $item,
            changeType: 'requote',
            previousData: ['price' => 100],
            newData: ['price' => 120],
            summary: 'Price increased due to material costs'
        );
        
        $this->assertInstanceOf(ProductItemVersion::class, $version);
        $this->assertEquals('requote', $version->change_type);
        $this->assertEquals(['price' => 100], $version->previous_data);
        $this->assertEquals(['price' => 120], $version->new_data);
    }
    
    /** @test */
    public function it_generates_version_history()
    {
        $item = ProductItem::factory()->create();
        $service = app(ProductItemVersionService::class);
        
        // Create multiple versions
        $service->createVersion($item, 'creation', [], ['price' => 100]);
        $service->createVersion($item, 'requote', ['price' => 100], ['price' => 120]);
        
        $history = $service->getVersionHistory($item);
        
        $this->assertCount(2, $history);
        $this->assertEquals('requote', $history->first()['type']);
    }
}
```

## Beneficios de la Aproximación Simple

✅ **Claridad**: Cada servicio tiene propósito específico
✅ **Testabilidad**: Lógica aislada y testeable
✅ **Reutilización**: Services reutilizables entre Actions
✅ **Mantenibilidad**: Lógica compleja centralizada
✅ **Performance**: Sin overhead innecesario