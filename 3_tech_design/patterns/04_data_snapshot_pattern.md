# 4. Data Snapshot Pattern

**Referencia ADR:** [ADR-006](../../2_architecture/adrs/adr006_crud_arquitectura_simplificada.md)

## Propósito

Proporcionar snapshots simples de datos para documentos inmutables cuando sea necesario. En la arquitectura simplificada, este patrón se usa de forma **opcional** y **selectiva** para:

- **Generación de documentos** que necesiten ser inmutables
- **Version tracking** con snapshots de datos específicos
- **Reportes históricos** que requieran conservar estado en el tiempo

## Arquitectura Simplificada

### Servicio Básico de Snapshots

```php
class DataSnapshotService
{
    public function createSnapshot(string $type, mixed $sourceData): array
    {
        return [
            'type' => $type,
            'data' => $this->collectRelevantData($type, $sourceData),
            'created_at' => now()->toISOString(),
            'created_by' => auth()->id(),
            'hash' => $this->generateHash($sourceData),
        ];
    }
    
    private function collectRelevantData(string $type, mixed $sourceData): array
    {
        return match($type) {
            'product_item_version' => $this->collectProductItemData($sourceData),
            'customer_quotation' => $this->collectQuotationData($sourceData),
            'project_summary' => $this->collectProjectData($sourceData),
            default => ['raw_data' => $sourceData]
        };
    }
    
    private function collectProductItemData(ProductItem $item): array
    {
        return [
            'id' => $item->id,
            'name' => $item->name,
            'status' => $item->status->value,
            'specifications' => $item->specifications,
            'project' => [
                'id' => $item->project->id,
                'name' => $item->project->name,
                'customer' => $item->project->customer->name ?? null,
            ],
        ];
    }
    
    private function collectQuotationData(Project $project): array
    {
        return [
            'project' => [
                'id' => $project->id,
                'name' => $project->name,
                'description' => $project->description,
            ],
            'customer' => [
                'name' => $project->customer->name,
                'email' => $project->customer->email,
                'address' => $project->customer->address,
            ],
            'items' => $project->productItems->map(fn($item) => [
                'name' => $item->name,
                'specifications' => $item->specifications,
                'quantity' => $item->quantity ?? 1,
                'unit_price' => $item->unit_price ?? 0,
            ])->toArray(),
            'totals' => [
                'subtotal' => $project->productItems->sum('unit_price'),
                'tax' => $project->tax_amount ?? 0,
                'total' => $project->total_amount ?? 0,
            ],
        ];
    }
    
    private function generateHash(mixed $data): string
    {
        return hash('sha256', serialize($data));
    }
}
```

## Uso en ProductItemVersionService

Integración simple con el version tracking existente:

```php
class ProductItemVersionService extends BaseService
{
    public function __construct(
        private DataSnapshotService $snapshotService
    ) {}
    
    public function createVersion(
        ProductItem $item,
        string $changeType,
        array $previousData,
        array $newData,
        ?string $summary = null
    ): ProductItemVersion {
        
        // Crear snapshot completo del item (opcional)
        $snapshot = $this->snapshotService->createSnapshot('product_item_version', $item);
        
        return ProductItemVersion::create([
            'product_item_id' => $item->id,
            'version_number' => $this->getNextVersionNumber($item),
            'change_type' => $changeType,
            'changes_summary' => $summary ?? $this->generateSummary($changeType, $previousData, $newData),
            'previous_data' => $previousData,
            'new_data' => $newData,
            'snapshot_data' => $snapshot, // Snapshot completo opcional
            'changed_by' => auth()->id(),
            'changed_at' => now(),
        ]);
    }
}
```

## Ejemplo: Generador de Documentos

```php
class SimpleDocumentGenerator
{
    public function __construct(
        private DataSnapshotService $snapshotService
    ) {}
    
    public function generateCustomerQuotation(Project $project): array
    {
        // Crear snapshot inmutable de los datos
        $snapshot = $this->snapshotService->createSnapshot('customer_quotation', $project);
        
        // Generar documento usando snapshot
        $document = $this->buildQuotationDocument($snapshot['data']);
        
        // Guardar snapshot para futura regeneración
        QuotationSnapshot::create([
            'project_id' => $project->id,
            'snapshot_data' => $snapshot,
            'document_hash' => hash('sha256', $document),
            'generated_at' => now(),
        ]);
        
        return $document;
    }
    
    private function buildQuotationDocument(array $snapshotData): array
    {
        return [
            'header' => [
                'title' => 'Cotización',
                'date' => now()->format('d/m/Y'),
                'customer' => $snapshotData['customer']['name'],
            ],
            'project_info' => [
                'name' => $snapshotData['project']['name'],
                'description' => $snapshotData['project']['description'],
            ],
            'items' => $snapshotData['items'],
            'totals' => $snapshotData['totals'],
            'terms' => 'Términos y condiciones estándar...',
        ];
    }
    
    public function regenerateFromSnapshot(int $snapshotId): array
    {
        $snapshot = QuotationSnapshot::findOrFail($snapshotId);
        return $this->buildQuotationDocument($snapshot->snapshot_data['data']);
    }
}
```

## Tabla de Base de Datos para Snapshots

```sql
-- Opcional: tabla dedicada para snapshots de documentos
CREATE TABLE quotation_snapshots (
    id BIGINT PRIMARY KEY,
    project_id BIGINT NOT NULL,
    snapshot_data JSON NOT NULL,
    document_hash VARCHAR(64),
    generated_at TIMESTAMP,
    created_at TIMESTAMP,
    
    FOREIGN KEY (project_id) REFERENCES projects(id)
);

-- O usar la misma tabla product_item_versions con snapshot_data opcional
ALTER TABLE product_item_versions 
ADD COLUMN snapshot_data JSON NULL;
```

## Principios de la Implementación Simple

1. **Opcional por defecto**: Solo usar cuando sea necesario preservar estado inmutable
2. **Datos específicos**: Capturar solo lo relevante, no todo el estado de la aplicación
3. **Self-contained**: Snapshots autocontenidos para regeneración independiente
4. **Hash integrity**: Verificación de integridad básica
5. **JSON storage**: Almacenamiento simple en JSON para PostgreSQL

## Cuándo Usar Data Snapshots

✅ **SÍ usar cuando**:
- Generar documentos que deben ser inmutables (cotizaciones, POs)
- Version tracking que requiere contexto completo
- Reports históricos específicos
- Audit trails que necesitan reconstrucción exacta

❌ **NO usar cuando**:
- Simple CRUD operations
- Datos que cambian frecuentemente
- Performance es crítica
- Storage es limitado

## Testing

```php
class DataSnapshotServiceTest extends TestCase
{
    use RefreshDatabase;
    
    /** @test */
    public function it_creates_product_item_snapshot()
    {
        $item = ProductItem::factory()->create([
            'name' => 'Test Product',
            'specifications' => ['color' => 'red']
        ]);
        
        $service = app(DataSnapshotService::class);
        $snapshot = $service->createSnapshot('product_item_version', $item);
        
        $this->assertEquals('product_item_version', $snapshot['type']);
        $this->assertEquals('Test Product', $snapshot['data']['name']);
        $this->assertEquals(['color' => 'red'], $snapshot['data']['specifications']);
        $this->assertArrayHasKey('hash', $snapshot);
    }
    
    /** @test */
    public function it_creates_quotation_snapshot()
    {
        $project = Project::factory()->create(['name' => 'Test Project']);
        $project->productItems()->create([
            'name' => 'Item 1',
            'unit_price' => 100
        ]);
        
        $service = app(DataSnapshotService::class);
        $snapshot = $service->createSnapshot('customer_quotation', $project);
        
        $this->assertEquals('customer_quotation', $snapshot['type']);
        $this->assertEquals('Test Project', $snapshot['data']['project']['name']);
        $this->assertCount(1, $snapshot['data']['items']);
    }
}
```

## Beneficios de la Aproximación Simple

✅ **Selective usage**: Solo donde aporta valor real
✅ **Minimal overhead**: Sin infrastructure compleja
✅ **Self-contained**: Snapshots independientes y regenerables  
✅ **Flexible**: Adaptable a diferentes tipos de datos
✅ **Simple storage**: JSON en PostgreSQL, sin schemas complejos

Esta implementación simple del Data Snapshot Pattern proporciona **exactamente lo que se necesita** sin la complejidad innecesaria de un sistema de snapshots universal.