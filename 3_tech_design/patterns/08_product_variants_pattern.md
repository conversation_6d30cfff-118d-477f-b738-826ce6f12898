# 8. Patrón de Producto con Variantes de Diseño

> **Propósito:** Definir un modelo de datos y un flujo de trabajo unificado para gestionar productos que son técnicamente idénticos pero que tienen múltiples variaciones de diseño (branding), así como productos individuales, sin duplicar lógica ni datos.

---

## 1. Contexto y Problema

El modelo de negocio a menudo presenta escenarios donde un cliente solicita un producto base (ej. 500 banderas) pero con múltiples diseños diferentes aplicados sobre él (ej. 100 banderas por cada una de las 5 marcas del cliente). Las especificaciones técnicas, el costo unitario y el proceso de producción son idénticos para todas las banderas; la única variación es el archivo de arte (logo) que se imprime.

Tratar cada variación como un `ProductItem` separado es ineficiente, propenso a errores y no refleja la realidad de la negociación con proveedores, donde se busca un precio por el volumen total.

## 2. Principio de Diseño: Un Flujo de Trabajo Unificado

La solución no es crear dos procesos distintos (uno para productos "simples" y otro para productos "con variantes"). La solución es adoptar un modelo donde un producto simple es un caso especial de un producto con variantes.

**Principio Clave:** Todo `ProductItem` gestionado en el sistema posee una o más "Variantes de Diseño".

*   Un **producto individual** es un `ProductItem` con **una** Variante de Diseño asociada.
*   Un **producto con variaciones** es un `ProductItem` con **múltiples** Variantes de Diseño asociadas.

De esta manera, la lógica fundamental del sistema no se duplica. El flujo de trabajo es siempre el mismo, y solo procesos específicos necesitan ser conscientes del número de variantes.

## 3. Modelo de Datos Propuesto

Se establece una relación de uno a muchos entre un `ProductItem` y sus `DesignVariant`.

*   **`ProductItem` (El Contenedor):**
    *   Representa el producto físico y comercial.
    *   Contiene las especificaciones técnicas comunes (materiales, dimensiones, etc.).
    *   Contiene la cantidad **total** (ej. 500).
    *   Gestiona el precio, el proveedor, los costos y el ciclo de vida de producción y logística.

*   **`DesignVariant` (El Contenido):**
    *   Representa una variación de branding específica.
    *   Contiene el nombre de la variante (ej. "Logo Marca A"), el archivo de arte, y la cantidad parcial (ej. 100).
    *   Gestiona su propio y único ciclo de vida de **aprobación de Maqueta Virtual (MV)**.

## 4. Impacto y Lógica en los Procesos de Negocio

La clave de este patrón es asignar la responsabilidad de cada proceso a la entidad correcta.

### 4.1. Procesos que Operan a Nivel de `ProductItem` (Contenedor)

La gran mayoría de los procesos solo interactúan con la entidad `ProductItem`, ignorando la cantidad de variantes que contiene. Esto simplifica enormemente la gestión.

*   **Sourcing y Costos:** Se negocia con el proveedor por la cantidad total del `ProductItem` para obtener economías de escala.
*   **Aprobación de Muestra de Preproducción (PPS):** Se produce y aprueba una única muestra física para validar la calidad del `ProductItem`.
*   **Producción:** Se emite una única orden de producción para el lote completo del `ProductItem`.
*   **Logística:** El `ProductItem` se trata como una sola entidad para fines de envío, aduanas y entrega.

### 4.2. Procesos que Operan a Nivel de `DesignVariant` (Contenido)

Solo un proceso necesita operar a nivel de variante, ya que es el único punto donde hay una diferencia real.

*   **Aprobación de Maqueta Virtual (MV):** Este proceso itera sobre cada `DesignVariant` asociado al `ProductItem`. Cada variante tiene un ciclo de aprobación de MV independiente. El `ProductItem` no puede avanzar en su ciclo de vida hasta que **todas** sus variantes tengan la MV aprobada.

### 5. Tabla Resumen de Responsabilidades

| Proceso | Entidad Principal de Gestión | Lógica Aplicada | 
| :--- | :--- | :--- |
| Sourcing y Costos | `ProductItem` | Se cotiza y costea el volumen total. No se consideran las variantes. |
| Aprobación de MV | `DesignVariant` | Se itera sobre cada variante. Se gestionan N aprobaciones. |
| Aprobación de PPS | `ProductItem` | Se aprueba la calidad física del producto una sola vez. |
| Producción | `ProductItem` | Se produce como un único lote. |
| Logística | `ProductItem` | Se envía y rastrea como una sola entidad. |

## 6. Conclusión

Este patrón evita la duplicación de lógica y datos. Permite que el sistema maneje el caso más simple (un producto individual) y el más complejo (un producto con múltiples diseños) a través del mismo flujo de trabajo robusto y eficiente, reflejando con mayor precisión la realidad operativa del negocio.
