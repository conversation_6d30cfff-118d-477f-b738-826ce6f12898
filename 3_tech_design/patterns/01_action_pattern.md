# 1. Action Pattern (Command Pattern)

**Referencia ADR:** [ADR-006](../../2_architecture/adrs/adr006_crud_arquitectura_simplificada.md)

## Propósito
Encapsular casos de uso CRUD con validaciones de negocio, version tracking opcional, y efectos secundarios controlados en una única clase simple y mantenible.

## Estructura

```php
abstract class BaseAction
{
    abstract public function execute(mixed $data): mixed;
    
    protected function authorize(string $operation, mixed $resource = null): void
    {
        // Autorización simple basada en policies Laravel
        $user = auth()->user();
        if (!$user->can($operation, $resource)) {
            throw new AuthorizationException();
        }
    }
    
    protected function log(string $message, array $context = []): void
    {
        Log::info($message, array_merge($context, [
            'user_id' => auth()->id(),
            'action' => static::class
        ]));
    }
}
```

## Acciones Principales CRUD + Version Tracking

- **Project Management:** `CreateProjectAction`, `UpdateProjectAction`, `CalculateProjectStatusAction`
- **ProductItem Management:** `CreateProductItemAction`, `UpdateProductItemAction`, `RequoteProductItemAction`
- **User Management:** `CreateUserAction`, `UpdateUserAction`, `ManageUserRolesAction`
- **Master Data:** `CreateCustomerAction`, `UpdateSupplierAction`, `CreateProductCategoryAction`
- **Version Tracking:** Solo para ProductItem requotes y cambios significativos

## Ejemplo de Implementación: `RequoteProductItemAction`

```php
class RequoteProductItemAction extends BaseAction
{
    public function __construct(
        private ProductItemVersionService $versionService
    ) {}

    public function execute(RequoteProductItemData $data): ProductItem
    {
        $this->authorize('update', $data->productItem);
        
        $item = $data->productItem;
        
        // Capturar datos previos para version tracking
        $previousData = [
            'specifications' => $item->specifications,
            'status' => $item->status->value,
            'price' => $data->previousPrice,
        ];
        
        return DB::transaction(function() use ($item, $data, $previousData) {
            // CRUD simple: actualizar item
            $item->update([
                'specifications' => $data->newSpecifications,
                'status' => $data->newStatus,
            ]);
            
            $newData = [
                'specifications' => $data->newSpecifications,
                'status' => $data->newStatus->value,
                'price' => $data->newPrice,
            ];
            
            // Version tracking automático para requotes
            $this->versionService->createVersion(
                item: $item,
                changeType: 'requote',
                previousData: $previousData,
                newData: $newData,
                summary: $data->reason
            );
            
            // Logging simple
            $this->log('ProductItem requoted', [
                'product_item_id' => $item->id,
                'previous_price' => $data->previousPrice,
                'new_price' => $data->newPrice,
                'reason' => $data->reason,
            ]);
            
            return $item;
        });
    }
}

// DTO simple con validación
class RequoteProductItemData extends Data
{
    public function __construct(
        public ProductItem $productItem,
        public array $newSpecifications,
        public ProductItemStatus $newStatus,
        public float $previousPrice,
        public float $newPrice,
        public string $reason
    ) {}
    
    public static function rules(): array
    {
        return [
            'newSpecifications' => ['required', 'array'],
            'newStatus' => ['required', 'enum:' . ProductItemStatus::class],
            'previousPrice' => ['required', 'numeric', 'min:0'],
            'newPrice' => ['required', 'numeric', 'min:0'],
            'reason' => ['required', 'string', 'max:500'],
        ];
    }
}
```

## Ejemplo Simple: `UpdateProjectAction`

```php
class UpdateProjectAction extends BaseAction
{
    public function execute(UpdateProjectData $data): Project
    {
        $this->authorize('update', $data->project);
        
        // CRUD directo sin complejidad adicional
        $data->project->update([
            'name' => $data->name,
            'description' => $data->description,
            'timeline' => $data->timeline,
        ]);
        
        // Logging básico
        $this->log('Project updated', [
            'project_id' => $data->project->id,
            'name' => $data->name,
        ]);
        
        return $data->project;
    }
}
```

## Principios de la Arquitectura Simplificada

1. **Simplicidad primero**: CRUD directo sin over-engineering
2. **Version tracking selectivo**: Solo para cambios críticos (requotes)
3. **DTOs para tipado**: Validación automática y contratos claros
4. **Logging manual**: Log explícito vs automático
5. **Autorización simple**: Laravel policies estándar

## Cuándo Usar Version Tracking

- ✅ **RequoteProductItemAction**: Siempre crea versión
- ✅ **UpdateProductItemAction**: Si hay cambios significativos
- ❌ **UpdateProjectAction**: No necesario
- ❌ **CreateUserAction**: No necesario
- ❌ **UpdateCustomerAction**: No necesario

## Flujo Típico de Acción

```mermaid
sequenceDiagram
    participant UI as Filament UI
    participant Action as Action
    participant DTO as Data/DTO
    participant Model as Eloquent Model
    participant Version as VersionService
    participant DB as Database

    UI->>Action: execute(formData)
    Action->>DTO: validate & transform
    Action->>Action: authorize(operation, resource)
    Action->>Model: update(data)
    Model->>DB: UPDATE query
    DB-->>Model: OK
    
    alt Version tracking needed
        Action->>Version: createVersion(item, changeType, data)
        Version->>DB: INSERT version record
        DB-->>Version: OK
    end
    
    Action->>Action: log(message, context)
    Action-->>UI: return updated model
```

## Beneficios de la Aproximación Simple

✅ **Development Speed**: 10x más rápido que Event Sourcing
✅ **Team Onboarding**: Cualquier dev Laravel puede trabajar inmediatamente  
✅ **Debugging**: Path directo, troubleshooting simple
✅ **Performance**: Sin overhead, queries directos optimizados
✅ **Maintenance**: Pocas moving parts, menos superficie para bugs
✅ **Testability**: Testing unit simple y directo