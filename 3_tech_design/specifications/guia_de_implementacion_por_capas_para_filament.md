# Guía de Implementación por Capas para Filament

> **Propósito:** Esta especificación técnica detalla la arquitectura y el flujo de trabajo para implementar un sistema de autorización en aplicaciones construidas con Filament v4, utilizando un enfoque por capas.

---

## 1. Arquitectura

La arquitectura se basa en el principio de separación de responsabilidades, donde cada componente del sistema de seguridad tiene un rol único y definido.

### 1.1. Capa de Modelo de Autorización (Spatie Permissions)
*   **Responsabilidad:** Actúa como la única fuente de verdad para la definición de `Roles` y `Permisos`.
*   **Implementación:** Se utiliza el paquete `spatie/laravel-permission`. Se activa la funcionalidad de "Teams" para acotar los roles de los usuarios a un contexto específico (ej. país).

### 1.2. Capa de Aislamiento de Datos (Global Scopes)
*   **Responsabilidad:** Filtrar las consultas a la base de datos para que los usuarios solo puedan acceder a los registros que pertenecen a su ámbito de equipo.
*   **Implementación:** Un `Global Scope` de Eloquent que añade una cláusula `WHERE team_id = ?` a las consultas de los modelos designados.

### 1.3. Capa de Lógica de Autorización (Laravel Policies)
*   **Responsabilidad:** Centralizar la lógica de negocio para las decisiones de autorización en el backend.
*   **Implementación:** Clases `Policy` que contienen la lógica y consultan a Spatie (`$user->can(...)`) para verificar permisos sobre una instancia de modelo específica.

### 1.4. Capa de Seguridad de UI (Filament Shield)
*   **Responsabilidad:** Conectar el motor de permisos con la interfaz de usuario, gestionando la visibilidad de los componentes de forma automática.
*   **Implementación:** El paquete `bezhanov/filament-shield` lee los permisos de Spatie y, siguiendo su convención de nomenclatura, gestiona la visibilidad de Recursos, Páginas, Widgets y Acciones en Filament.

---

## 2. Secuencia de Implementación

El desarrollo se estructura en dos fases principales: una configuración inicial del proyecto y un ciclo de desarrollo iterativo para las entidades de negocio.

### 2.1. Fase de Configuración Inicial
Esta fase se ejecuta una sola vez al comienzo del proyecto para establecer el entorno de trabajo.

1.  **Instalación de Laravel:** Se inicia el proyecto base.
2.  **Creación de Modelos Fundamentales:** Se crean los modelos Eloquent y las migraciones para las entidades `User` y `Team`.
3.  **Instalación de Spatie Permissions:** Se instala el paquete, se publica su configuración, se activa la funcionalidad de "Teams" y se ejecuta su migración.
4.  **Definición de Roles Base:** Se utiliza un seeder para crear los `Roles` principales (ej. `Analista de Ventas`) sin asignarles aún permisos de entidades.
5.  **Instalación de Filament:** Se instala y configura el panel de administración.
6.  **Instalación de Filament Shield:** Se instala el plugin y se configura para operar con el modo "Teams".

### 2.2. Fase de Ciclo de Desarrollo por Entidad
Este ciclo se repite para cada nueva entidad de negocio que se introduce en el sistema (ej. `Project`, `ProductItem`).

1.  **Crear Modelo y Factory:** Se define el nuevo modelo de Eloquent (ej. `Project`) junto con su migración y su `Factory` para pruebas.
2.  **Definir y Asignar Permisos:** Se actualiza el seeder de Spatie para añadir los permisos específicos de la nueva entidad (ej. `create_project`, `update_project`). Estos permisos se asignan a los roles correspondientes.
3.  **Escribir Tests de Autorización:** Se crean tests para verificar la lógica de los permisos sobre la nueva entidad.
4.  **Crear Resource de Filament:** Se genera el `Resource` de Filament para la entidad, definiendo sus tablas y formularios.
5.  **Generar Permisos de Shield:** Se ejecuta el comando `shield:generate` para que el sistema verifique y registre los permisos de la nueva interfaz según su convención.
6.  **Probar Integración:** Se ejecutan las migraciones y seeders, y se verifica el comportamiento del nuevo `Resource` en la interfaz de usuario para un usuario con los roles adecuados.