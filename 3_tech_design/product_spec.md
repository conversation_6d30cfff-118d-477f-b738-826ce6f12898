# Especificación Técnica: Sistema de Productos y Especificaciones Dinámicas

> **Propósito:** Documento técnico completo para la implementación del sistema de gestión de productos con taxonomías flexibles y especificaciones dinámicas agrupadas en PromoSmart.

---

## 1. Decisión Arquitectónica

### 1.1 Contexto del Problema

PromoSmart maneja múltiples categorías de productos promocionales donde cada categoría tiene especificaciones completamente diferentes:

- **Textiles**: Material, GSM, Tallas, Colores, Área de Impresión
- **Drinkware**: Material, Capacidad, Tipo de Asa, Apto Microondas
- **Merchandising**: Dimensiones, Peso, Capacidad de Almacenamiento
- **Tech Accessories**: Capacidad Batería, Puertos, Conectividad

**Desafíos:**
1. Sistema debe ser **flexible** para agregar nuevas categorías sin cambios de código
2. **Validación dinámica** según tipo de producto
3. **UI adaptativa** que se ajuste automáticamente
4. **Performance optimizada** para catálogos extensos
5. **Especificaciones agrupadas** por tipos comunes entre productos

### 1.2 Evaluación de Alternativas

| Criterio | Enfoque Evolutivo (Gemini) | Enfoque Robusto (Claude) |
|:---------|:--------------------------:|:------------------------:|
| **Time to Market** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Costo Inicial** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **Flexibilidad Futura** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Complejidad Implementación** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **Adecuación a PromoSmart** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

### 1.3 Decisión Final

**Se adopta el Enfoque Evolutivo (Gemini)** con extensiones para agrupación de atributos:

**Justificación:**
- ✅ PromoSmart tiene **jerarquía de 2 niveles** (Category → Subcategory) - relaciones Eloquent estándar son suficientes
- ✅ **Time to market crítico** para validación del modelo de negocio
- ✅ **Team size pequeño** requiere simplicidad inicial
- ✅ **Escalabilidad planificada** permite migrar a Nested Set si crece la jerarquía

**Extensiones Agregadas:**
- Agrupación de atributos por tipos comunes
- UI dinámica enriquecida con Filament
- Validación cross-field avanzada

---

## 2. Arquitectura del Sistema

### 2.1 Estructura de Entidades

```mermaid
erDiagram
    ProductCategory ||--o{ ProductSubcategory : contains
    ProductSubcategory ||--o{ ProductItem : categorizes
    ProductSubcategory ||--o{ SubcategoryAttributeGroup : configures
    AttributeGroup ||--o{ SubcategoryAttributeGroup : configured_in
    
    ProductCategory {
        id integer PK
        name string
        slug string
        description text
    }
    
    ProductSubcategory {
        id integer PK
        name string
        slug string
        category_id integer FK
    }
    
    AttributeGroup {
        id integer PK
        name string
        label string
        description text
        schema json
    }
    
    SubcategoryAttributeGroup {
        id integer PK
        product_subcategory_id integer FK
        attribute_group_id integer FK
        required boolean
        overrides json
    }
    
    ProductItem {
        id integer PK
        name string
        subcategory_id integer FK
        specifications json
        status enum
        quantity integer
    }
```

### 2.2 Flujo de Datos

1. **Definición de Grupos de Atributos**: Se definen tipos comunes (Material, Dimensiones, Impresión, etc.)
2. **Composición por Subcategoría**: Cada subcategoría selecciona qué grupos necesita
3. **Override Específico**: Personalización de atributos por subcategoría si es necesario
4. **Schema Compilation**: Generación dinámica del esquema final por subcategoría
5. **UI Generation**: Filament genera formularios dinámicos basados en el esquema
6. **Validation**: Validación automática según reglas del esquema compilado
7. **Storage**: Almacenamiento en JSON agrupado por tipos de atributos

---

## 3. Implementación Base (Enfoque Gemini)

### 3.1 Modelos Principales

#### ProductCategory

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class ProductCategory extends Model
{
    protected $fillable = ['name', 'slug', 'description'];

    public function subcategories(): HasMany
    {
        return $this->hasMany(ProductSubcategory::class, 'category_id');
    }

    public function products(): HasManyThrough
    {
        return $this->hasManyThrough(ProductItem::class, ProductSubcategory::class);
    }
}
```

#### ProductSubcategory

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Validation\Rule;

class ProductSubcategory extends Model
{
    protected $fillable = ['name', 'slug', 'category_id', 'description'];

    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class);
    }

    public function products(): HasMany
    {
        return $this->hasMany(ProductItem::class, 'subcategory_id');
    }

    public function attributeGroups(): BelongsToMany
    {
        return $this->belongsToMany(AttributeGroup::class, 'product_subcategory_attribute_group')
                    ->withPivot('required', 'overrides')
                    ->withTimestamps();
    }

    /**
     * Genera el schema compilado combinando todos los grupos de atributos
     */
    public function getCompiledSchema(): array
    {
        $compiledSchema = [];

        foreach ($this->attributeGroups as $group) {
            $groupSchema = $group->getSchemaForSubcategory($this);

            // Agregar campos del grupo al schema compilado
            foreach ($groupSchema as $field => $config) {
                $compiledSchema["{$group->name}_{$field}"] = array_merge($config, [
                    'group' => $group->name,
                    'group_label' => $group->label
                ]);
            }
        }

        return $compiledSchema;
    }

    /**
     * Retorna schema agrupado por tipos de atributos para UI
     */
    public function getGroupedSchema(): array
    {
        $groupedSchema = [];

        foreach ($this->attributeGroups as $group) {
            $groupSchema = $group->getSchemaForSubcategory($this);

            $groupedSchema[$group->name] = [
                'label' => $group->label,
                'description' => $group->description,
                'required' => $group->pivot->required,
                'fields' => $groupSchema
            ];
        }

        return $groupedSchema;
    }

    /**
     * Genera reglas de validación dinámicas
     */
    public function generateValidationRules(): array
    {
        $rules = [];
        $groupedSchema = $this->getGroupedSchema();

        foreach ($groupedSchema as $groupName => $groupConfig) {
            foreach ($groupConfig['fields'] as $field => $config) {
                $fieldRules = [];

                if ($config['required'] ?? false) {
                    $fieldRules[] = 'required';
                } else {
                    $fieldRules[] = 'nullable';
                }

                match($config['type']) {
                    'enum' => $this->addEnumRules($fieldRules, $config),
                    'integer' => $this->addIntegerRules($fieldRules, $config),
                    'array' => $fieldRules[] = 'array',
                    'object' => $this->addObjectRules($rules, $groupName, $field, $config),
                    'boolean' => $fieldRules[] = 'boolean',
                    default => $fieldRules[] = 'string|max:255'
                };

                if ($config['type'] !== 'object') {
                    $rules["specifications.{$groupName}.{$field}"] = $fieldRules;
                }
            }
        }

        return $rules;
    }

    private function addEnumRules(array &$fieldRules, array $config): void
    {
        if ($config['multiple'] ?? false) {
            $fieldRules[] = 'array';
            $fieldRules[] = Rule::in($config['options']);
        } else {
            $fieldRules[] = Rule::in($config['options']);
        }
    }

    private function addIntegerRules(array &$fieldRules, array $config): void
    {
        $fieldRules[] = 'integer';

        if (isset($config['min'])) {
            $fieldRules[] = "min:{$config['min']}";
        }

        if (isset($config['max'])) {
            $fieldRules[] = "max:{$config['max']}";
        }
    }

    private function addObjectRules(array &$rules, string $groupName, string $field, array $config): void
    {
        foreach ($config['properties'] as $prop => $propConfig) {
            $propRules = ['required'];

            match($propConfig['type']) {
                'integer' => $propRules[] = 'integer',
                'enum' => $propRules[] = Rule::in($propConfig['options']),
                'string' => $propRules[] = 'string',
                default => null
            };

            $rules["specifications.{$groupName}.{$field}.{$prop}"] = $propRules;
        }
    }
}
```

---

## 4. Sistema de Grupos de Atributos

### 4.1 Modelo AttributeGroup

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class AttributeGroup extends Model
{
    protected $fillable = ['name', 'label', 'description', 'schema'];
    protected $casts = ['schema' => 'array'];

    public function subcategories(): BelongsToMany
    {
        return $this->belongsToMany(ProductSubcategory::class, 'product_subcategory_attribute_group')
                    ->withPivot('required', 'overrides')
                    ->withTimestamps();
    }

    /**
     * Obtiene el schema para una subcategoría específica aplicando overrides
     */
    public function getSchemaForSubcategory(ProductSubcategory $subcategory): array
    {
        $pivot = $this->subcategories()
                     ->where('product_subcategory_id', $subcategory->id)
                     ->first()
                     ?->pivot;

        if (!$pivot) return $this->schema;

        // Aplicar overrides específicos de la subcategoría
        $schema = $this->schema;
        $overrides = $pivot->overrides ?? [];

        foreach ($overrides as $field => $fieldOverrides) {
            if (isset($schema[$field])) {
                $schema[$field] = array_merge($schema[$field], $fieldOverrides);
            }
        }

        return $schema;
    }
}
```

### 4.2 Migraciones

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// Migración para categorías base (enfoque Gemini)
class CreateProductCategoriesTable extends Migration
{
    public function up()
    {
        Schema::create('product_categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->text('description')->nullable();
            $table->timestamps();
        });
    }
}

// Migración para subcategorías
class CreateProductSubcategoriesTable extends Migration
{
    public function up()
    {
        Schema::create('product_subcategories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('slug')->unique();
            $table->foreignId('category_id')->constrained('product_categories')->onDelete('cascade');
            $table->text('description')->nullable();
            $table->timestamps();
            
            $table->index(['category_id', 'name']);
        });
    }
}

// Migración para grupos de atributos
class CreateAttributeGroupsTable extends Migration
{
    public function up()
    {
        Schema::create('attribute_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('label');
            $table->text('description')->nullable();
            $table->json('schema'); // Schema de atributos del grupo
            $table->timestamps();
        });
    }
}

// Migración para tabla pivot subcategory-attribute_group
class CreateSubcategoryAttributeGroupTable extends Migration
{
    public function up()
    {
        Schema::create('product_subcategory_attribute_group', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_subcategory_id')->constrained()->onDelete('cascade');
            $table->foreignId('attribute_group_id')->constrained()->onDelete('cascade');
            $table->boolean('required')->default(false);
            $table->json('overrides')->nullable(); // Personalizaciones específicas
            $table->timestamps();
            
            $table->unique(['product_subcategory_id', 'attribute_group_id'], 'subcat_attr_unique');
        });
    }
}

// Migración para productos con especificaciones JSON agrupadas
class CreateProductItemsTable extends Migration
{
    public function up()
    {
        Schema::create('product_items', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('subcategory_id')->constrained('product_subcategories');
            $table->integer('quantity')->default(1);
            $table->json('specifications')->nullable(); // Especificaciones agrupadas
            $table->enum('status', ['draft', 'sourcing', 'quoted', 'confirmed', 'in_progress', 'completed']);
            $table->timestamps();
            
            $table->index(['subcategory_id', 'status']);
        });
    }
}
```

### 4.3 Enums del Sistema

```php
<?php

namespace App\Enums;

enum ProductStatus: string
{
    case Draft = 'draft';
    case Sourcing = 'sourcing';
    case Quoted = 'quoted';
    case Confirmed = 'confirmed';
    case InProgress = 'in_progress';
    case Completed = 'completed';

    public function label(): string
    {
        return match($this) {
            self::Draft => 'Draft',
            self::Sourcing => 'Sourcing',
            self::Quoted => 'Quoted',
            self::Confirmed => 'Confirmed',
            self::InProgress => 'In Progress',
            self::Completed => 'Completed',
        };
    }

    public function color(): string
    {
        return match($this) {
            self::Draft => 'gray',
            self::Sourcing => 'warning',
            self::Quoted => 'info',
            self::Confirmed => 'success',
            self::InProgress => 'warning',
            self::Completed => 'success',
        };
    }
}

enum MaterialType: string
{
    case Cotton = 'cotton';
    case Polyester = 'polyester';
    case CottonPolyBlend = 'cotton_poly_blend';
    case Plastic = 'plastic';
    case Metal = 'metal';
    case Glass = 'glass';
    case Ceramic = 'ceramic';
    case StainlessSteel = 'stainless_steel';

    public function label(): string
    {
        return match($this) {
            self::Cotton => 'Cotton',
            self::Polyester => 'Polyester',
            self::CottonPolyBlend => 'Cotton/Poly Blend',
            self::Plastic => 'Plastic',
            self::Metal => 'Metal',
            self::Glass => 'Glass',
            self::Ceramic => 'Ceramic',
            self::StainlessSteel => 'Stainless Steel',
        };
    }
}
```

### 4.4 Modelo ProductItem Extendido

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use App\Enums\ProductStatus;

class ProductItem extends Model
{
    protected $fillable = [
        'name', 'subcategory_id', 'quantity', 'specifications', 'status'
    ];

    protected $casts = [
        'specifications' => 'array',
        'status' => ProductStatus::class
    ];

    public function subcategory(): BelongsTo
    {
        return $this->belongsTo(ProductSubcategory::class, 'subcategory_id');
    }

    // Relación con Project se agregará cuando se implemente el módulo de proyectos
    // public function project(): BelongsTo
    // {
    //     return $this->belongsTo(Project::class);
    // }

    /**
     * Obtiene especificaciones estructuradas por grupos
     */
    public function getSpecifications(): array
    {
        /*
        Estructura esperada:
        {
            "material": {
                "type": "Cotton",
                "composition": "100% Cotton",
                "certifications": ["OEKO-TEX"]
            },
            "dimensions": {
                "length": 70,
                "width": 50,
                "weight": 180
            },
            "printing": {
                "max_width": 25,
                "max_height": 30,
                "techniques": ["Screen Print", "Embroidery"]
            }
        }
        */
        return $this->specifications ?? [];
    }

    /**
     * Obtiene especificaciones formateadas para display
     */
    public function getFormattedSpecifications(): array
    {
        $specs = $this->getSpecifications();
        $formatted = [];
        $groupedSchema = $this->subcategory->getGroupedSchema();

        foreach ($groupedSchema as $groupName => $groupConfig) {
            $groupSpecs = $specs[$groupName] ?? [];

            $formatted[$groupName] = [
                'label' => $groupConfig['label'],
                'values' => []
            ];

            foreach ($groupConfig['fields'] as $field => $fieldConfig) {
                if (isset($groupSpecs[$field])) {
                    $formatted[$groupName]['values'][$field] = [
                        'label' => $fieldConfig['label'],
                        'value' => $groupSpecs[$field],
                        'formatted' => $this->formatValue($groupSpecs[$field], $fieldConfig)
                    ];
                }
            }
        }

        return $formatted;
    }

    /**
     * Formatea un valor según su configuración
     */
    private function formatValue($value, array $config): string
    {
        return match($config['type']) {
            'integer' => $value . ($config['unit'] ?? ''),
            'array' => implode(', ', $value),
            'boolean' => $value ? 'Sí' : 'No',
            'object' => $this->formatObjectValue($value),
            default => (string) $value
        };
    }

    private function formatObjectValue($value): string
    {
        if (!is_array($value)) return (string) $value;

        $formatted = [];
        foreach ($value as $key => $val) {
            $formatted[] = ucfirst($key) . ': ' . $val;
        }
        return implode(', ', $formatted);
    }

    /**
     * Establece especificaciones con validación
     */
    public function setSpecificationsAttribute(array $specs): void
    {
        // Validar especificaciones antes de guardar si la subcategoría está disponible
        if ($this->subcategory) {
            $validatedSpecs = ProductSpecificationValidator::validate($specs, $this->subcategory);
            $this->attributes['specifications'] = json_encode($validatedSpecs);
        } else {
            $this->attributes['specifications'] = json_encode($specs);
        }
    }
}
```

---

## 5. Validación Dinámica

### 5.1 Servicio de Validación

```php
<?php

namespace App\Services;

use App\Models\ProductSubcategory;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ProductSpecificationValidator
{
    /**
     * Valida especificaciones según el schema de la subcategoría
     */
    public static function validate(array $specs, ProductSubcategory $subcategory): array
    {
        $rules = $subcategory->generateValidationRules();
        
        return Validator::make(['specifications' => $specs], $rules)->validate()['specifications'];
    }

    /**
     * Construye reglas de validación para una subcategoría
     */
    public static function buildRules(ProductSubcategory $subcategory): array
    {
        return $subcategory->generateValidationRules();
    }

    /**
     * Valida un campo específico
     */
    public static function validateField(string $field, $value, array $config): bool
    {
        $rules = self::buildRulesForField($field, $config);
        $validator = Validator::make([$field => $value], [$field => $rules]);
        
        return $validator->passes();
    }

    /**
     * Construye reglas para un campo individual
     */
    private static function buildRulesForField(string $field, array $config): array
    {
        $rules = [];

        // Requerido/Opcional
        $rules[] = ($config['required'] ?? false) ? 'required' : 'nullable';

        // Reglas específicas por tipo
        match($config['type']) {
            'enum' => $rules = array_merge($rules, self::enumRules($config)),
            'integer' => $rules = array_merge($rules, self::integerRules($config)),
            'object' => $rules = array_merge($rules, self::objectRules($field, $config)),
            'array' => $rules[] = 'array',
            'boolean' => $rules[] = 'boolean',
            default => $rules[] = 'string'
        };

        return $rules;
    }

    private static function enumRules(array $config): array
    {
        $rules = [];

        if ($config['multiple'] ?? false) {
            $rules[] = 'array';
            $rules[] = Rule::in($config['options']);
        } else {
            $rules[] = Rule::in($config['options']);
        }

        return $rules;
    }

    private static function integerRules(array $config): array
    {
        $rules = ['integer'];

        if (isset($config['min'])) $rules[] = "min:{$config['min']}";
        if (isset($config['max'])) $rules[] = "max:{$config['max']}";

        return $rules;
    }

    private static function objectRules(string $field, array $config): array
    {
        $rules = ['array'];

        // Validar propiedades del objeto
        foreach ($config['properties'] as $prop => $propConfig) {
            $propRules = self::buildRulesForField("{$field}.{$prop}", $propConfig);
            $rules["{$field}.{$prop}"] = $propRules;
        }

        return $rules;
    }
}
```

### 5.2 FormRequest Dinámico

```php
<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\ProductSubcategory;
use App\Services\ProductSpecificationValidator;

class CreateProductItemRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $baseRules = [
            'name' => 'required|string|max:255',
            'subcategory_id' => 'required|exists:product_subcategories,id',
            'quantity' => 'required|integer|min:1|max:100000',
            'status' => ['required', Rule::enum(ProductStatus::class)]
        ];

        if ($this->subcategory_id) {
            $subcategory = ProductSubcategory::find($this->subcategory_id);
            if ($subcategory) {
                $specRules = ProductSpecificationValidator::buildRules($subcategory);
                $baseRules = array_merge($baseRules, $specRules);
            }
        }

        return $baseRules;
    }

    public function messages(): array
    {
        $messages = [
            'name.required' => 'El nombre del producto es obligatorio.',
            'subcategory_id.required' => 'Debe seleccionar una subcategoría.',
            'quantity.required' => 'La cantidad es obligatoria.',
            'quantity.min' => 'La cantidad debe ser al menos 1.'
        ];

        if ($this->subcategory_id) {
            $subcategory = ProductSubcategory::find($this->subcategory_id);
            $groupedSchema = $subcategory?->getGroupedSchema() ?? [];

            foreach ($groupedSchema as $groupName => $groupConfig) {
                foreach ($groupConfig['fields'] as $field => $config) {
                    $label = $config['label'] ?? $field;
                    $fieldKey = "specifications.{$groupName}.{$field}";
                    
                    $messages["{$fieldKey}.required"] = "El campo {$label} es obligatorio.";
                    
                    if ($config['type'] === 'enum') {
                        $options = implode(', ', $config['options']);
                        $messages["{$fieldKey}.in"] = "El campo {$label} debe ser uno de: {$options}.";
                    }
                }
            }
        }

        return $messages;
    }
}
```

---

## 6. Implementación UI con Filament

### 6.1 Resource Principal

```php
<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductItemResource\Pages;
use App\Models\ProductItem;
use App\Models\ProductSubcategory;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;

class ProductItemResource extends Resource
{
    protected static ?string $model = ProductItem::class;
    protected static ?string $navigationIcon = 'heroicon-o-cube';
    protected static ?string $navigationGroup = 'Product Catalog';

    public static function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Basic Information')
                ->schema([
                    Forms\Components\TextInput::make('name')
                        ->required()
                        ->maxLength(255),
                    
                    Forms\Components\Select::make('subcategory_id')
                        ->relationship('subcategory', 'name')
                        ->searchable()
                        ->preload()
                        ->required()
                        ->live()
                        ->afterStateUpdated(function($state, callable $set) {
                            // Limpiar especificaciones cuando cambia la subcategoría
                            $set('specifications', []);
                        }),
                    
                    Forms\Components\TextInput::make('quantity')
                        ->numeric()
                        ->required()
                        ->minValue(1)
                        ->maxValue(100000)
                        ->default(1),
                    
                    Forms\Components\Select::make('status')
                        ->options(ProductStatus::class)
                        ->required()
                        ->default(ProductStatus::Draft)
                ]),
            
            // 🎯 SECCIONES DINÁMICAS DE ESPECIFICACIONES
            ...self::buildDynamicAttributeGroups()
        ]);
    }

    protected static function buildDynamicAttributeGroups(): array
    {
        return [
            Forms\Components\Section::make('Product Specifications')
                ->description('Configure specifications based on the selected category')
                ->schema(function($get) {
                    $subcategoryId = $get('subcategory_id');
                    if (!$subcategoryId) {
                        return [
                            Forms\Components\Placeholder::make('select_category')
                                ->content('Select a subcategory first to configure specifications')
                        ];
                    }
                    
                    $subcategory = ProductSubcategory::with('attributeGroups')->find($subcategoryId);
                    if (!$subcategory) return [];
                    
                    // Generar secciones dinámicas por cada grupo de atributos
                    return self::buildGroupSections($subcategory);
                })
                ->visible(fn($get) => $get('subcategory_id'))
                ->collapsible()
        ];
    }

    protected static function buildGroupSections(ProductSubcategory $subcategory): array
    {
        $sections = [];
        $groupedSchema = $subcategory->getGroupedSchema();

        foreach ($groupedSchema as $groupName => $groupConfig) {
            $sections[] = Forms\Components\Section::make($groupConfig['label'])
                ->description($groupConfig['description'])
                ->schema(self::buildFieldsForGroup($groupName, $groupConfig))
                ->columns(2)
                ->collapsible()
                ->collapsed(false)
                ->icon(self::getGroupIcon($groupName));
        }

        return $sections;
    }

    protected static function buildFieldsForGroup(string $groupName, array $groupConfig): array
    {
        $fields = [];

        foreach ($groupConfig['fields'] as $fieldName => $fieldConfig) {
            $fullFieldName = "specifications.{$groupName}.{$fieldName}";

            $field = match($fieldConfig['type']) {
                'enum' => self::buildEnumField($fullFieldName, $fieldConfig),
                'integer' => self::buildIntegerField($fullFieldName, $fieldConfig),
                'string' => self::buildStringField($fullFieldName, $fieldConfig),
                'boolean' => self::buildBooleanField($fullFieldName, $fieldConfig),
                'object' => self::buildObjectField($fullFieldName, $fieldConfig),
                'array' => self::buildArrayField($fullFieldName, $fieldConfig),
                default => Forms\Components\TextInput::make($fullFieldName)
                    ->label($fieldConfig['label'])
            };

            $fields[] = $field;
        }

        return $fields;
    }

    protected static function buildEnumField(string $fieldName, array $config): Forms\Components\Select|Forms\Components\CheckboxList
    {
        $isMultiple = $config['multiple'] ?? false;

        $field = $isMultiple 
            ? Forms\Components\CheckboxList::make($fieldName)->columns(3)
            : Forms\Components\Select::make($fieldName);

        return $field
            ->label($config['label'])
            ->options(array_combine($config['options'], $config['options']))
            ->required($config['required'] ?? false)
            ->helperText($config['help_text'] ?? null)
            ->live()
            ->afterStateUpdated(function($state, callable $set) use ($fieldName) {
                // Lógica condicional entre campos
                if (str_contains($fieldName, 'material.type')) {
                    self::handleMaterialTypeChange($state, $set);
                }
            });
    }

    protected static function buildIntegerField(string $fieldName, array $config): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make($fieldName)
            ->label($config['label'])
            ->numeric()
            ->minValue($config['min'] ?? null)
            ->maxValue($config['max'] ?? null)
            ->step($config['step'] ?? 1)
            ->suffix($config['unit'] ?? null)
            ->required($config['required'] ?? false)
            ->helperText($config['help_text'] ?? null)
            ->placeholder($config['placeholder'] ?? null)
            ->rules(function() use ($config) {
                $rules = ['numeric'];
                if (isset($config['min'])) $rules[] = "min:{$config['min']}";
                if (isset($config['max'])) $rules[] = "max:{$config['max']}";
                return $rules;
            });
    }

    protected static function buildStringField(string $fieldName, array $config): Forms\Components\TextInput
    {
        return Forms\Components\TextInput::make($fieldName)
            ->label($config['label'])
            ->maxLength($config['max_length'] ?? 255)
            ->required($config['required'] ?? false)
            ->helperText($config['help_text'] ?? null)
            ->placeholder($config['placeholder'] ?? null);
    }

    protected static function buildBooleanField(string $fieldName, array $config): Forms\Components\Toggle
    {
        return Forms\Components\Toggle::make($fieldName)
            ->label($config['label'])
            ->helperText($config['help_text'] ?? null)
            ->default($config['default'] ?? false);
    }

    protected static function buildObjectField(string $fieldName, array $config): Forms\Components\Group
    {
        $properties = $config['properties'] ?? [];
        $propertyFields = [];

        foreach ($properties as $propName => $propConfig) {
            $propFieldName = "{$fieldName}.{$propName}";

            $propertyFields[] = match($propConfig['type']) {
                'integer' => Forms\Components\TextInput::make($propFieldName)
                    ->label($propConfig['label'] ?? ucfirst($propName))
                    ->numeric()
                    ->suffix($propConfig['unit'] ?? null)
                    ->required($propConfig['required'] ?? true),
                    
                'enum' => Forms\Components\Select::make($propFieldName)
                    ->label($propConfig['label'] ?? ucfirst($propName))
                    ->options(array_combine($propConfig['options'] ?? [], $propConfig['options'] ?? []))
                    ->required($propConfig['required'] ?? true),
                    
                default => Forms\Components\TextInput::make($propFieldName)
                    ->label($propConfig['label'] ?? ucfirst($propName))
                    ->required($propConfig['required'] ?? true)
            };
        }

        return Forms\Components\Group::make($propertyFields)
            ->label($config['label'])
            ->columns(count($propertyFields) > 2 ? 2 : 1)
            ->columnSpanFull();
    }

    protected static function buildArrayField(string $fieldName, array $config): Forms\Components\TagsInput
    {
        return Forms\Components\TagsInput::make($fieldName)
            ->label($config['label'])
            ->required($config['required'] ?? false)
            ->helperText($config['help_text'] ?? null)
            ->suggestions($config['suggestions'] ?? []);
    }

    protected static function handleMaterialTypeChange(string $materialType, callable $set): void
    {
        match($materialType) {
            'Cotton' => $set('specifications.material.shrinkage_info', 'Pre-shrunk'),
            'Metal' => $set('specifications.material.finish_options', ['Brushed', 'Polished', 'Matte']),
            'Plastic' => $set('specifications.material.recycled_content', null),
            default => null
        };
    }

    protected static function getGroupIcon(string $groupName): string
    {
        return match($groupName) {
            'material' => 'heroicon-o-swatch',
            'dimensions' => 'heroicon-o-scale',
            'printing' => 'heroicon-o-printer',
            'packaging' => 'heroicon-o-archive-box',
            'electrical' => 'heroicon-o-bolt',
            default => 'heroicon-o-squares-2x2'
        };
    }

    public static function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('name')
                ->searchable()
                ->sortable(),
            
            Tables\Columns\TextColumn::make('subcategory.name')
                ->label('Subcategory')
                ->searchable()
                ->sortable(),
            
            Tables\Columns\TextColumn::make('subcategory.category.name')
                ->label('Category')
                ->searchable()
                ->sortable(),
            
            Tables\Columns\TextColumn::make('quantity')
                ->numeric()
                ->sortable(),
            
            // Columnas dinámicas de especificaciones comunes
            Tables\Columns\TextColumn::make('material_type')
                ->label('Material')
                ->getStateUsing(fn($record) => $record->specifications['material']['type'] ?? '-')
                ->toggleable(),
                
            Tables\Columns\TextColumn::make('dimensions_summary')
                ->label('Dimensions')
                ->getStateUsing(function($record) {
                    $dims = $record->specifications['dimensions'] ?? [];
                    if (empty($dims)) return '-';
                    
                    $parts = [];
                    if (isset($dims['length'])) $parts[] = $dims['length'] . 'cm';
                    if (isset($dims['width'])) $parts[] = $dims['width'] . 'cm';
                    if (isset($dims['weight'])) $parts[] = $dims['weight'] . 'g';
                    
                    return implode(' × ', $parts);
                })
                ->toggleable(),
            
            Tables\Columns\TextColumn::make('status')
                ->badge()
                ->color(fn (ProductStatus $state): string => $state->color()),
            
            Tables\Columns\TextColumn::make('created_at')
                ->dateTime()
                ->sortable()
                ->toggleable(isToggledHiddenByDefault: true),
        ])
        ->filters([
            Tables\Filters\SelectFilter::make('subcategory')
                ->relationship('subcategory', 'name')
                ->searchable()
                ->preload(),
            
            Tables\Filters\SelectFilter::make('category')
                ->options(fn() => \App\Models\ProductCategory::pluck('name', 'id'))
                ->query(function($query, array $data) {
                    if ($data['value']) {
                        $query->whereHas('subcategory.category', fn($q) => $q->where('id', $data['value']));
                    }
                }),
                
            Tables\Filters\SelectFilter::make('status')
                ->options(ProductStatus::class),
                
            // Filtros dinámicos por especificaciones
            Tables\Filters\SelectFilter::make('material_type')
                ->options(function() {
                    return ProductItem::whereJsonContains('specifications->material->type', '!=', null)
                        ->pluck('specifications->material->type', 'specifications->material->type')
                        ->unique()
                        ->sort();
                })
                ->query(function($query, array $data) {
                    if ($data['value']) {
                        $query->whereJsonContains('specifications->material->type', $data['value']);
                    }
                }),
        ])
        ->actions([
            Tables\Actions\ViewAction::make(),
            Tables\Actions\EditAction::make(),
        ])
        ->bulkActions([
            Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
            ]),
        ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Section::make('Product Information')
                ->schema([
                    TextEntry::make('name'),
                    TextEntry::make('subcategory.name')->label('Category'),
                    TextEntry::make('quantity'),
                    TextEntry::make('status')->badge(),
                ]),
            
            // Secciones dinámicas de especificaciones
            ...self::buildSpecificationInfoSections()
        ]);
    }

    protected static function buildSpecificationInfoSections(): array
    {
        return [
            Section::make('Specifications')
                ->schema(function($record) {
                    $sections = [];
                    $formattedSpecs = $record->getFormattedSpecifications();

                    foreach ($formattedSpecs as $groupName => $groupData) {
                        $entries = [];

                        foreach ($groupData['values'] as $field => $fieldData) {
                            $entries[] = TextEntry::make("spec_{$groupName}_{$field}")
                                ->label($fieldData['label'])
                                ->getStateUsing(fn() => $fieldData['formatted'])
                                ->badge(fn() => is_array($fieldData['value']));
                        }

                        $sections[] = Section::make($groupData['label'])
                            ->schema($entries)
                            ->columns(2)
                            ->collapsible()
                            ->icon(self::getGroupIcon($groupName));
                    }

                    return $sections;
                })
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProductItems::route('/'),
            'create' => Pages\CreateProductItem::route('/create'),
            'view' => Pages\ViewProductItem::route('/{record}'),
            'edit' => Pages\EditProductItem::route('/{record}/edit'),
        ];
    }
}
```

---

## 7. Seeders con Datos Realistas

### 7.1 Seeder de Grupos de Atributos

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AttributeGroup;
use App\Models\ProductCategory;
use App\Models\ProductSubcategory;

class AttributeGroupSeeder extends Seeder
{
    public function run()
    {
        $this->createAttributeGroups();
        $this->createCategoriesAndSubcategories();
        $this->attachGroupsToSubcategories();
    }

    private function createAttributeGroups()
    {
        $attributeGroups = [
            'material' => [
                'label' => 'Material Specifications',
                'description' => 'Common material properties for all products',
                'schema' => [
                    'type' => [
                        'type' => 'enum',
                        'required' => true,
                        'options' => ['cotton', 'polyester', 'plastic', 'metal', 'glass', 'ceramic'],
                        'label' => 'Material Type'
                    ],
                    'composition' => [
                        'type' => 'string',
                        'required' => false,
                        'label' => 'Detailed Composition',
                        'help_text' => 'e.g., 60% Cotton, 40% Polyester'
                    ],
                    'certifications' => [
                        'type' => 'enum',
                        'required' => false,
                        'multiple' => true,
                        'options' => ['oeko_tex', 'gots', 'bpa_free', 'fda_approved', 'ce_certified'],
                        'label' => 'Certifications'
                    ]
                ]
            ],
            
            'dimensions' => [
                'label' => 'Physical Dimensions',
                'description' => 'Size and weight specifications',
                'schema' => [
                    'length' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'unit' => 'cm', 
                        'label' => 'Length'
                    ],
                    'width' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'unit' => 'cm', 
                        'label' => 'Width'
                    ],
                    'height' => [
                        'type' => 'integer', 
                        'required' => false, 
                        'min' => 1, 
                        'unit' => 'cm', 
                        'label' => 'Height'
                    ],
                    'weight' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'unit' => 'g', 
                        'label' => 'Weight'
                    ]
                ]
            ],
            
            'printing' => [
                'label' => 'Printing Specifications',
                'description' => 'Available printing and customization options',
                'schema' => [
                    'max_width' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'unit' => 'cm', 
                        'label' => 'Max Print Width'
                    ],
                    'max_height' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'unit' => 'cm', 
                        'label' => 'Max Print Height'
                    ],
                    'techniques' => [
                        'type' => 'enum',
                        'required' => true,
                        'multiple' => true,
                        'options' => ['screen_print', 'digital_print', 'embroidery', 'laser_engraving', 'pad_print'],
                        'label' => 'Available Techniques'
                    ],
                    'max_colors' => [
                        'type' => 'integer', 
                        'required' => false, 
                        'min' => 1, 
                        'max' => 10, 
                        'label' => 'Max Colors'
                    ],
                    'area' => [
                        'type' => 'object',
                        'required' => true,
                        'properties' => [
                            'location' => [
                                'type' => 'enum', 
                                'options' => ['front_center', 'back_center', 'left_chest', 'right_chest', 'sleeve'],
                                'label' => 'Print Location'
                            ]
                        ],
                        'label' => 'Print Area'
                    ]
                ]
            ],
            
            'packaging' => [
                'label' => 'Packaging Information',
                'description' => 'Shipping and packaging specifications',
                'schema' => [
                    'individual_packaging' => [
                        'type' => 'enum',
                        'required' => true,
                        'options' => ['polybag', 'box', 'none'],
                        'label' => 'Individual Packaging'
                    ],
                    'units_per_carton' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'label' => 'Units per Carton'
                    ],
                    'carton_weight' => [
                        'type' => 'integer', 
                        'required' => true, 
                        'min' => 1, 
                        'unit' => 'kg', 
                        'label' => 'Carton Weight'
                    ],
                    'carton_dimensions' => [
                        'type' => 'object',
                        'required' => true,
                        'properties' => [
                            'length' => ['type' => 'integer', 'min' => 1, 'unit' => 'cm'],
                            'width' => ['type' => 'integer', 'min' => 1, 'unit' => 'cm'],
                            'height' => ['type' => 'integer', 'min' => 1, 'unit' => 'cm']
                        ],
                        'label' => 'Carton Dimensions'
                    ]
                ]
            ],

            'electrical' => [
                'label' => 'Electrical Specifications',
                'description' => 'Power and electrical connectivity specifications',
                'schema' => [
                    'capacity_mah' => [
                        'type' => 'integer',
                        'required' => true,
                        'min' => 1000,
                        'max' => 50000,
                        'unit' => 'mAh',
                        'label' => 'Battery Capacity'
                    ],
                    'input_voltage' => [
                        'type' => 'enum',
                        'required' => true,
                        'options' => ['5V', '9V', '12V', '110V', '220V'],
                        'label' => 'Input Voltage'
                    ],
                    'output_ports' => [
                        'type' => 'enum',
                        'required' => true,
                        'multiple' => true,
                        'options' => ['USB-A', 'USB-C', 'Lightning', 'Micro-USB', 'Wireless'],
                        'label' => 'Output Ports'
                    ],
                    'wireless_charging' => [
                        'type' => 'boolean',
                        'required' => false,
                        'label' => 'Wireless Charging Support'
                    ]
                ]
            ]
        ];

        foreach ($attributeGroups as $name => $config) {
            AttributeGroup::create([
                'name' => $name,
                'label' => $config['label'],
                'description' => $config['description'],
                'schema' => $config['schema']
            ]);
        }
    }

    private function createCategoriesAndSubcategories()
    {
        // Textiles
        $textiles = ProductCategory::create([
            'name' => 'Textiles',
            'slug' => 'textiles',
            'description' => 'Clothing and fabric-based promotional products'
        ]);

        $tshirts = ProductSubcategory::create([
            'name' => 'T-Shirts',
            'slug' => 't-shirts',
            'category_id' => $textiles->id,
            'description' => 'Custom printed and embroidered t-shirts'
        ]);

        $hoodies = ProductSubcategory::create([
            'name' => 'Hoodies',
            'slug' => 'hoodies',
            'category_id' => $textiles->id,
            'description' => 'Custom hooded sweatshirts'
        ]);

        // Drinkware
        $drinkware = ProductCategory::create([
            'name' => 'Drinkware',
            'slug' => 'drinkware',
            'description' => 'Mugs, bottles, and drinking accessories'
        ]);

        $mugs = ProductSubcategory::create([
            'name' => 'Mugs',
            'slug' => 'mugs',
            'category_id' => $drinkware->id,
            'description' => 'Ceramic and stainless steel mugs'
        ]);

        $bottles = ProductSubcategory::create([
            'name' => 'Water Bottles',
            'slug' => 'water-bottles',
            'category_id' => $drinkware->id,
            'description' => 'Reusable water bottles'
        ]);

        // Tech Accessories
        $tech = ProductCategory::create([
            'name' => 'Tech Accessories',
            'slug' => 'tech-accessories',
            'description' => 'Electronic accessories and gadgets'
        ]);

        $powerBanks = ProductSubcategory::create([
            'name' => 'Power Banks',
            'slug' => 'power-banks',
            'category_id' => $tech->id,
            'description' => 'Portable battery chargers'
        ]);
    }

    private function attachGroupsToSubcategories()
    {
        // Obtener modelos
        $tshirts = ProductSubcategory::where('slug', 't-shirts')->first();
        $hoodies = ProductSubcategory::where('slug', 'hoodies')->first();
        $mugs = ProductSubcategory::where('slug', 'mugs')->first();
        $bottles = ProductSubcategory::where('slug', 'water-bottles')->first();
        $powerBanks = ProductSubcategory::where('slug', 'power-banks')->first();

        $material = AttributeGroup::where('name', 'material')->first();
        $dimensions = AttributeGroup::where('name', 'dimensions')->first();
        $printing = AttributeGroup::where('name', 'printing')->first();
        $packaging = AttributeGroup::where('name', 'packaging')->first();
        $electrical = AttributeGroup::where('name', 'electrical')->first();

        // T-Shirts: Material + Dimensiones + Impresión
        $tshirts->attributeGroups()->attach([
            $material->id => [
                'required' => true,
                'overrides' => [
                    'type' => ['options' => ['cotton', 'polyester', 'cotton_poly_blend']]
                ]
            ],
            $dimensions->id => [
                'required' => true, 
                'overrides' => ['height' => ['required' => false]]
            ],
            $printing->id => ['required' => true]
        ]);

        // Hoodies: Material + Dimensiones + Impresión
        $hoodies->attributeGroups()->attach([
            $material->id => [
                'required' => true,
                'overrides' => [
                    'type' => ['options' => ['cotton_fleece', 'polyester_fleece', 'cotton_poly_blend']]
                ]
            ],
            $dimensions->id => ['required' => true],
            $printing->id => ['required' => true]
        ]);

        // Mugs: Material + Dimensiones + Impresión + Packaging
        $mugs->attributeGroups()->attach([
            $material->id => [
                'required' => true,
                'overrides' => [
                    'type' => ['options' => ['ceramic', 'stainless_steel', 'glass']]
                ]
            ],
            $dimensions->id => ['required' => true],
            $printing->id => ['required' => true],
            $packaging->id => ['required' => true]
        ]);

        // Water Bottles: Material + Dimensiones + Impresión + Packaging
        $bottles->attributeGroups()->attach([
            $material->id => [
                'required' => true,
                'overrides' => [
                    'type' => ['options' => ['stainless_steel', 'bpa_free_plastic', 'glass', 'aluminum']]
                ]
            ],
            $dimensions->id => ['required' => true],
            $printing->id => ['required' => true],
            $packaging->id => ['required' => true]
        ]);

        // Power Banks: Dimensiones + Eléctrico + Packaging
        $powerBanks->attributeGroups()->attach([
            $dimensions->id => ['required' => true],
            $electrical->id => ['required' => true],
            $packaging->id => ['required' => true]
        ]);
    }
}
```

### 7.2 Seeder de Productos de Ejemplo

```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ProductItem;
use App\Models\ProductSubcategory;

class ProductItemSeeder extends Seeder
{
    public function run()
    {
        $this->createSampleProducts();
    }

    private function createSampleProducts()
    {
        $tshirts = ProductSubcategory::where('slug', 't-shirts')->first();
        $mugs = ProductSubcategory::where('slug', 'mugs')->first();
        $powerBanks = ProductSubcategory::where('slug', 'power-banks')->first();

        // T-Shirts de ejemplo
        ProductItem::create([
            'name' => 'Classic Cotton Corporate Tee',
            'subcategory_id' => $tshirts->id,
            'quantity' => 500,
            'specifications' => [
                'material' => [
                    'type' => 'cotton',
                    'composition' => '100% Ring Spun Cotton',
                    'certifications' => ['oeko_tex']
                ],
                'dimensions' => [
                    'length' => 72,
                    'width' => 54,
                    'weight' => 180
                ],
                'printing' => [
                    'max_width' => 25,
                    'max_height' => 30,
                    'techniques' => ['screen_print', 'embroidery'],
                    'max_colors' => 4,
                    'area' => [
                        'location' => 'front_center'
                    ]
                ]
            ],
            'status' => ProductStatus::Draft
        ]);

        // Mugs de ejemplo
        ProductItem::create([
            'name' => 'Executive Ceramic Mug',
            'subcategory_id' => $mugs->id,
            'quantity' => 200,
            'specifications' => [
                'material' => [
                    'type' => 'ceramic',
                    'certifications' => ['fda_approved']
                ],
                'dimensions' => [
                    'length' => 12,
                    'width' => 9,
                    'height' => 9,
                    'weight' => 350
                ],
                'printing' => [
                    'max_width' => 8,
                    'max_height' => 8,
                    'techniques' => ['digital_print', 'laser_engraving'],
                    'max_colors' => 4,
                    'area' => [
                        'location' => 'front_center'
                    ]
                ],
                'packaging' => [
                    'individual_packaging' => 'box',
                    'units_per_carton' => 36,
                    'carton_weight' => 15,
                    'carton_dimensions' => [
                        'length' => 40,
                        'width' => 30,
                        'height' => 25
                    ]
                ]
            ],
            'status' => ProductStatus::Sourcing
        ]);

        // Power Bank de ejemplo
        ProductItem::create([
            'name' => 'Wireless Power Bank 10000mAh',
            'subcategory_id' => $powerBanks->id,
            'quantity' => 100,
            'specifications' => [
                'dimensions' => [
                    'length' => 15,
                    'width' => 7,
                    'height' => 2,
                    'weight' => 250
                ],
                'electrical' => [
                    'capacity_mah' => 10000,
                    'input_voltage' => '5V',
                    'output_ports' => ['USB-A', 'USB-C', 'Wireless'],
                    'wireless_charging' => true
                ],
                'packaging' => [
                    'individual_packaging' => 'box',
                    'units_per_carton' => 20,
                    'carton_weight' => 6,
                    'carton_dimensions' => [
                        'length' => 35,
                        'width' => 25,
                        'height' => 15
                    ]
                ]
            ],
            'status' => ProductStatus::Quoted
        ]);
    }
}
```

---

## 8. Testing

### 8.1 Feature Test

```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\ProductItem;
use App\Models\ProductSubcategory;
use App\Models\AttributeGroup;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductSpecificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_product_item_can_be_created_with_grouped_specifications()
    {
        $subcategory = ProductSubcategory::where('slug', 't-shirts')->first();
        
        $productData = [
            'name' => 'Test T-Shirt',
            'subcategory_id' => $subcategory->id,
            'quantity' => 100,
            'specifications' => [
                'material' => [
                    'type' => 'cotton',
                    'composition' => '100% Cotton'
                ],
                'dimensions' => [
                    'length' => 70,
                    'width' => 50,
                    'weight' => 180
                ],
                'printing' => [
                    'max_width' => 25,
                    'max_height' => 30,
                    'techniques' => ['screen_print']
                ]
            ]
        ];

        $product = ProductItem::create($productData);

        $this->assertDatabaseHas('product_items', [
            'name' => 'Test T-Shirt',
            'subcategory_id' => $subcategory->id
        ]);

        $this->assertEquals('cotton', $product->specifications['material']['type']);
        $this->assertEquals(180, $product->specifications['dimensions']['weight']);
    }

    public function test_validation_fails_for_missing_required_specifications()
    {
        $subcategory = ProductSubcategory::where('slug', 't-shirts')->first();
        
        $this->expectException(\Illuminate\Validation\ValidationException::class);
        
        ProductItem::create([
            'name' => 'Invalid T-Shirt',
            'subcategory_id' => $subcategory->id,
            'specifications' => [
                'material' => [
                    // Falta el campo requerido 'type'
                    'composition' => '100% Cotton'
                ]
            ]
        ]);
    }

    public function test_formatted_specifications_returns_correct_structure()
    {
        $product = ProductItem::factory()->create();
        $formatted = $product->getFormattedSpecifications();

        $this->assertIsArray($formatted);
        
        foreach ($formatted as $groupName => $groupData) {
            $this->assertArrayHasKey('label', $groupData);
            $this->assertArrayHasKey('values', $groupData);
            $this->assertIsArray($groupData['values']);
        }
    }

    public function test_subcategory_compiles_schema_correctly()
    {
        $subcategory = ProductSubcategory::where('slug', 't-shirts')->first();
        $compiledSchema = $subcategory->getCompiledSchema();

        $this->assertIsArray($compiledSchema);
        $this->assertArrayHasKey('material_type', $compiledSchema);
        $this->assertArrayHasKey('dimensions_length', $compiledSchema);
        $this->assertArrayHasKey('printing_max_width', $compiledSchema);
    }

    public function test_attribute_group_applies_overrides_correctly()
    {
        $subcategory = ProductSubcategory::where('slug', 't-shirts')->first();
        $materialGroup = AttributeGroup::where('name', 'material')->first();
        
        $schemaForSubcategory = $materialGroup->getSchemaForSubcategory($subcategory);
        
        // Verificar que los overrides se aplicaron
        $this->assertEquals(['cotton', 'polyester', 'cotton_poly_blend'], 
            $schemaForSubcategory['type']['options']);
    }
}
```

### 8.2 Factory

```php
<?php

namespace Database\Factories;

use App\Models\ProductItem;
use App\Models\ProductSubcategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductItemFactory extends Factory
{
    protected $model = ProductItem::class;

    public function definition()
    {
        $subcategory = ProductSubcategory::inRandomOrder()->first() 
            ?? ProductSubcategory::factory()->create();
        
        return [
            'name' => $this->faker->words(3, true),
            'subcategory_id' => $subcategory->id,
            'quantity' => $this->faker->numberBetween(50, 1000),
            'specifications' => $this->generateSpecificationsForSubcategory($subcategory),
            'status' => $this->faker->randomElement([ProductStatus::Draft, ProductStatus::Sourcing, ProductStatus::Quoted, ProductStatus::Confirmed])
        ];
    }

    private function generateSpecificationsForSubcategory(ProductSubcategory $subcategory): array
    {
        $specifications = [];
        $groupedSchema = $subcategory->getGroupedSchema();

        foreach ($groupedSchema as $groupName => $groupConfig) {
            $groupSpecs = [];
            
            foreach ($groupConfig['fields'] as $fieldName => $fieldConfig) {
                if ($fieldConfig['required'] || $this->faker->boolean(70)) {
                    $groupSpecs[$fieldName] = $this->generateValueForField($fieldConfig);
                }
            }
            
            if (!empty($groupSpecs)) {
                $specifications[$groupName] = $groupSpecs;
            }
        }

        return $specifications;
    }

    private function generateValueForField(array $config)
    {
        return match($config['type']) {
            'enum' => ($config['multiple'] ?? false) 
                ? $this->faker->randomElements($config['options'], $this->faker->numberBetween(1, 3))
                : $this->faker->randomElement($config['options']),
            'integer' => $this->faker->numberBetween(
                $config['min'] ?? 1, 
                $config['max'] ?? 1000
            ),
            'string' => $this->faker->sentence(3),
            'boolean' => $this->faker->boolean(),
            'array' => $this->faker->words($this->faker->numberBetween(1, 5)),
            'object' => $this->generateObjectValue($config['properties'] ?? []),
            default => $this->faker->word()
        };
    }

    private function generateObjectValue(array $properties): array
    {
        $object = [];
        
        foreach ($properties as $prop => $propConfig) {
            $object[$prop] = $this->generateValueForField($propConfig);
        }
        
        return $object;
    }
}
```

---

## 9. Beneficios de la Implementación

### 9.1 Ventajas Técnicas

✅ **Flexibilidad Controlada**
- Nuevas categorías sin cambios de código
- Especificaciones dinámicas por tipo de producto
- Override específico por subcategoría

✅ **Performance Optimizada**  
- Una query para especificaciones completas
- JSON storage eficiente
- Eager loading de relaciones

✅ **Validación Robusta**
- Validación dinámica automática
- Type safety con Value Objects
- Cross-field validation

✅ **UI Dinámica Rica**
- Formularios que se adaptan automáticamente
- Campos condicionales
- Preview en tiempo real

✅ **Mantenibilidad**
- Schema centralizado por grupos
- Cambios se propagan automáticamente
- Testing automatizado

### 9.2 Ventajas de Negocio

✅ **Time to Market**
- Implementación rápida vs. alternativas complejas
- Validación de modelo de negocio inmediata

✅ **Escalabilidad**
- Sistema preparado para crecimiento
- Migración futura planificada si es necesario

✅ **Experiencia de Usuario**
- Formularios organizados lógicamente
- Validación inmediata
- Interfaz consistente

✅ **Operación Eficiente**
- Menos errores de especificaciones
- Proceso de creación más rápido
- Reporting unificado

### 9.3 ROI Estimado

**Inversión inicial**: 4-6 semanas de desarrollo
**Ahorros esperados**:
- -60% tiempo creación de productos
- -80% tiempo agregando nuevas categorías  
- -70% errores de validación
- +100% flexibilidad del sistema

---

## 10. Roadmap Futuro

### 10.1 Fase 2: Optimizaciones (3-6 meses)

- **AI-Assisted Specification Extraction**: Generar especificaciones desde descripciones de texto
- **Bulk Operations**: Importación masiva de productos con validación
- **Advanced Validation**: Cross-field dependencies más complejas
- **Template System**: Plantillas de especificaciones por cliente

### 10.2 Fase 3: Escalamiento (6-12 meses)

- **Nested Set Migration**: Si la jerarquía crece > 2 niveles
- **Multi-tenant Schemas**: Diferentes esquemas por cliente
- **Version Management**: Versionado de especificaciones
- **Advanced Analytics**: Reporting cross-category

### 10.3 Integración con Otras Features

- **Sourcing**: AI sugiere proveedores según especificaciones
- **Pricing**: Estimación automática de costos
- **Quality Control**: Validación automática con proveedores
- **Customer Portal**: Self-service para clientes

---

Esta implementación proporciona una **base sólida y extensible** para el sistema de productos de PromoSmart, balanceando **simplicidad inicial** con **capacidad de crecimiento futuro**, siguiendo las mejores prácticas de Laravel y aprovechando completamente las capacidades de Filament para UI dinámica.