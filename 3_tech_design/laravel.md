# Guía de Arquitectura y Patrones de Diseño Laravel

**Propósito:** Este documento define la arquitectura de software y los patrones de diseño estándar que se utilizarán en la implementación del sistema PromoSmart sobre el framework Laravel. El propósito de esta guía es asegurar la construcción de una base de código coherente, mantenible, testeable y que aproveche las fortalezas del ecosistema Laravel.

---

### Filosofía Arquitectónica

La arquitectura seguirá los principios de la filosofía de Laravel: código **elegante, expresivo y pragmático** que prioriza la **experiencia del desarrollador (Developer Experience)**. Se favorecerán los patrones que mejoren la claridad y reduzcan la complejidad, evitando la sobre-ingeniería y enfocándose en soluciones robustas que resuelvan problemas de negocio concretos.

---

### 1. Principio Central: Diseño Orientado a Comportamientos

Las entidades del sistema se definirán por sus comportamientos y capacidades, no por una categorización estricta. Este principio de **Composición sobre Herencia** se implementará de la siguiente manera:

*   **Implementación:** Los comportamientos específicos como `GeneratesDocuments`, `SendableDocument`, o `ApprovableDocument` se implementarán como **Traits** de PHP. Las **Interfaces** de PHP (`Contracts`) se usarán para garantizar que las clases cumplan con un contrato de comportamiento específico.
*   **Beneficio:** Se obtiene un código más flexible, reutilizable y fácil de entender, evitando jerarquías de clases rígidas.

### 2. Autorización y Control de Acceso (Gates & Policies)

*   **Desafío:** La matriz de permisos del sistema es compleja. La lógica para determinar si un usuario puede realizar una acción no debe estar dispersa por el código (ej. en controladores o vistas).
*   **Patrón Seleccionado:** Una sinergia de tres capas para una autorización clara y centralizada.
    1.  **Capa de Dominio (Nuestros Traits):** Definen las **acciones** posibles en el sistema (el **QUÉ** se puede hacer, ej. el método `approve()`).
    2.  **Capa de Permisos (Backend):** El paquete `spatie/laravel-permission` gestionará los roles y permisos (las "llaves"). Define **QUIÉN** tiene permiso para qué acción.
    3.  **Capa de Presentación (UI):** El paquete `filament/filament-shield` conectará los permisos a la interfaz de Filament, **mostrando u ocultando** automáticamente los botones y accesos según el rol del usuario.
*   **Beneficio:** Se obtiene un sistema de permisos extremadamente robusto, centralizado y fácil de mantener, donde la interfaz reacciona automáticamente a la lógica de negocio sin `if/else` en el código de la vista.

### 3. Gestión de Procesos y Flujos de Trabajo

*   **Desafío:** El ciclo de vida de entidades como `ProductItem` es complejo y sigue reglas de transición estrictas.
*   **Patrón Seleccionado:** **Máquina de Estados (State Machine)**, implementada a través de un paquete estándar de la comunidad como `spatie/laravel-model-states`.
*   **Beneficio:** Encapsula la lógica de los estados y transiciones en clases dedicadas, previniendo estados inválidos y haciendo el flujo de trabajo explícito y seguro.

### 4. Taxonomía y Especificaciones de Productos

*   **Desafío:** Gestionar una jerarquía de categorías y atributos de producto que son dinámicos, reutilizables y consistentes para cada subcategoría.
*   **Patrón Seleccionado:** Un **modelo de composición de atributos** que balancea flexibilidad, reutilización y simplicidad.
    1.  **Grupos de Atributos Reutilizables (`AttributeGroup`):** Se definen bloques de atributos comunes (ej. "Dimensiones Físicas", "Detalles de Impresión") como modelos centrales. Cada grupo contiene su propio esquema de atributos en una columna JSON.
    2.  **Composición por Subcategoría:** Una `ProductSubcategory` **compone** su plantilla de especificaciones mediante una relación `belongsToMany` con los `AttributeGroup` que necesita. Esto permite reutilizar la definición de "Dimensiones" en múltiples subcategorías.
    3.  **Almacenamiento de Valores en `ProductItem`:** Los valores concretos para un producto se guardan en una **única columna JSON (`specifications`)** en la tabla `product_items`. Este JSON se estructura internamente por los grupos de atributos (ej. `{"dimensions": {"largo": 10, ...}, "material": {...}}`).
*   **Beneficio:** Este enfoque es superior a las alternativas. Evita la duplicación de datos del JSON simple y la complejidad masiva de un modelo EAV, logrando reutilización (DRY), consistencia y una implementación rápida.

### 5. Validación de Datos

*   **Desafío:** Manejar reglas de validación complejas y condicionales que dependen del esquema compilado de la subcategoría.
*   **Patrón Seleccionado:**
    1.  **Custom Form Requests:** Toda la lógica de validación para las peticiones `POST`, `PUT`, `PATCH` se encapsulará en clases `FormRequest` dedicadas. Estas clases construirán dinámicamente las reglas de validación basadas en los `AttributeGroups` de la subcategoría.
    2.  **Custom Validation Rules:** La lógica de validación que sea compleja o reutilizable se extraerá a sus propias clases de `Rule`.
*   **Beneficio:** Centraliza y organiza la lógica de validación, haciéndola reutilizable, testeable y manteniendo los controladores limpios.

### 6. Lógica de Negocio y Presentación de Datos

*   **Desafío:** Encapsular la lógica de negocio y controlar la información que se expone en diferentes contextos (vistas, APIs).
*   **Patrones Seleccionados:**
    1.  **Value Objects con Custom Casts:** Para conceptos como `Money` o `ProductSpecifications`. Eloquent los manejará como objetos nativos, mejorando la seguridad y expresividad del código.
    2.  **Service Classes:** Para orquestar lógica de negocio compleja que no pertenece a un modelo (ej. `PricingService` para calcular la rentabilidad).
    3.  **Query Scopes:** Para encapsular la lógica de consulta de la base de datos, especialmente la que depende de roles (ej. `Project::forUser($user)`).
    4.  **API Resources:** Para transformar los modelos de Eloquent a respuestas JSON, asegurando que solo se expongan los datos necesarios para cada rol o contexto.

### 7. Tareas Asíncronas y Búsqueda

*   **Desafío:** Manejar tareas lentas sin afectar la experiencia del usuario y proveer una búsqueda global eficiente.
*   **Patrones Seleccionados:**
    1.  **Queues & Jobs:** Todas las tareas que puedan tardar (generación de PDFs, envío de emails, llamadas a APIs externas) se ejecutarán en segundo plano a través del sistema de colas de Laravel.
    2.  **Laravel Scout:** Para la funcionalidad de búsqueda global (`RF-13`). Provee una abstracción simple sobre motores de búsqueda potentes (como Meilisearch o Algolia) para una experiencia de búsqueda rápida y relevante.

### 8. Ciclo de Vida y Persistencia de Datos

*   **Desafío:** Asegurar la integridad de los datos a lo largo del tiempo, previniendo la pérdida de información y manteniendo un registro claro de la historia de cada entidad.
*   **Patrones Seleccionados:**
    1.  **Historial de Eventos (Activity Log):** Para un registro cronológico y legible de "quién hizo qué y cuándo", se usará un paquete estándar como `spatie/laravel-activitylog`. Esto responde a la pregunta "¿Qué ha pasado?".
    2.  **Versionado de Estado (Snapshots):** Para momentos críticos de negocio (ej. creación de la `baseline`), se guardará un snapshot JSON del estado completo de la entidad. Esto responde a la pregunta "¿Cómo era esta entidad en un momento específico?".
    3.  **Persistencia con Soft Deletes:** Se usará el trait `SoftDeletes` en todos los modelos de negocio principales para prevenir la eliminación permanente de datos, asegurando la integridad referencial de los registros antiguos.
*   **Beneficio:** Se obtiene una trazabilidad completa y un ciclo de vida de datos robusto. El historial provee la narrativa, el versionado la evidencia, y los soft deletes la recuperabilidad.

### 9. Gestión de la Configuración

*   **Desafío:** Administrar parámetros y constantes del negocio (ej. márgenes por defecto, umbrales de alerta) de forma centralizada y bajo control de versiones.
*   **Patrón Seleccionado:** Crear **archivos de configuración personalizados** en el directorio `config/` (ej. `config/promosmart.php`).
*   **Beneficio:** Separa la configuración de la aplicación (que puede ser versionada en Git) de la configuración del entorno (que vive en `.env`). Provee una única fuente de verdad para las constantes del negocio.

### 10. Organización de Código y Escalabilidad

*   **Desafío:** A medida que la aplicación crece, la estructura de carpetas por defecto de Laravel puede volverse caótica.
*   **Patrón Seleccionado:** Organizar la estructura de la carpeta `app/` por **dominios de negocio** en lugar de por tipo de clase. Por ejemplo:
    ```
    app/
    ├───Domain/
    │   ├───Projects/
    │   │   ├───Actions/
    │   │   ├───Models/
    │   │   │   └── Project.php
    │   │   └── Policies/
    │   ├───Products/
    │   └───Users/
    └───Support/
    ```
*   **Beneficio:** Facilita la escalabilidad y el mantenimiento a largo plazo, ya que todo el código relacionado con un concepto de negocio se encuentra agrupado. Mejora la claridad y la incorporación de nuevos desarrolladores.

### 11. Integraciones con Servicios Externos

*   **Desafío:** El sistema necesita integrarse con múltiples APIs externas (tasas de cambio, logística, etc.). Acoplar la lógica de estas llamadas directamente en el código es frágil y difícil de probar.
*   **Patrón Seleccionado:** Crear una **clase de servicio dedicada (Cliente de API)** para cada integración externa.
*   **Implementación:** Una clase como `ShippingTrackerClient` encapsulará toda la lógica para comunicarse con la API de seguimiento. Esta clase se registrará en el Service Container y se inyectará donde sea necesaria.
*   **Beneficio:** Aísla las dependencias externas. Si la API de un proveedor cambia, solo se debe modificar una clase. Además, facilita enormemente las pruebas al permitir hacer "mocks" del cliente de API.