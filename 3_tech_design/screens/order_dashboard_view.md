# Specification: Order Dashboard View

**Objective:** To provide a single, comprehensive screen where an internal user (e.g., Sales Analyst, Procurement Analyst) can understand the real-time status of a Sales Order's schedule and profitability.

---

## 1. General Layout

The view will be titled with the Sales Order ID (e.g., "Sales Order #SO12345").

It will feature two primary panels arranged side-by-side or stacked vertically, depending on screen size:
- **Panel A: Order Schedule**
- **Panel B: Order Profitability**

A high-level status banner should be displayed at the top, summarizing the overall order status.

```
+-----------------------------------------------------------------+
| Sales Order #SO12345   [ Status: At Risk ]                      |
+-----------------------------------------------------------------+
| [ Panel A: Order Schedule ]      | [ Panel B: Order Profitability ] |
|                                  |                                  |
|                                  |                                  |
+-----------------------------------------------------------------+
```

---

## 2. Panel A: Order Schedule

**Source Entity:** `OrderSchedule`

**Objective:** To clearly communicate whether the order is on time and show the status of all critical milestones.

### 2.1. Header

- **Overall Status:** A prominent badge showing the `status` field (e.g., "On Track", "Delayed", "At Risk").
- **Key Date:** Displays the `final_delivery_planned_at` date, clearly labeled "Planned Delivery".

### 2.2. Milestone List

This section will display a list of all key milestones from the `OrderSchedule` entity.

- Each row represents one milestone.
- It must be easily scannable, using colors to draw attention to issues.

**Visual Mockup:**

```
+----------------------------------------------------------------------+
| Order Schedule   [ Status: At Risk ⚠️ ]                               |
| Planned Delivery: 2025-10-15                                         |
+----------------------------------------------------------------------+
| Milestone                     | Planned Date | Actual Date  | Status  |
|-------------------------------|--------------|--------------|---------|
| Virtual Mock-up Approval      | 2025-08-20   | 2025-08-21   | ✅ Done   |
| Pre-production Sample Approval| 2025-09-05   | 2025-09-10   | ⚠️ Late   |
| Supplier PO Issuance          | 2025-09-12   | 2025-09-12   | ✅ Done   |
| Production Completion         | 2025-09-30   | -            | ⏳ Pending |
| Goods Shipment                | 2025-10-02   | -            | ⏳ Pending |
| Final Delivery                | 2025-10-15   | -            | ⏳ Pending |
+----------------------------------------------------------------------+
```

- **Status Logic:**
    - `✅ Done`: `actual_at` is not null.
    - `⚠️ Late`: `actual_at` is null AND `planned_at` is in the past.
    - `⏳ Pending`: `actual_at` is null AND `planned_at` is in the future.

---

## 3. Panel B: Order Profitability

**Source Entity:** `OrderProfitability`

**Objective:** To provide a clear, real-time view of the order's financial health and highlight any margin erosion.

### 3.1. Header

- **Overall Margin:** A large, prominent display of the `realized_margin_percentage`. The color should change based on thresholds (e.g., green > 20%, amber 15-20%, red < 15%).
- **Status:** A badge for the `status` field (e.g., "Healthy", "Under Review").

### 3.2. Financial Breakdown

This section will provide a clear breakdown of revenue and costs.

**Visual Mockup:**

```
+----------------------------------------------------------------------+
| Order Profitability   [ Margin: 18.5% 🟠 ]                           |
+----------------------------------------------------------------------+
| Total Revenue:                            $10,000.00                 |
| Total Costs:                              $8,150.00                  |
|----------------------------------------------------------------------|
| COST BREAKDOWN                                                       |
|                                 | Estimated    | Actual       | Diff. |
|---------------------------------|--------------|--------------|-------|
| Cost of Goods (COGS)            | $6,000.00    | $6,000.00    | $0.00 |
| Logistics                       | $1,000.00    | $1,200.00    | +$200 |
| Duties & Taxes                  | $500.00      | $450.00      | -$50  |
| Unplanned Costs                 | -            | $500.00      | +$500 |
+----------------------------------------------------------------------+
```

- **Difference Logic:** The "Diff." column shows the variance between `actual` and `estimated` costs. Positive numbers are bad (over budget), negative numbers are good (under budget). This column should use color to highlight significant variances.
