# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a **documentation repository** for PromoSmart, a promotional products management system. It contains comprehensive business and technical documentation organized in a structured manner. This is NOT a code repository - it contains only Markdown documentation files.

## Project Architecture

PromoSmart follows a **CRUD + Version Tracking architecture** built on the **TALL stack** (Tailwind CSS, Alpine.js, Laravelq, Livewire) with **Filament v4** for admin interfaces.

### Key Architectural Decisions
- **Simplified CRUD approach** instead of Event Sourcing for performance and maintainability
- **TALL stack adoption** for unified development experience (see `2_architecture/adrs/adr004_adopcion_tall_stack_filament.md`)
- **PostgreSQL database** for robust data handling
- **Version tracking** for significant business changes (re-quotes, specifications updates)

### Core Business Entities
- **Project**: Container for client orders (e.g., "Summer Campaign 2024 - BeerCO")
- **ProductItem**: Individual products within projects with independent lifecycles
- **Customer**: Companies requesting promotional products
- **Supplier**: Manufacturers (typically overseas)
- **ImportShipmentRecord**: Groups products for logistics optimization

## Documentation Structure

```
1_overview/          # Business documentation (stakeholder-friendly)
├── Vision, Objectives, Business Model
├── Product Types, System Requirements
├── Roles, Permissions, Workflows
└── Reporting and Analytics

2_architecture/      # Technical documentation (developer-focused)
├── Integrated Architecture, Solution Strategy
├── Building Blocks, Runtime Views
├── Cross-cutting Concerns, Quality Requirements
└── adrs/           # Architecture Decision Records

3_tech_design/       # Implementation guidance
├── Data migration plans
├── patterns/       # Implementation patterns (Actions, Services, etc.)
├── screens/        # UI specifications
└── specifications/ # Layer-by-layer implementation guides
```

## Business Process Flow

The system revolves around the **ProductItem lifecycle**:

1. **Sales & Quotation**: Draft → Sourcing → Quoted to Client
2. **Production Preparation**: Virtual Mockup Approval → Physical Sample Approval → Ready for Production
3. **Fulfillment & Delivery**: In Production → In Transit → Delivered

**Critical milestone**: The **Commitment Milestone** activates when receiving the Client Purchase Order (CPO), creating an immutable baseline for financial and schedule tracking.

## Key Documentation Files

### Business Understanding
- `1_overview/03_Modelo_de_Negocio.md` - Core business model and entity lifecycles
- `1_overview/04_Tipos_de_Productos.md` - Product classification and variants
- `1_overview/05_Requisitos_del_Sistema.md` - Functional requirements with user stories

### Technical Implementation
- `2_architecture/00_arquitectura_integrada.md` - Complete architectural overview
- `2_architecture/13_domain_to_code_mapping.md` - Business-to-code mapping
- `3_tech_design/specifications/guia_de_implementacion_por_capas_para_filament.md` - Filament implementation guide

### Pending Documentation Tasks
See `instrucciones.md` for detailed completion tasks including:
- Missing functional requirements (RF-20 to RF-24)
- Reports and Analytics documentation
- Document reorganization between overview and architecture sections

## Working with This Repository

Since this is a documentation repository:
- **No build/test/lint commands** - only Markdown files
- **Focus on content accuracy** and cross-references between documents
- **Maintain separation** between business (overview) and technical (architecture) documentation
- **Use Spanish** for all documentation content (established convention)
- **Follow existing format** and structure when creating new documents

## Development Context

When implementing the actual PromoSmart system:
- Use **Laravel 12+** with **Filament v4**
- Implement **Action + DTO pattern** for business logic
- Use **Enums with methods** for business rules and UI logic
- Apply **simplified service layer** for cross-domain operations
- Implement **selective version tracking** only for significant business changes

## Document Maintenance

- Keep business documentation (1_overview/) accessible to non-technical stakeholders
- Maintain technical details in architecture section (2_architecture/)
- Update cross-references when moving or renaming documents
- Follow the anti-over-engineering principle emphasized throughout the documentation